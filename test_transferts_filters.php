<?php
/**
 * Test des filtres de l'API transferts.php
 */

echo "TEST DES FILTRES API TRANSFERTS\n";
echo "===============================\n\n";

require_once 'api/config.php';

// Test 1: Filtre par type
echo "1. Test filtre par type 'national'...\n";
$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET = ['page' => '1', 'limit' => '10', 'type' => 'national'];

ob_start();
try {
    include 'api/transferts.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ Filtre type OK - " . count($data['data']) . " résultats\n";
        if (!empty($data['data'])) {
            echo "   Premier résultat type: " . $data['data'][0]['type'] . "\n";
        }
    } else {
        echo "❌ Erreur filtre type: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception filtre type: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Filtre par statut
echo "2. Test filtre par statut 'en_attente'...\n";
$_GET = ['page' => '1', 'limit' => '10', 'statut' => 'en_attente'];

ob_start();
try {
    include 'api/transferts.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ Filtre statut OK - " . count($data['data']) . " résultats\n";
        if (!empty($data['data'])) {
            echo "   Premier résultat statut: " . $data['data'][0]['statut'] . "\n";
        }
    } else {
        echo "❌ Erreur filtre statut: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception filtre statut: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Filtre par recherche
echo "3. Test filtre par recherche 'TRF'...\n";
$_GET = ['page' => '1', 'limit' => '10', 'search' => 'TRF'];

ob_start();
try {
    include 'api/transferts.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ Filtre recherche OK - " . count($data['data']) . " résultats\n";
        if (!empty($data['data'])) {
            echo "   Premier résultat référence: " . $data['data'][0]['reference'] . "\n";
        }
    } else {
        echo "❌ Erreur filtre recherche: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception filtre recherche: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Filtres combinés
echo "4. Test filtres combinés (type + statut)...\n";
$_GET = ['page' => '1', 'limit' => '10', 'type' => 'national', 'statut' => 'en_attente'];

ob_start();
try {
    include 'api/transferts.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ Filtres combinés OK - " . count($data['data']) . " résultats\n";
        if (!empty($data['data'])) {
            echo "   Type: " . $data['data'][0]['type'] . ", Statut: " . $data['data'][0]['statut'] . "\n";
        }
    } else {
        echo "❌ Erreur filtres combinés: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception filtres combinés: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Test avec dates
echo "5. Test filtre par dates...\n";
$_GET = ['page' => '1', 'limit' => '10', 'date_debut' => '2025-07-01', 'date_fin' => '2025-07-31'];

ob_start();
try {
    include 'api/transferts.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ Filtre dates OK - " . count($data['data']) . " résultats\n";
        if (!empty($data['data'])) {
            echo "   Date transfert: " . $data['data'][0]['date_transfert'] . "\n";
        }
    } else {
        echo "❌ Erreur filtre dates: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception filtre dates: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
