<?php
/**
 * Script de migration pour ajouter les champs de commission différenciés
 * Exécute la migration 09_add_commission_fields.sql
 */

require_once 'config.php';

try {
    echo "=== MIGRATION DES CHAMPS DE COMMISSION ===\n";
    echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

    // Commencer une transaction
    $pdo->beginTransaction();

    echo "1. Vérification des colonnes existantes...\n";

    // Vérifier et ajouter les colonnes une par une
    $columns_to_add = [
        'commission_envoi_fixe' => 'DECIMAL(10,2) DEFAULT NULL COMMENT "Commission fixe pour les envois"',
        'commission_envoi_pourcentage' => 'DECIMAL(5,2) DEFAULT NULL COMMENT "Commission pourcentage pour les envois"',
        'commission_retrait_fixe' => 'DECIMAL(10,2) DEFAULT NULL COMMENT "Commission fixe pour les retraits"',
        'commission_retrait_pourcentage' => 'DECIMAL(5,2) DEFAULT NULL COMMENT "Commission pourcentage pour les retraits"'
    ];

    $executed_count = 0;

    foreach ($columns_to_add as $column_name => $column_definition) {
        try {
            // Vérifier si la colonne existe
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'services_transfert'
                AND COLUMN_NAME = ?
            ");
            $stmt->execute([$column_name]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['count'] == 0) {
                // Ajouter la colonne
                $alter_sql = "ALTER TABLE services_transfert ADD COLUMN $column_name $column_definition";
                echo "   Ajout de la colonne '$column_name'...\n";

                $pdo->exec($alter_sql);
                echo "     ✅ Colonne '$column_name' ajoutée avec succès\n";
                $executed_count++;
            } else {
                echo "   ⚠️  Colonne '$column_name' déjà existante\n";
            }

        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "   ⚠️  Colonne '$column_name' déjà existante (ignoré)\n";
            } else {
                echo "   ❌ Erreur lors de l'ajout de '$column_name': " . $e->getMessage() . "\n";
                throw $e;
            }
        }
    }

    echo "\n2. Mise à jour des services existants...\n";

    // Mettre à jour les services existants avec des valeurs par défaut
    $update_sql = "
        UPDATE services_transfert
        SET
            commission_envoi_fixe = COALESCE(commission_fixe, 0),
            commission_envoi_pourcentage = COALESCE(commission_pourcentage, 0),
            commission_retrait_fixe = COALESCE(commission_fixe * 0.8, 0),
            commission_retrait_pourcentage = COALESCE(commission_pourcentage * 0.8, 0)
        WHERE
            commission_envoi_fixe IS NULL
            OR commission_envoi_pourcentage IS NULL
            OR commission_retrait_fixe IS NULL
            OR commission_retrait_pourcentage IS NULL
    ";

    $pdo->exec($update_sql);
    echo "   ✅ Services mis à jour avec les nouvelles commissions\n";

    // Valider la transaction
    $pdo->commit();
    
    echo "\n=== RÉSULTATS DE LA MIGRATION ===\n";
    echo "Requêtes exécutées avec succès: $executed_count\n";
    
    // Vérifier que les colonnes ont été ajoutées
    echo "\n4. Vérification des colonnes ajoutées...\n";
    
    $check_columns = [
        'commission_envoi_fixe',
        'commission_envoi_pourcentage', 
        'commission_retrait_fixe',
        'commission_retrait_pourcentage'
    ];
    
    foreach ($check_columns as $column) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'services_transfert' 
            AND COLUMN_NAME = ?
        ");
        $stmt->execute([$column]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "   ✅ Colonne '$column' présente\n";
        } else {
            echo "   ❌ Colonne '$column' manquante\n";
        }
    }
    
    // Afficher un échantillon des données mises à jour
    echo "\n5. Échantillon des services mis à jour:\n";
    $stmt = $pdo->prepare("
        SELECT nom, commission_fixe, commission_pourcentage,
               commission_envoi_fixe, commission_envoi_pourcentage,
               commission_retrait_fixe, commission_retrait_pourcentage
        FROM services_transfert 
        LIMIT 3
    ");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($services as $service) {
        echo "   Service: {$service['nom']}\n";
        echo "     Envoi: {$service['commission_envoi_fixe']} FCFA + {$service['commission_envoi_pourcentage']}%\n";
        echo "     Retrait: {$service['commission_retrait_fixe']} FCFA + {$service['commission_retrait_pourcentage']}%\n";
        echo "\n";
    }
    
    echo "=== MIGRATION TERMINÉE AVEC SUCCÈS ===\n";
    
} catch (Exception $e) {
    // Annuler la transaction en cas d'erreur
    try {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
            echo "\nTransaction annulée.\n";
        }
    } catch (Exception $rollback_error) {
        echo "\nErreur lors de l'annulation de la transaction: " . $rollback_error->getMessage() . "\n";
    }

    echo "\n❌ ERREUR LORS DE LA MIGRATION:\n";
    echo $e->getMessage() . "\n";
    echo "\nLa migration a échoué.\n";
    exit(1);
}
?>
