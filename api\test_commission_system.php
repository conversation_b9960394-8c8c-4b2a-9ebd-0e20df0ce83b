<?php
/**
 * Script de test pour le système de commission différencié
 */

require_once 'config.php';
require_once 'services/transfer_service.php';

echo "=== TEST DU SYSTÈME DE COMMISSION DIFFÉRENCIÉ ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Initialiser le service de transfert
    $transferService = new TransferService($pdo);
    
    // Test 1: Vérifier les services en base
    echo "1. Services disponibles en base de données:\n";
    $stmt = $pdo->prepare("
        SELECT nom, commission_envoi_fixe, commission_envoi_pourcentage,
               commission_retrait_fixe, commission_retrait_pourcentage, actif
        FROM services_transfert
        WHERE actif = 1
        ORDER BY nom
    ");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($services)) {
        echo "   ❌ Aucun service actif trouvé\n";
        exit(1);
    }
    
    foreach ($services as $service) {
        echo "   ✅ {$service['nom']}\n";
        echo "      Envoi: {$service['commission_envoi_fixe']} FCFA + {$service['commission_envoi_pourcentage']}%\n";
        echo "      Retrait: {$service['commission_retrait_fixe']} FCFA + {$service['commission_retrait_pourcentage']}%\n";
        echo "\n";
    }
    
    // Test 2: Calcul des frais pour différents montants et opérations
    echo "2. Test de calcul des frais:\n";
    $test_amounts = [5000, 25000, 100000];
    $test_services = ['MIXX', 'FLOOZ'];
    $operations = ['envoi', 'retrait'];
    
    foreach ($test_services as $service_name) {
        echo "   Service: $service_name\n";
        
        foreach ($test_amounts as $amount) {
            echo "     Montant: " . number_format($amount, 0, ',', ' ') . " FCFA\n";
            
            foreach ($operations as $operation) {
                try {
                    $fees = $transferService->calculateFees($service_name, $amount, $operation);
                    $percentage = ($fees / $amount) * 100;
                    
                    echo "       $operation: " . number_format($fees, 0, ',', ' ') . " FCFA (" . 
                         number_format($percentage, 2) . "%)\n";
                    
                } catch (Exception $e) {
                    echo "       $operation: ❌ Erreur - " . $e->getMessage() . "\n";
                }
            }
            echo "\n";
        }
    }
    
    // Test 3: Comparaison avec l'ancien système
    echo "3. Comparaison avec l'ancien système (fallback):\n";
    
    // Désactiver temporairement tous les services pour tester le fallback
    $pdo->exec("UPDATE services_transfert SET actif = 0");
    
    $test_amount = 50000;
    echo "   Montant de test: " . number_format($test_amount, 0, ',', ' ') . " FCFA\n";
    
    try {
        $fees_legacy = $transferService->calculateFees('MIXX', $test_amount, 'envoi');
        echo "   Frais avec système legacy: " . number_format($fees_legacy, 0, ',', ' ') . " FCFA\n";
    } catch (Exception $e) {
        echo "   ❌ Erreur système legacy: " . $e->getMessage() . "\n";
    }
    
    // Réactiver les services
    $pdo->exec("UPDATE services_transfert SET actif = 1");
    
    // Test 4: Test de l'API quote
    echo "\n4. Test de l'API quote:\n";
    
    // Simuler une requête GET pour quote
    $_GET = [
        'service' => 'MIXX',
        'montant' => 25000,
        'devise_source' => 'XOF',
        'devise_destination' => 'XOF',
        'operation_type' => 'envoi'
    ];
    
    echo "   Paramètres: service=MIXX, montant=25000, operation_type=envoi\n";
    
    // Récupérer les informations du service
    $stmt = $pdo->prepare("SELECT * FROM services_transfert WHERE nom = ? AND actif = 1");
    $stmt->execute(['MIXX']);
    $service_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($service_info) {
        $service_nom = $service_info['nom'];
        $operation_type = $_GET['operation_type'] ?? 'envoi';
        $frais = $transferService->calculateFees($service_nom, $_GET['montant'], $operation_type);
        $taux_change = $transferService->getExchangeRate($_GET['devise_source'], $_GET['devise_destination']);
        $montant_a_recevoir = ($_GET['montant'] - $frais) * $taux_change;
        
        echo "   ✅ Frais calculés: " . number_format($frais, 0, ',', ' ') . " FCFA\n";
        echo "   ✅ Montant à recevoir: " . number_format($montant_a_recevoir, 0, ',', ' ') . " FCFA\n";
        echo "   ✅ Taux de change: $taux_change\n";
    } else {
        echo "   ❌ Service MIXX non trouvé\n";
    }
    
    // Test 5: Validation des paramètres
    echo "\n5. Test de validation des paramètres:\n";
    
    $invalid_tests = [
        ['service' => '', 'amount' => 1000, 'operation' => 'envoi', 'expected_error' => 'service vide'],
        ['service' => 'MIXX', 'amount' => 0, 'operation' => 'envoi', 'expected_error' => 'montant zéro'],
        ['service' => 'MIXX', 'amount' => -1000, 'operation' => 'envoi', 'expected_error' => 'montant négatif'],
        ['service' => 'MIXX', 'amount' => 1000, 'operation' => 'invalid', 'expected_error' => 'opération invalide'],
    ];
    
    foreach ($invalid_tests as $test) {
        try {
            $fees = $transferService->calculateFees($test['service'], $test['amount'], $test['operation']);
            echo "   ❌ Test '{$test['expected_error']}' devrait échouer mais a réussi\n";
        } catch (Exception $e) {
            echo "   ✅ Test '{$test['expected_error']}' a échoué comme attendu: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== TESTS TERMINÉS AVEC SUCCÈS ===\n";
    
} catch (Exception $e) {
    echo "\n❌ ERREUR LORS DES TESTS:\n";
    echo $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
