-- Migration pour ajouter les champs de commission différenciés
-- Date: 2025-07-30
-- Description: Ajoute les champs de commission séparés pour les envois et retraits

-- Vérifier si les colonnes existent déjà avant de les ajouter
SET @exist_commission_envoi_fixe = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'services_transfert' 
    AND COLUMN_NAME = 'commission_envoi_fixe'
);

SET @exist_commission_envoi_pourcentage = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'services_transfert' 
    AND COLUMN_NAME = 'commission_envoi_pourcentage'
);

SET @exist_commission_retrait_fixe = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'services_transfert' 
    AND COLUMN_NAME = 'commission_retrait_fixe'
);

SET @exist_commission_retrait_pourcentage = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'services_transfert' 
    AND COLUMN_NAME = 'commission_retrait_pourcentage'
);

-- Ajouter les colonnes si elles n'existent pas
SET @sql_add_commission_envoi_fixe = IF(@exist_commission_envoi_fixe = 0, 
    'ALTER TABLE services_transfert ADD COLUMN commission_envoi_fixe DECIMAL(10,2) DEFAULT NULL COMMENT "Commission fixe pour les envois" AFTER commission_pourcentage;', 
    'SELECT "Column commission_envoi_fixe already exists" as message;'
);

SET @sql_add_commission_envoi_pourcentage = IF(@exist_commission_envoi_pourcentage = 0, 
    'ALTER TABLE services_transfert ADD COLUMN commission_envoi_pourcentage DECIMAL(5,2) DEFAULT NULL COMMENT "Commission pourcentage pour les envois" AFTER commission_envoi_fixe;', 
    'SELECT "Column commission_envoi_pourcentage already exists" as message;'
);

SET @sql_add_commission_retrait_fixe = IF(@exist_commission_retrait_fixe = 0, 
    'ALTER TABLE services_transfert ADD COLUMN commission_retrait_fixe DECIMAL(10,2) DEFAULT NULL COMMENT "Commission fixe pour les retraits" AFTER commission_envoi_pourcentage;', 
    'SELECT "Column commission_retrait_fixe already exists" as message;'
);

SET @sql_add_commission_retrait_pourcentage = IF(@exist_commission_retrait_pourcentage = 0, 
    'ALTER TABLE services_transfert ADD COLUMN commission_retrait_pourcentage DECIMAL(5,2) DEFAULT NULL COMMENT "Commission pourcentage pour les retraits" AFTER commission_retrait_fixe;', 
    'SELECT "Column commission_retrait_pourcentage already exists" as message;'
);

-- Exécuter les requêtes
PREPARE stmt1 FROM @sql_add_commission_envoi_fixe;
EXECUTE stmt1;
DEALLOCATE PREPARE stmt1;

PREPARE stmt2 FROM @sql_add_commission_envoi_pourcentage;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

PREPARE stmt3 FROM @sql_add_commission_retrait_fixe;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

PREPARE stmt4 FROM @sql_add_commission_retrait_pourcentage;
EXECUTE stmt4;
DEALLOCATE PREPARE stmt4;

-- Mettre à jour les services existants avec des valeurs par défaut
-- Les commissions d'envoi reprennent les valeurs actuelles
-- Les commissions de retrait sont légèrement inférieures (stratégie commerciale)
UPDATE services_transfert 
SET 
    commission_envoi_fixe = COALESCE(commission_fixe, 0),
    commission_envoi_pourcentage = COALESCE(commission_pourcentage, 0),
    commission_retrait_fixe = COALESCE(commission_fixe * 0.8, 0),
    commission_retrait_pourcentage = COALESCE(commission_pourcentage * 0.8, 0)
WHERE 
    commission_envoi_fixe IS NULL 
    OR commission_envoi_pourcentage IS NULL 
    OR commission_retrait_fixe IS NULL 
    OR commission_retrait_pourcentage IS NULL;

-- Ajouter des commentaires aux colonnes existantes pour clarifier leur usage
ALTER TABLE services_transfert 
MODIFY COLUMN commission_fixe DECIMAL(10,2) DEFAULT NULL COMMENT 'Commission fixe générale (pour compatibilité)',
MODIFY COLUMN commission_pourcentage DECIMAL(5,2) DEFAULT NULL COMMENT 'Commission pourcentage générale (pour compatibilité)';
