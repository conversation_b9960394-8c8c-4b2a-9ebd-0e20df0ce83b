<?php
/**
 * Test de l'API transferts.php
 */

echo "TEST DE L'API TRANSFERTS\n";
echo "========================\n\n";

// Simuler une requête GET à l'API
$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET['page'] = '1';
$_GET['limit'] = '10';

// Capturer la sortie
ob_start();

try {
    // Inclure l'API
    include 'api/transferts.php';
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "RÉPONSE DE L'API:\n";
    echo $output . "\n";
    
    // Essayer de décoder le JSON
    $data = json_decode($output, true);
    
    if ($data) {
        echo "\n✅ JSON VALIDE\n";
        echo "Nombre de transferts: " . count($data['data']) . "\n";
        
        if (!empty($data['data'])) {
            echo "\nPREMIER TRANSFERT:\n";
            $premier = $data['data'][0];
            foreach ($premier as $key => $value) {
                echo "- $key: " . (is_null($value) ? 'NULL' : $value) . "\n";
            }
            
            // Vérifier spécifiquement le champ 'type'
            if (isset($premier['type'])) {
                echo "\n✅ Champ 'type' présent: " . $premier['type'] . "\n";
            } else {
                echo "\n❌ Champ 'type' manquant!\n";
            }
        }
    } else {
        echo "\n❌ JSON INVALIDE\n";
        echo "Erreur JSON: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
