[2025-07-15 20:10:00] Échec: Mot de passe incorrect
[2025-07-15 20:10:00] Vérification du mot de passe: échouée
[2025-07-15 20:10:00] Mot de passe fourni: admin123
[2025-07-15 20:10:00] Hash stocké: $2y$10$GUVDYRu0g1/CJiIH0VGmn.7uH3jgv5vjMrFlHCVsb9HZAzd/YSEtu
[2025-07-15 20:10:00] Rôle: admin
[2025-07-15 20:10:00] ID utilisateur: 1
[2025-07-15 20:10:00] Recherche de l'utilisateur: trouvé
[2025-07-15 20:10:00] Connexion à la base de données réussie
[2025-07-15 20:10:00] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:10:00] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:10:00] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:48:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:48:52] Méthode non autorisée: GET
[2025-06-30 14:48:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:48:52] Méthode non autorisée: GET
[2025-06-30 14:49:08] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:49:08] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:49:08] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 14:49:08] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 14:49:08] Connexion à la base de données réussie
[2025-06-30 14:49:08] Recherche de l'utilisateur: trouvé
[2025-06-30 14:49:08] ID utilisateur: 1
[2025-06-30 14:49:08] Rôle: admin
[2025-06-30 14:49:08] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 14:49:08] Mot de passe fourni: admin123
[2025-06-30 14:49:08] Vérification du mot de passe: réussie
[2025-06-30 14:49:08] Token généré: abf46ef8865f013371f36b02a5c061b82071bbf533e8ec874070fd8f206a773b
[2025-06-30 14:49:08] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"abf46ef8865f013371f36b02a5c061b82071bbf533e8ec874070fd8f206a773b"}
[2025-06-30 14:49:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:49:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:49:38] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 14:49:38] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 14:49:38] Connexion à la base de données réussie
[2025-06-30 14:49:38] Recherche de l'utilisateur: trouvé
[2025-06-30 14:49:38] ID utilisateur: 1
[2025-06-30 14:49:38] Rôle: admin
[2025-06-30 14:49:38] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 14:49:38] Mot de passe fourni: admin123
[2025-06-30 14:49:38] Vérification du mot de passe: réussie
[2025-06-30 14:49:38] Token généré: 979c3e6c931efb6570e1a3a664bf34c1353bc6a5b5ba024467f929c70e91920e
[2025-06-30 14:49:38] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"979c3e6c931efb6570e1a3a664bf34c1353bc6a5b5ba024467f929c70e91920e"}
[2025-06-30 14:51:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:51:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:51:45] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 14:51:45] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 14:51:45] Connexion à la base de données réussie
[2025-06-30 14:51:45] Recherche de l'utilisateur: trouvé
[2025-06-30 14:51:45] ID utilisateur: 1
[2025-06-30 14:51:45] Rôle: admin
[2025-06-30 14:51:45] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 14:51:45] Mot de passe fourni: admin123
[2025-06-30 14:51:45] Vérification du mot de passe: réussie
[2025-06-30 14:51:45] Token généré: 21a68d48b8f71f019b82e4212a2d5187aa6b5c5e7072da00abc3bbd3d6995f37
[2025-06-30 14:51:45] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"21a68d48b8f71f019b82e4212a2d5187aa6b5c5e7072da00abc3bbd3d6995f37"}
[2025-06-30 14:56:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:56:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:56:19] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 14:56:19] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 14:56:19] Connexion à la base de données réussie
[2025-06-30 14:56:19] Recherche de l'utilisateur: trouvé
[2025-06-30 14:56:19] ID utilisateur: 1
[2025-06-30 14:56:19] Rôle: admin
[2025-06-30 14:56:19] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 14:56:19] Mot de passe fourni: admin123
[2025-06-30 14:56:19] Vérification du mot de passe: réussie
[2025-06-30 14:56:19] Token généré: 631697aa6d317d87bc4c1004cb10a963d65666286d5ca67a202c195fdd4f444b
[2025-06-30 14:56:19] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"631697aa6d317d87bc4c1004cb10a963d65666286d5ca67a202c195fdd4f444b"}
[2025-06-30 14:59:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:59:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 14:59:43] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 14:59:43] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 14:59:43] Connexion à la base de données réussie
[2025-06-30 14:59:43] Recherche de l'utilisateur: trouvé
[2025-06-30 14:59:43] ID utilisateur: 1
[2025-06-30 14:59:43] Rôle: admin
[2025-06-30 14:59:43] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 14:59:43] Mot de passe fourni: admin123
[2025-06-30 14:59:43] Vérification du mot de passe: réussie
[2025-06-30 14:59:43] Token généré: f122b4c6db177680c8dcc7552d007287f354cd5735d1583b32bb1eef13a76655
[2025-06-30 14:59:43] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"f122b4c6db177680c8dcc7552d007287f354cd5735d1583b32bb1eef13a76655"}
[2025-06-30 15:23:54] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 15:23:54] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 15:23:54] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 15:23:54] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 15:23:54] Connexion à la base de données réussie
[2025-06-30 15:23:54] Recherche de l'utilisateur: trouvé
[2025-06-30 15:23:54] ID utilisateur: 1
[2025-06-30 15:23:54] Rôle: admin
[2025-06-30 15:23:54] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 15:23:54] Mot de passe fourni: admin123
[2025-06-30 15:23:54] Vérification du mot de passe: réussie
[2025-06-30 15:23:54] Token généré: e6bd685ecc0159ce92811df2c63890060a87c3cc016afec420e6e18279149375
[2025-06-30 15:23:54] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"e6bd685ecc0159ce92811df2c63890060a87c3cc016afec420e6e18279149375"}
[2025-06-30 15:36:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 15:36:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 15:36:58] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 15:36:58] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 15:36:58] Connexion à la base de données réussie
[2025-06-30 15:36:58] Recherche de l'utilisateur: trouvé
[2025-06-30 15:36:58] ID utilisateur: 1
[2025-06-30 15:36:58] Rôle: admin
[2025-06-30 15:36:58] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 15:36:58] Mot de passe fourni: admin123
[2025-06-30 15:36:58] Vérification du mot de passe: réussie
[2025-06-30 15:36:58] Token généré: 42051dbf8ffc40a8186325263e04db7a006d4068f66a1ee2612053a71af5a689
[2025-06-30 15:36:58] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"42051dbf8ffc40a8186325263e04db7a006d4068f66a1ee2612053a71af5a689"}
[2025-06-30 18:54:05] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 18:54:05] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 18:54:05] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 18:54:05] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 18:54:05] Connexion à la base de données réussie
[2025-06-30 18:54:05] Recherche de l'utilisateur: trouvé
[2025-06-30 18:54:05] ID utilisateur: 1
[2025-06-30 18:54:05] Rôle: admin
[2025-06-30 18:54:05] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 18:54:05] Mot de passe fourni: admin123
[2025-06-30 18:54:05] Vérification du mot de passe: réussie
[2025-06-30 18:54:05] Token généré: 4ae73f91772f1204bff8626f918a006cef2c64d7849ea1b6af65fce80ed1d5da
[2025-06-30 18:54:05] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"4ae73f91772f1204bff8626f918a006cef2c64d7849ea1b6af65fce80ed1d5da"}
[2025-06-30 19:08:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 19:08:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 19:08:42] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 19:08:42] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 19:08:42] Connexion à la base de données réussie
[2025-06-30 19:08:42] Recherche de l'utilisateur: trouvé
[2025-06-30 19:08:42] ID utilisateur: 1
[2025-06-30 19:08:42] Rôle: admin
[2025-06-30 19:08:42] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 19:08:42] Mot de passe fourni: admin123
[2025-06-30 19:08:42] Vérification du mot de passe: réussie
[2025-06-30 19:08:42] Token généré: fa49e36b9143f65d70c4b36e3ddfd642b3b26efef8ae102c73bd7aa4e472e811
[2025-06-30 19:08:42] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"fa49e36b9143f65d70c4b36e3ddfd642b3b26efef8ae102c73bd7aa4e472e811"}
[2025-06-30 19:35:21] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 19:35:21] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-06-30 19:35:21] Données reçues: {"username":"admin","password":"admin123"}
[2025-06-30 19:35:21] Tentative de connexion pour l'utilisateur: admin
[2025-06-30 19:35:21] Connexion à la base de données réussie
[2025-06-30 19:35:21] Recherche de l'utilisateur: trouvé
[2025-06-30 19:35:21] ID utilisateur: 1
[2025-06-30 19:35:21] Rôle: admin
[2025-06-30 19:35:21] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-06-30 19:35:21] Mot de passe fourni: admin123
[2025-06-30 19:35:21] Vérification du mot de passe: réussie
[2025-06-30 19:35:21] Token généré: 0f93a926df91cd585b186edd12eb433e71c788ea20eb6be476ff754f08036c53
[2025-06-30 19:35:21] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"0f93a926df91cd585b186edd12eb433e71c788ea20eb6be476ff754f08036c53"}
[2025-07-01 10:27:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-01 10:27:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-01 10:27:19] Données reçues: {"username":"admin","password":"moustaniratouK1"}
[2025-07-01 10:27:19] Tentative de connexion pour l'utilisateur: admin
[2025-07-01 10:27:19] Connexion à la base de données réussie
[2025-07-01 10:27:19] Recherche de l'utilisateur: trouvé
[2025-07-01 10:27:19] ID utilisateur: 1
[2025-07-01 10:27:19] Rôle: admin
[2025-07-01 10:27:19] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-01 10:27:19] Mot de passe fourni: moustaniratouK1
[2025-07-01 10:27:19] Vérification du mot de passe: échouée
[2025-07-01 10:27:19] Échec: Mot de passe incorrect
[2025-07-01 10:27:35] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-01 10:27:35] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-01 10:27:35] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-01 10:27:35] Tentative de connexion pour l'utilisateur: admin
[2025-07-01 10:27:35] Connexion à la base de données réussie
[2025-07-01 10:27:35] Recherche de l'utilisateur: trouvé
[2025-07-01 10:27:35] ID utilisateur: 1
[2025-07-01 10:27:35] Rôle: admin
[2025-07-01 10:27:35] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-01 10:27:35] Mot de passe fourni: admin123
[2025-07-01 10:27:35] Vérification du mot de passe: réussie
[2025-07-01 10:27:35] Token généré: d9e0670deb61ffdd57c44d8dde370b7b9aec2e32ab9fbf1a3407b169ea5c467a
[2025-07-01 10:27:35] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"d9e0670deb61ffdd57c44d8dde370b7b9aec2e32ab9fbf1a3407b169ea5c467a"}
[2025-07-02 11:32:01] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 11:32:01] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-02 11:32:01] Tentative de connexion pour l'utilisateur: admin
[2025-07-02 11:32:01] Connexion à la base de données réussie
[2025-07-02 11:32:01] Recherche de l'utilisateur: trouvé
[2025-07-02 11:32:01] ID utilisateur: 1
[2025-07-02 11:32:01] Rôle: admin
[2025-07-02 11:32:01] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-02 11:32:01] Mot de passe fourni: admin123
[2025-07-02 11:32:01] Vérification du mot de passe: réussie
[2025-07-02 11:32:01] Token généré: b22ed9b9daa8ab3234915251f552ef8dd7893f1c14238021a45fb2481f06cc1a
[2025-07-02 11:32:01] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"b22ed9b9daa8ab3234915251f552ef8dd7893f1c14238021a45fb2481f06cc1a"}
[2025-07-02 19:35:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:35:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:35:45] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-02 19:35:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-02 19:35:45] Connexion à la base de données réussie
[2025-07-02 19:35:45] Recherche de l'utilisateur: trouvé
[2025-07-02 19:35:45] ID utilisateur: 1
[2025-07-02 19:35:45] Rôle: admin
[2025-07-02 19:35:45] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-02 19:35:45] Mot de passe fourni: admin123
[2025-07-02 19:35:45] Vérification du mot de passe: réussie
[2025-07-02 19:35:45] Token généré: 57f712d00f2725b57d355766510c285a4a870d979db1d729094f221c078c2eba
[2025-07-02 19:35:45] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"57f712d00f2725b57d355766510c285a4a870d979db1d729094f221c078c2eba"}
[2025-07-02 19:36:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:36:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:36:19] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 19:36:19] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 19:36:19] Connexion à la base de données réussie
[2025-07-02 19:36:19] Recherche de l'utilisateur: non trouvé
[2025-07-02 19:36:19] Échec: Utilisateur non trouvé
[2025-07-02 19:36:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:36:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:36:51] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 19:36:51] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 19:36:51] Connexion à la base de données réussie
[2025-07-02 19:36:51] Recherche de l'utilisateur: non trouvé
[2025-07-02 19:36:51] Échec: Utilisateur non trouvé
[2025-07-02 19:40:49] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:40:49] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 19:40:49] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 19:40:49] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 19:40:49] Connexion à la base de données réussie
[2025-07-02 19:40:49] Recherche de l'utilisateur: trouvé
[2025-07-02 19:40:49] ID utilisateur: 2
[2025-07-02 19:40:49] Rôle: manager
[2025-07-02 19:40:49] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-02 19:40:49] Mot de passe fourni: manager123
[2025-07-02 19:40:49] Vérification du mot de passe: réussie
[2025-07-02 19:40:49] Token généré: 9d6427e8d2770b6f3e70a4884e2b0512f88d11cb8c7096c941323dbbc4b5bda5
[2025-07-02 19:40:49] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"9d6427e8d2770b6f3e70a4884e2b0512f88d11cb8c7096c941323dbbc4b5bda5"}
[2025-07-02 20:25:10] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 20:25:10] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 20:25:10] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 20:25:10] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 20:25:10] Connexion à la base de données réussie
[2025-07-02 20:25:10] Recherche de l'utilisateur: trouvé
[2025-07-02 20:25:10] ID utilisateur: 2
[2025-07-02 20:25:10] Rôle: manager
[2025-07-02 20:25:10] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-02 20:25:10] Mot de passe fourni: manager123
[2025-07-02 20:25:10] Vérification du mot de passe: réussie
[2025-07-02 20:25:10] Token généré: 34c4e6bf5fc5c253435a2707bcb238c46fa3437e973b5e6a1dafcaf895a5e225
[2025-07-02 20:25:10] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"34c4e6bf5fc5c253435a2707bcb238c46fa3437e973b5e6a1dafcaf895a5e225"}
[2025-07-02 22:45:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 22:45:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 22:45:18] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 22:45:18] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 22:45:18] Connexion à la base de données réussie
[2025-07-02 22:45:18] Recherche de l'utilisateur: trouvé
[2025-07-02 22:45:18] ID utilisateur: 2
[2025-07-02 22:45:18] Rôle: manager
[2025-07-02 22:45:18] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-02 22:45:18] Mot de passe fourni: manager123
[2025-07-02 22:45:18] Vérification du mot de passe: réussie
[2025-07-02 22:45:18] Token généré: 700f7a3aa30aadbf67cb7f2f7d9b67df86cba082f480a53c0504e11cdcaa392f
[2025-07-02 22:45:18] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"700f7a3aa30aadbf67cb7f2f7d9b67df86cba082f480a53c0504e11cdcaa392f"}
[2025-07-02 22:52:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 22:52:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 22:52:58] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 22:52:58] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 22:52:58] Connexion à la base de données réussie
[2025-07-02 22:52:58] Recherche de l'utilisateur: trouvé
[2025-07-02 22:52:58] ID utilisateur: 2
[2025-07-02 22:52:58] Rôle: manager
[2025-07-02 22:52:58] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-02 22:52:58] Mot de passe fourni: manager123
[2025-07-02 22:52:58] Vérification du mot de passe: réussie
[2025-07-02 22:52:58] Token généré: 1524bd030fc2c6e1ef1c08eb85e9791e1646c650906883282a47663b96a94c3e
[2025-07-02 22:52:58] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"1524bd030fc2c6e1ef1c08eb85e9791e1646c650906883282a47663b96a94c3e"}
[2025-07-02 23:14:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 23:14:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 23:14:13] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 23:14:13] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 23:14:13] Connexion à la base de données réussie
[2025-07-02 23:14:13] Recherche de l'utilisateur: trouvé
[2025-07-02 23:14:13] ID utilisateur: 2
[2025-07-02 23:14:13] Rôle: manager
[2025-07-02 23:14:13] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-02 23:14:13] Mot de passe fourni: manager123
[2025-07-02 23:14:13] Vérification du mot de passe: réussie
[2025-07-02 23:14:13] Token généré: ac5c62d6713b5ef15b84e6ebdb0dbbd1d39d274c9f1c44bd7cca0620973e2550
[2025-07-02 23:14:13] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"ac5c62d6713b5ef15b84e6ebdb0dbbd1d39d274c9f1c44bd7cca0620973e2550"}
[2025-07-02 23:22:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 23:22:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-02 23:22:55] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-02 23:22:55] Tentative de connexion pour l'utilisateur: manager
[2025-07-02 23:22:55] Connexion à la base de données réussie
[2025-07-02 23:22:55] Recherche de l'utilisateur: trouvé
[2025-07-02 23:22:55] ID utilisateur: 2
[2025-07-02 23:22:55] Rôle: manager
[2025-07-02 23:22:55] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-02 23:22:56] Mot de passe fourni: manager123
[2025-07-02 23:22:56] Vérification du mot de passe: réussie
[2025-07-02 23:22:56] Token généré: 9a619e095d43c188612a1c090daf80f51f63f891ef87bef57b3058e8903e417b
[2025-07-02 23:22:56] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"9a619e095d43c188612a1c090daf80f51f63f891ef87bef57b3058e8903e417b"}
[2025-07-03 10:20:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-03 10:20:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-03 10:20:40] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-03 10:20:40] Tentative de connexion pour l'utilisateur: manager
[2025-07-03 10:20:40] Connexion à la base de données réussie
[2025-07-03 10:20:40] Recherche de l'utilisateur: trouvé
[2025-07-03 10:20:40] ID utilisateur: 2
[2025-07-03 10:20:40] Rôle: manager
[2025-07-03 10:20:40] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-03 10:20:41] Mot de passe fourni: manager123
[2025-07-03 10:20:41] Vérification du mot de passe: réussie
[2025-07-03 10:20:41] Token généré: 0d42a43e4362d9b0d2b12c0ab686496b47ee3eb9d8427018950f2790ea70f1b2
[2025-07-03 10:20:41] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"0d42a43e4362d9b0d2b12c0ab686496b47ee3eb9d8427018950f2790ea70f1b2"}
[2025-07-03 10:43:07] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-03 10:43:07] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-03 10:43:07] Tentative de connexion pour l'utilisateur: manager
[2025-07-03 10:43:07] Connexion à la base de données réussie
[2025-07-03 10:43:07] Recherche de l'utilisateur: trouvé
[2025-07-03 10:43:07] ID utilisateur: 2
[2025-07-03 10:43:07] Rôle: manager
[2025-07-03 10:43:07] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-03 10:43:07] Mot de passe fourni: manager123
[2025-07-03 10:43:07] Vérification du mot de passe: réussie
[2025-07-03 10:43:07] Token généré: b84ef0f64220e0ed5e73d08080ab2680b358e10f7c28359aa463125a9dd19dbd
[2025-07-03 10:43:07] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"b84ef0f64220e0ed5e73d08080ab2680b358e10f7c28359aa463125a9dd19dbd"}
[2025-07-03 17:37:04] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-03 17:37:04] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-03 17:37:04] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-03 17:37:04] Tentative de connexion pour l'utilisateur: admin
[2025-07-03 17:37:04] Connexion à la base de données réussie
[2025-07-03 17:37:04] Recherche de l'utilisateur: trouvé
[2025-07-03 17:37:04] ID utilisateur: 1
[2025-07-03 17:37:04] Rôle: admin
[2025-07-03 17:37:04] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-03 17:37:04] Mot de passe fourni: admin123
[2025-07-03 17:37:04] Vérification du mot de passe: réussie
[2025-07-03 17:37:04] Token généré: a643b962c831de62ff1e01ecfcb952c8c8f18f2442792a636496f52e91f426d3
[2025-07-03 17:37:04] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"a643b962c831de62ff1e01ecfcb952c8c8f18f2442792a636496f52e91f426d3"}
[2025-07-04 00:37:20] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 00:37:21] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 00:37:21] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-04 00:37:21] Tentative de connexion pour l'utilisateur: admin
[2025-07-04 00:37:21] Connexion à la base de données réussie
[2025-07-04 00:37:21] Recherche de l'utilisateur: trouvé
[2025-07-04 00:37:21] ID utilisateur: 1
[2025-07-04 00:37:21] Rôle: admin
[2025-07-04 00:37:21] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-04 00:37:21] Mot de passe fourni: admin123
[2025-07-04 00:37:21] Vérification du mot de passe: réussie
[2025-07-04 00:37:21] Token généré: 6bd9d589f4230db108df803f2609fe65fa59f85f17c7eb4cbd778e74e5c5a98e
[2025-07-04 00:37:21] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"6bd9d589f4230db108df803f2609fe65fa59f85f17c7eb4cbd778e74e5c5a98e"}
[2025-07-04 00:37:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 00:37:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 00:37:38] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-04 00:37:38] Tentative de connexion pour l'utilisateur: manager
[2025-07-04 00:37:38] Connexion à la base de données réussie
[2025-07-04 00:37:38] Recherche de l'utilisateur: trouvé
[2025-07-04 00:37:38] ID utilisateur: 2
[2025-07-04 00:37:38] Rôle: manager
[2025-07-04 00:37:38] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-04 00:37:38] Mot de passe fourni: manager123
[2025-07-04 00:37:38] Vérification du mot de passe: réussie
[2025-07-04 00:37:38] Token généré: f1ec50dc01548c66b3f314d11a6629648d3e38f3a208cf0d5a0e3c6776e9bdf2
[2025-07-04 00:37:38] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"f1ec50dc01548c66b3f314d11a6629648d3e38f3a208cf0d5a0e3c6776e9bdf2"}
[2025-07-04 00:59:00] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 00:59:00] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 00:59:00] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-04 00:59:00] Tentative de connexion pour l'utilisateur: admin
[2025-07-04 00:59:00] Connexion à la base de données réussie
[2025-07-04 00:59:00] Recherche de l'utilisateur: trouvé
[2025-07-04 00:59:00] ID utilisateur: 1
[2025-07-04 00:59:00] Rôle: admin
[2025-07-04 00:59:00] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-04 00:59:00] Mot de passe fourni: admin123
[2025-07-04 00:59:00] Vérification du mot de passe: réussie
[2025-07-04 00:59:00] Token généré: 7875999a4ad91d671fc57686b029e54992da21c02b9c0b6578238be8ccefd02c
[2025-07-04 00:59:00] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"7875999a4ad91d671fc57686b029e54992da21c02b9c0b6578238be8ccefd02c"}
[2025-07-04 07:33:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 07:33:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 07:33:18] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-04 07:33:18] Tentative de connexion pour l'utilisateur: manager
[2025-07-04 07:33:18] Connexion à la base de données réussie
[2025-07-04 07:33:18] Recherche de l'utilisateur: trouvé
[2025-07-04 07:33:18] ID utilisateur: 2
[2025-07-04 07:33:18] Rôle: manager
[2025-07-04 07:33:18] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-04 07:33:19] Mot de passe fourni: manager123
[2025-07-04 07:33:19] Vérification du mot de passe: réussie
[2025-07-04 07:33:19] Token généré: e4b742abead13f9198bc65bd7e93fc171ec7bc9b88d09a8b26b8ee90a31c326e
[2025-07-04 07:33:19] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"e4b742abead13f9198bc65bd7e93fc171ec7bc9b88d09a8b26b8ee90a31c326e"}
[2025-07-04 07:53:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 07:53:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 07:53:26] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-04 07:53:26] Tentative de connexion pour l'utilisateur: admin
[2025-07-04 07:53:26] Connexion à la base de données réussie
[2025-07-04 07:53:26] Recherche de l'utilisateur: trouvé
[2025-07-04 07:53:26] ID utilisateur: 1
[2025-07-04 07:53:26] Rôle: admin
[2025-07-04 07:53:26] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-04 07:53:26] Mot de passe fourni: admin123
[2025-07-04 07:53:26] Vérification du mot de passe: réussie
[2025-07-04 07:53:26] Token généré: 846c126879b8ea862636d753ac7cfb9d6c3fe790ebd16434a31fba126d0e4409
[2025-07-04 07:53:26] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"846c126879b8ea862636d753ac7cfb9d6c3fe790ebd16434a31fba126d0e4409"}
[2025-07-04 09:39:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 09:39:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 09:39:26] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-04 09:39:26] Tentative de connexion pour l'utilisateur: manager
[2025-07-04 09:39:26] Connexion à la base de données réussie
[2025-07-04 09:39:26] Recherche de l'utilisateur: trouvé
[2025-07-04 09:39:26] ID utilisateur: 2
[2025-07-04 09:39:26] Rôle: manager
[2025-07-04 09:39:26] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-04 09:39:26] Mot de passe fourni: manager123
[2025-07-04 09:39:26] Vérification du mot de passe: réussie
[2025-07-04 09:39:26] Token généré: ec8d671a39bf55406569242c19079c4df1a5472ea149dcaab83cb8244467ed86
[2025-07-04 09:39:26] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"ec8d671a39bf55406569242c19079c4df1a5472ea149dcaab83cb8244467ed86"}
[2025-07-04 13:17:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 13:17:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 13:17:40] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-04 13:17:40] Tentative de connexion pour l'utilisateur: manager
[2025-07-04 13:17:40] Connexion à la base de données réussie
[2025-07-04 13:17:40] Recherche de l'utilisateur: trouvé
[2025-07-04 13:17:40] ID utilisateur: 2
[2025-07-04 13:17:40] Rôle: manager
[2025-07-04 13:17:40] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-04 13:17:40] Mot de passe fourni: manager123
[2025-07-04 13:17:40] Vérification du mot de passe: réussie
[2025-07-04 13:17:40] Token généré: 639d0a4cefa7458ba685aa29c5190d70a5f99f1c3c07d38a25dd6067607f8c69
[2025-07-04 13:17:40] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"639d0a4cefa7458ba685aa29c5190d70a5f99f1c3c07d38a25dd6067607f8c69"}
[2025-07-04 15:39:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 15:39:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 15:39:18] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-04 15:39:18] Tentative de connexion pour l'utilisateur: manager
[2025-07-04 15:39:18] Connexion à la base de données réussie
[2025-07-04 15:39:18] Recherche de l'utilisateur: trouvé
[2025-07-04 15:39:18] ID utilisateur: 2
[2025-07-04 15:39:18] Rôle: manager
[2025-07-04 15:39:18] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-04 15:39:18] Mot de passe fourni: manager123
[2025-07-04 15:39:18] Vérification du mot de passe: réussie
[2025-07-04 15:39:18] Token généré: b0b5fa8dc2309402d923b9b79715e74498b75711e7849621afda07712572e229
[2025-07-04 15:39:18] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"b0b5fa8dc2309402d923b9b79715e74498b75711e7849621afda07712572e229"}
[2025-07-04 21:03:29] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:03:49] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:04:49] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:15:05] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:21:06] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:21:06] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:21:06] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-04 21:21:06] Tentative de connexion pour l'utilisateur: manager
[2025-07-04 21:21:06] Connexion à la base de données réussie
[2025-07-04 21:21:06] Recherche de l'utilisateur: trouvé
[2025-07-04 21:21:06] ID utilisateur: 2
[2025-07-04 21:21:06] Rôle: manager
[2025-07-04 21:21:06] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-04 21:21:06] Mot de passe fourni: manager123
[2025-07-04 21:21:06] Vérification du mot de passe: réussie
[2025-07-04 21:21:06] Token généré: d0b7a37cfa51a4a66f2a48a8bcf9ffe4c0521f0a2cddf3b8de08b03245d12d10
[2025-07-04 21:21:06] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"d0b7a37cfa51a4a66f2a48a8bcf9ffe4c0521f0a2cddf3b8de08b03245d12d10"}
[2025-07-04 21:21:16] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:21:16] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-04 21:21:16] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-04 21:21:16] Tentative de connexion pour l'utilisateur: admin
[2025-07-04 21:21:16] Connexion à la base de données réussie
[2025-07-04 21:21:16] Recherche de l'utilisateur: trouvé
[2025-07-04 21:21:16] ID utilisateur: 1
[2025-07-04 21:21:16] Rôle: admin
[2025-07-04 21:21:16] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-04 21:21:16] Mot de passe fourni: admin123
[2025-07-04 21:21:16] Vérification du mot de passe: réussie
[2025-07-04 21:21:16] Token généré: 6579b1abbcf8fbd7a842289bbb3a9e0a9f42a5649bec629d44e18e7d7d91f766
[2025-07-04 21:21:16] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"6579b1abbcf8fbd7a842289bbb3a9e0a9f42a5649bec629d44e18e7d7d91f766"}
[2025-07-05 12:41:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-05 12:41:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-05 12:41:18] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-05 12:41:18] Tentative de connexion pour l'utilisateur: admin
[2025-07-05 12:41:18] Connexion à la base de données réussie
[2025-07-05 12:41:18] Recherche de l'utilisateur: trouvé
[2025-07-05 12:41:18] ID utilisateur: 1
[2025-07-05 12:41:18] Rôle: admin
[2025-07-05 12:41:18] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-05 12:41:18] Mot de passe fourni: admin123
[2025-07-05 12:41:18] Vérification du mot de passe: réussie
[2025-07-05 12:41:18] Token généré: 3547534ce466b8f0248c7b1a98c7eee53124c52f60b9a33a78ad482efc569c78
[2025-07-05 12:41:18] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"3547534ce466b8f0248c7b1a98c7eee53124c52f60b9a33a78ad482efc569c78"}
[2025-07-05 12:41:31] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-05 12:41:31] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-05 12:41:31] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-05 12:41:31] Tentative de connexion pour l'utilisateur: manager
[2025-07-05 12:41:31] Connexion à la base de données réussie
[2025-07-05 12:41:31] Recherche de l'utilisateur: trouvé
[2025-07-05 12:41:31] ID utilisateur: 2
[2025-07-05 12:41:31] Rôle: manager
[2025-07-05 12:41:31] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-05 12:41:31] Mot de passe fourni: manager123
[2025-07-05 12:41:31] Vérification du mot de passe: réussie
[2025-07-05 12:41:31] Token généré: da70468daa06125b92051156930f6ce14beac03977730dc07db055e16822dc37
[2025-07-05 12:41:31] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"da70468daa06125b92051156930f6ce14beac03977730dc07db055e16822dc37"}
[2025-07-05 16:50:30] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-05 16:50:30] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-05 16:50:30] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-05 16:50:30] Tentative de connexion pour l'utilisateur: admin
[2025-07-05 16:50:30] Connexion à la base de données réussie
[2025-07-05 16:50:30] Recherche de l'utilisateur: trouvé
[2025-07-05 16:50:30] ID utilisateur: 1
[2025-07-05 16:50:30] Rôle: admin
[2025-07-05 16:50:30] Hash stocké: $2y$10$aCZEKc5TncLIQy8b4Ff35eRENsQJIvW5pI3b6n.5irjOLJ1AkHu7u
[2025-07-05 16:50:30] Mot de passe fourni: admin123
[2025-07-05 16:50:30] Vérification du mot de passe: réussie
[2025-07-05 16:50:30] Token généré: 2bfed671a295dc0d0f1a060fb7f6e4c03189c70a1d12e89c219dede9d1655ba2
[2025-07-05 16:50:30] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"2bfed671a295dc0d0f1a060fb7f6e4c03189c70a1d12e89c219dede9d1655ba2"}
[2025-07-12 16:14:06] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 16:14:06] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 16:14:06] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 16:14:06] Connexion à la base de données réussie
[2025-07-12 16:14:06] Recherche de l'utilisateur: trouvé
[2025-07-12 16:14:06] ID utilisateur: 1
[2025-07-12 16:14:06] Rôle: 
[2025-07-12 16:14:06] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 16:14:06] Mot de passe fourni: admin123
[2025-07-12 16:14:06] Vérification du mot de passe: réussie
[2025-07-12 16:14:06] Token généré: 64dff976889582e0a800337746cf4ec97a31551b3620564651d8515889664b46
[2025-07-12 16:14:06] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":null},"token":"64dff976889582e0a800337746cf4ec97a31551b3620564651d8515889664b46"}
[2025-07-12 18:12:05] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:12:05] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:12:05] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:12:05] Connexion à la base de données réussie
[2025-07-12 18:12:05] Recherche de l'utilisateur: trouvé
[2025-07-12 18:12:05] ID utilisateur: 1
[2025-07-12 18:12:05] Rôle: 
[2025-07-12 18:12:05] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:12:06] Mot de passe fourni: admin123
[2025-07-12 18:12:06] Vérification du mot de passe: réussie
[2025-07-12 18:12:06] Token généré: adf8c5843c188483145dd6f83fbfb4832963bc4ee099f14d6bfa52e66cc41c48
[2025-07-12 18:12:06] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":null},"token":"adf8c5843c188483145dd6f83fbfb4832963bc4ee099f14d6bfa52e66cc41c48"}
[2025-07-12 18:12:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:12:19] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:12:19] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:12:19] Connexion à la base de données réussie
[2025-07-12 18:12:19] Recherche de l'utilisateur: trouvé
[2025-07-12 18:12:19] ID utilisateur: 2
[2025-07-12 18:12:19] Rôle: 
[2025-07-12 18:12:19] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:12:19] Mot de passe fourni: manager123
[2025-07-12 18:12:19] Vérification du mot de passe: réussie
[2025-07-12 18:12:19] Token généré: 37a47ec6aff82e8f68e2dd157981e5f2f629416d94ff38949bd4239ce7bc454e
[2025-07-12 18:12:19] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":null},"token":"37a47ec6aff82e8f68e2dd157981e5f2f629416d94ff38949bd4239ce7bc454e"}
[2025-07-12 18:12:23] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:12:23] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:12:23] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:12:23] Connexion à la base de données réussie
[2025-07-12 18:12:23] Recherche de l'utilisateur: trouvé
[2025-07-12 18:12:23] ID utilisateur: 2
[2025-07-12 18:12:23] Rôle: 
[2025-07-12 18:12:23] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:12:23] Mot de passe fourni: manager123
[2025-07-12 18:12:23] Vérification du mot de passe: réussie
[2025-07-12 18:12:23] Token généré: 7f1bda14b61eb7555cfdf00f1af141463c3fa33775633a918e0156805265eae3
[2025-07-12 18:12:23] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":null},"token":"7f1bda14b61eb7555cfdf00f1af141463c3fa33775633a918e0156805265eae3"}
[2025-07-12 18:12:28] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:12:28] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:12:28] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:12:28] Connexion à la base de données réussie
[2025-07-12 18:12:28] Recherche de l'utilisateur: trouvé
[2025-07-12 18:12:28] ID utilisateur: 2
[2025-07-12 18:12:28] Rôle: 
[2025-07-12 18:12:28] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:12:28] Mot de passe fourni: manager123
[2025-07-12 18:12:28] Vérification du mot de passe: réussie
[2025-07-12 18:12:28] Token généré: d1fff3ac69636d6737ed6714ff2b0a2b8c76e25d80f44db36eecc5ae730011fc
[2025-07-12 18:12:28] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":null},"token":"d1fff3ac69636d6737ed6714ff2b0a2b8c76e25d80f44db36eecc5ae730011fc"}
[2025-07-12 18:12:33] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:12:33] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:12:33] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:12:33] Connexion à la base de données réussie
[2025-07-12 18:12:33] Recherche de l'utilisateur: trouvé
[2025-07-12 18:12:33] ID utilisateur: 2
[2025-07-12 18:12:33] Rôle: 
[2025-07-12 18:12:33] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:12:33] Mot de passe fourni: manager123
[2025-07-12 18:12:33] Vérification du mot de passe: réussie
[2025-07-12 18:12:33] Token généré: 5271367ae2dc0b58cde29898f1940f599a5274ce0476102254e8483fb6bda840
[2025-07-12 18:12:33] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":null},"token":"5271367ae2dc0b58cde29898f1940f599a5274ce0476102254e8483fb6bda840"}
[2025-07-12 18:16:14] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:16:14] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:16:14] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:16:14] Connexion à la base de données réussie
[2025-07-12 18:16:14] Recherche de l'utilisateur: trouvé
[2025-07-12 18:16:14] ID utilisateur: 1
[2025-07-12 18:16:14] Rôle: 
[2025-07-12 18:16:14] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:16:14] Mot de passe fourni: admin123
[2025-07-12 18:16:14] Vérification du mot de passe: réussie
[2025-07-12 18:16:14] Token généré: 7ab8eebeec1d31c17e095cd807bd52c24b9f2bd1835abcebd1081e5e7bfa9099
[2025-07-12 18:16:14] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":null},"token":"7ab8eebeec1d31c17e095cd807bd52c24b9f2bd1835abcebd1081e5e7bfa9099"}
[2025-07-12 18:21:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:21:50] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:21:50] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:21:50] Connexion à la base de données réussie
[2025-07-12 18:21:50] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'roles.nom' in 'field list'
[2025-07-12 18:28:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:28:45] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:28:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:28:45] Connexion à la base de données réussie
[2025-07-12 18:28:45] Recherche de l'utilisateur: trouvé
[2025-07-12 18:28:45] ID utilisateur: 1
[2025-07-12 18:28:45] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:28:45] Mot de passe fourni: admin123
[2025-07-12 18:28:45] Vérification du mot de passe: réussie
[2025-07-12 18:28:45] Token généré: 453d0306ebd8d5fbaa196b63a4a5b3cf0474483fd83fbcd137f67911f3b7f66f
[2025-07-12 18:28:45] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin"},"token":"453d0306ebd8d5fbaa196b63a4a5b3cf0474483fd83fbcd137f67911f3b7f66f"}
[2025-07-12 18:28:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:28:55] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:28:55] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:28:55] Connexion à la base de données réussie
[2025-07-12 18:28:55] Recherche de l'utilisateur: trouvé
[2025-07-12 18:28:55] ID utilisateur: 1
[2025-07-12 18:28:55] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:28:55] Mot de passe fourni: admin123
[2025-07-12 18:28:55] Vérification du mot de passe: réussie
[2025-07-12 18:28:55] Token généré: 39db8284881f54e700700fde09a8c2ae0423e440f9263e08730c9c7dbee8aea2
[2025-07-12 18:28:55] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin"},"token":"39db8284881f54e700700fde09a8c2ae0423e440f9263e08730c9c7dbee8aea2"}
[2025-07-12 18:29:09] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:29:09] Données reçues: {"username":"admin","password":"admin123gggg"}
[2025-07-12 18:29:09] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:29:09] Connexion à la base de données réussie
[2025-07-12 18:29:09] Recherche de l'utilisateur: trouvé
[2025-07-12 18:29:09] ID utilisateur: 1
[2025-07-12 18:29:09] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:29:09] Mot de passe fourni: admin123gggg
[2025-07-12 18:29:09] Vérification du mot de passe: échouée
[2025-07-12 18:29:09] Échec: Mot de passe incorrect
[2025-07-12 18:29:20] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:29:20] Données reçues: {"username":"admin","password":"admin123gghhhh"}
[2025-07-12 18:29:20] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:29:20] Connexion à la base de données réussie
[2025-07-12 18:29:20] Recherche de l'utilisateur: trouvé
[2025-07-12 18:29:20] ID utilisateur: 1
[2025-07-12 18:29:20] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:29:20] Mot de passe fourni: admin123gghhhh
[2025-07-12 18:29:20] Vérification du mot de passe: échouée
[2025-07-12 18:29:20] Échec: Mot de passe incorrect
[2025-07-12 18:29:28] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:29:28] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:29:28] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:29:28] Connexion à la base de données réussie
[2025-07-12 18:29:28] Recherche de l'utilisateur: trouvé
[2025-07-12 18:29:28] ID utilisateur: 1
[2025-07-12 18:29:28] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:29:28] Mot de passe fourni: admin123
[2025-07-12 18:29:28] Vérification du mot de passe: réussie
[2025-07-12 18:29:28] Token généré: ae539ddf0f348e96f87ce7a221a3a8e06d76ffe9cf1de3a74f59cc76a791d8fb
[2025-07-12 18:29:28] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin"},"token":"ae539ddf0f348e96f87ce7a221a3a8e06d76ffe9cf1de3a74f59cc76a791d8fb"}
[2025-07-12 18:30:35] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:30:35] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:30:35] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:30:35] Connexion à la base de données réussie
[2025-07-12 18:30:35] Recherche de l'utilisateur: trouvé
[2025-07-12 18:30:35] ID utilisateur: 2
[2025-07-12 18:30:35] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:30:35] Mot de passe fourni: manager123
[2025-07-12 18:30:35] Vérification du mot de passe: réussie
[2025-07-12 18:30:35] Token généré: 4d09885eed03ede7529478986918b65af7334d0af779b3fad2402cdd8967132b
[2025-07-12 18:30:35] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager"},"token":"4d09885eed03ede7529478986918b65af7334d0af779b3fad2402cdd8967132b"}
[2025-07-12 18:35:32] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:35:32] Données reçues: {"username":"admin","password":"admin1ffgg23"}
[2025-07-12 18:35:32] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:35:32] Connexion à la base de données réussie
[2025-07-12 18:35:32] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:35:46] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:35:46] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:35:46] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:35:46] Connexion à la base de données réussie
[2025-07-12 18:35:46] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:38:07] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:38:07] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:38:07] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:38:07] Connexion à la base de données réussie
[2025-07-12 18:38:07] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:38:29] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:38:29] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:38:29] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:38:29] Connexion à la base de données réussie
[2025-07-12 18:38:29] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:38:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:38:40] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:38:40] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:38:40] Connexion à la base de données réussie
[2025-07-12 18:38:40] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:39:22] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:39:22] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:39:22] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:39:22] Connexion à la base de données réussie
[2025-07-12 18:39:22] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:40:41] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:40:41] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:40:41] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:40:41] Connexion à la base de données réussie
[2025-07-12 18:40:41] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'r.nom' in 'field list'
[2025-07-12 18:41:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:41:50] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:41:50] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:41:50] Connexion à la base de données réussie
[2025-07-12 18:41:50] Recherche de l'utilisateur: trouvé
[2025-07-12 18:41:50] ID utilisateur: 1
[2025-07-12 18:41:50] Rôle: admin
[2025-07-12 18:41:50] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:41:50] Mot de passe fourni: admin123
[2025-07-12 18:41:50] Vérification du mot de passe: réussie
[2025-07-12 18:41:50] Token généré: 524dc0ca155a1104182866974784b4f8e921ad861166dd088d4705ff6b3464de
[2025-07-12 18:41:50] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"524dc0ca155a1104182866974784b4f8e921ad861166dd088d4705ff6b3464de"}
[2025-07-12 18:42:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:42:26] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:42:26] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:42:26] Connexion à la base de données réussie
[2025-07-12 18:42:26] Recherche de l'utilisateur: trouvé
[2025-07-12 18:42:26] ID utilisateur: 2
[2025-07-12 18:42:26] Rôle: vendeur
[2025-07-12 18:42:26] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:42:26] Mot de passe fourni: manager123
[2025-07-12 18:42:26] Vérification du mot de passe: réussie
[2025-07-12 18:42:26] Token généré: d95e089b880674f48ca44841e94aa42720d70b4595ab6d87534213e6e1a31a2e
[2025-07-12 18:42:26] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"d95e089b880674f48ca44841e94aa42720d70b4595ab6d87534213e6e1a31a2e"}
[2025-07-12 18:42:29] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:42:29] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:42:29] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:42:29] Connexion à la base de données réussie
[2025-07-12 18:42:29] Recherche de l'utilisateur: trouvé
[2025-07-12 18:42:29] ID utilisateur: 2
[2025-07-12 18:42:29] Rôle: vendeur
[2025-07-12 18:42:29] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:42:29] Mot de passe fourni: manager123
[2025-07-12 18:42:29] Vérification du mot de passe: réussie
[2025-07-12 18:42:29] Token généré: 31ff8aa0fddf5c7ff4f64996a3b873ed3f9dc19711a5ca37f8c24aaafa56045e
[2025-07-12 18:42:29] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"31ff8aa0fddf5c7ff4f64996a3b873ed3f9dc19711a5ca37f8c24aaafa56045e"}
[2025-07-12 18:46:07] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:46:07] Données reçues: {"username":"admin","password":"admin123mppo"}
[2025-07-12 18:46:07] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:46:07] Connexion à la base de données réussie
[2025-07-12 18:46:07] Recherche de l'utilisateur: trouvé
[2025-07-12 18:46:07] ID utilisateur: 1
[2025-07-12 18:46:07] Rôle: admin
[2025-07-12 18:46:07] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:46:07] Mot de passe fourni: admin123mppo
[2025-07-12 18:46:07] Vérification du mot de passe: échouée
[2025-07-12 18:46:07] Échec: Mot de passe incorrect
[2025-07-12 18:46:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:46:18] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:46:18] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:46:18] Connexion à la base de données réussie
[2025-07-12 18:46:18] Recherche de l'utilisateur: trouvé
[2025-07-12 18:46:18] ID utilisateur: 1
[2025-07-12 18:46:18] Rôle: admin
[2025-07-12 18:46:18] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:46:18] Mot de passe fourni: admin123
[2025-07-12 18:46:18] Vérification du mot de passe: réussie
[2025-07-12 18:46:18] Token généré: 3c7da9a985d778e4a38fd30f4c2c3bb22fb7d77295417deccbf0a8a88a2f2381
[2025-07-12 18:46:18] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"3c7da9a985d778e4a38fd30f4c2c3bb22fb7d77295417deccbf0a8a88a2f2381"}
[2025-07-12 18:46:34] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:46:34] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:46:34] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:46:34] Connexion à la base de données réussie
[2025-07-12 18:46:34] Recherche de l'utilisateur: trouvé
[2025-07-12 18:46:34] ID utilisateur: 2
[2025-07-12 18:46:34] Rôle: vendeur
[2025-07-12 18:46:34] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:46:34] Mot de passe fourni: manager123
[2025-07-12 18:46:34] Vérification du mot de passe: réussie
[2025-07-12 18:46:34] Token généré: 5bcd4499507a21c66eb658becfc02775d26d9f0383488c7a2bca613e97446c2e
[2025-07-12 18:46:34] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"5bcd4499507a21c66eb658becfc02775d26d9f0383488c7a2bca613e97446c2e"}
[2025-07-12 18:51:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:51:43] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:51:43] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:51:43] Connexion à la base de données réussie
[2025-07-12 18:51:43] Recherche de l'utilisateur: trouvé
[2025-07-12 18:51:43] ID utilisateur: 2
[2025-07-12 18:51:43] Rôle: vendeur
[2025-07-12 18:51:43] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:51:44] Mot de passe fourni: manager123
[2025-07-12 18:51:44] Vérification du mot de passe: réussie
[2025-07-12 18:51:44] Token généré: 98d62aa5ac7ebb04e1bbee1ab2f2be600ba7f56e7d511cfe2c0112f04f799564
[2025-07-12 18:51:44] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"98d62aa5ac7ebb04e1bbee1ab2f2be600ba7f56e7d511cfe2c0112f04f799564"}
[2025-07-12 18:52:00] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:52:00] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:52:00] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:52:00] Connexion à la base de données réussie
[2025-07-12 18:52:00] Recherche de l'utilisateur: trouvé
[2025-07-12 18:52:00] ID utilisateur: 1
[2025-07-12 18:52:00] Rôle: admin
[2025-07-12 18:52:00] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:52:01] Mot de passe fourni: admin123
[2025-07-12 18:52:01] Vérification du mot de passe: réussie
[2025-07-12 18:52:01] Token généré: 8af69fbe0cab4f0552938a974b0f43224787ba7155097a2b4eed9ca9cac38301
[2025-07-12 18:52:01] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"8af69fbe0cab4f0552938a974b0f43224787ba7155097a2b4eed9ca9cac38301"}
[2025-07-12 18:52:14] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:52:14] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:52:14] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:52:14] Connexion à la base de données réussie
[2025-07-12 18:52:14] Recherche de l'utilisateur: trouvé
[2025-07-12 18:52:14] ID utilisateur: 2
[2025-07-12 18:52:14] Rôle: vendeur
[2025-07-12 18:52:14] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:52:14] Mot de passe fourni: manager123
[2025-07-12 18:52:14] Vérification du mot de passe: réussie
[2025-07-12 18:52:14] Token généré: d4a289e7b48ea0f3307b11c06f9629cd95525c2f7dc4fb190e9fd0c845e18cd8
[2025-07-12 18:52:14] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"d4a289e7b48ea0f3307b11c06f9629cd95525c2f7dc4fb190e9fd0c845e18cd8"}
[2025-07-12 18:53:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:53:43] Données reçues: {"username":"manager","password":"manager123mmpoo"}
[2025-07-12 18:53:43] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:53:43] Connexion à la base de données réussie
[2025-07-12 18:53:43] Recherche de l'utilisateur: trouvé
[2025-07-12 18:53:43] ID utilisateur: 2
[2025-07-12 18:53:43] Rôle: vendeur
[2025-07-12 18:53:43] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:53:43] Mot de passe fourni: manager123mmpoo
[2025-07-12 18:53:43] Vérification du mot de passe: échouée
[2025-07-12 18:53:43] Échec: Mot de passe incorrect
[2025-07-12 18:53:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:53:50] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:53:50] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:53:50] Connexion à la base de données réussie
[2025-07-12 18:53:50] Recherche de l'utilisateur: trouvé
[2025-07-12 18:53:50] ID utilisateur: 2
[2025-07-12 18:53:50] Rôle: vendeur
[2025-07-12 18:53:50] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:53:50] Mot de passe fourni: manager123
[2025-07-12 18:53:50] Vérification du mot de passe: réussie
[2025-07-12 18:53:50] Token généré: 4308e62c25cc5a28f214930f03b24b525e31bff5ccb97e6d1d65f293f5629506
[2025-07-12 18:53:50] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"4308e62c25cc5a28f214930f03b24b525e31bff5ccb97e6d1d65f293f5629506"}
[2025-07-12 18:55:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:55:19] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:55:19] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:55:19] Connexion à la base de données réussie
[2025-07-12 18:55:19] Recherche de l'utilisateur: trouvé
[2025-07-12 18:55:19] ID utilisateur: 2
[2025-07-12 18:55:19] Rôle: vendeur
[2025-07-12 18:55:19] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:55:19] Mot de passe fourni: manager123
[2025-07-12 18:55:19] Vérification du mot de passe: réussie
[2025-07-12 18:55:19] Token généré: 6b7ce10d0ae560a1e1856df020708f69604da739236a970656cf3211810d0f12
[2025-07-12 18:55:19] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"6b7ce10d0ae560a1e1856df020708f69604da739236a970656cf3211810d0f12"}
[2025-07-12 18:57:20] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:57:20] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 18:57:20] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 18:57:20] Connexion à la base de données réussie
[2025-07-12 18:57:20] Recherche de l'utilisateur: trouvé
[2025-07-12 18:57:20] ID utilisateur: 2
[2025-07-12 18:57:20] Rôle: vendeur
[2025-07-12 18:57:20] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 18:57:20] Mot de passe fourni: manager123
[2025-07-12 18:57:20] Vérification du mot de passe: réussie
[2025-07-12 18:57:20] Token généré: c9c8dc037f03ac1ad430848e35e025fcc51e6445a2fb4d7e41f2f90116343892
[2025-07-12 18:57:20] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"c9c8dc037f03ac1ad430848e35e025fcc51e6445a2fb4d7e41f2f90116343892"}
[2025-07-12 18:58:10] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 18:58:10] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 18:58:10] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 18:58:10] Connexion à la base de données réussie
[2025-07-12 18:58:10] Recherche de l'utilisateur: trouvé
[2025-07-12 18:58:10] ID utilisateur: 1
[2025-07-12 18:58:10] Rôle: admin
[2025-07-12 18:58:10] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 18:58:10] Mot de passe fourni: admin123
[2025-07-12 18:58:10] Vérification du mot de passe: réussie
[2025-07-12 18:58:10] Token généré: bcaff010fd88da355d389b0ed5d0e19646aa73256c9e3af665bd2fd12e2c1493
[2025-07-12 18:58:10] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"bcaff010fd88da355d389b0ed5d0e19646aa73256c9e3af665bd2fd12e2c1493"}
[2025-07-12 19:01:02] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 19:01:02] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 19:01:02] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 19:01:02] Connexion à la base de données réussie
[2025-07-12 19:01:02] Recherche de l'utilisateur: trouvé
[2025-07-12 19:01:02] ID utilisateur: 1
[2025-07-12 19:01:02] Rôle: admin
[2025-07-12 19:01:02] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 19:01:03] Mot de passe fourni: admin123
[2025-07-12 19:01:03] Vérification du mot de passe: réussie
[2025-07-12 19:01:03] Token généré: 2f831e84dab47a2288460f4240c72fe9add1e8c8ae2b2c73ea42e1b325a58014
[2025-07-12 19:01:03] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"2f831e84dab47a2288460f4240c72fe9add1e8c8ae2b2c73ea42e1b325a58014"}
[2025-07-12 19:01:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 19:01:12] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-12 19:01:12] Tentative de connexion pour l'utilisateur: manager
[2025-07-12 19:01:12] Connexion à la base de données réussie
[2025-07-12 19:01:12] Recherche de l'utilisateur: trouvé
[2025-07-12 19:01:12] ID utilisateur: 2
[2025-07-12 19:01:12] Rôle: vendeur
[2025-07-12 19:01:12] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-12 19:01:12] Mot de passe fourni: manager123
[2025-07-12 19:01:12] Vérification du mot de passe: réussie
[2025-07-12 19:01:12] Token généré: 0126a130a08a91cc559596a9e3003e84cae751ba715febd2a78af8b09d346e1f
[2025-07-12 19:01:12] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":2,"username":"manager","role":"vendeur"},"token":"0126a130a08a91cc559596a9e3003e84cae751ba715febd2a78af8b09d346e1f"}
[2025-07-12 19:16:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 19:16:58] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 19:16:58] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 19:16:58] Connexion à la base de données réussie
[2025-07-12 19:16:58] Recherche de l'utilisateur: trouvé
[2025-07-12 19:16:58] ID utilisateur: 1
[2025-07-12 19:16:58] Rôle: admin
[2025-07-12 19:16:58] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 19:16:58] Mot de passe fourni: admin123
[2025-07-12 19:16:58] Vérification du mot de passe: réussie
[2025-07-12 19:16:58] Token généré: adec7831b9de9eee36b07edb226a1229e146bcd1df4d5ddec5915f9504f4e1d8
[2025-07-12 19:16:58] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"adec7831b9de9eee36b07edb226a1229e146bcd1df4d5ddec5915f9504f4e1d8"}
[2025-07-12 20:48:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-12 20:48:13] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-12 20:48:13] Tentative de connexion pour l'utilisateur: admin
[2025-07-12 20:48:13] Connexion à la base de données réussie
[2025-07-12 20:48:13] Recherche de l'utilisateur: trouvé
[2025-07-12 20:48:13] ID utilisateur: 1
[2025-07-12 20:48:13] Rôle: admin
[2025-07-12 20:48:13] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-12 20:48:13] Mot de passe fourni: admin123
[2025-07-12 20:48:13] Vérification du mot de passe: réussie
[2025-07-12 20:48:13] Token généré: f44076762d1211b563457397ff79f03d53c33cd4621b59380fc6304848d0f439
[2025-07-12 20:48:13] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"f44076762d1211b563457397ff79f03d53c33cd4621b59380fc6304848d0f439"}
[2025-07-13 09:25:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 09:25:55] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 09:25:55] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 09:25:55] Connexion à la base de données réussie
[2025-07-13 09:25:55] Recherche de l'utilisateur: trouvé
[2025-07-13 09:25:55] ID utilisateur: 1
[2025-07-13 09:25:55] Rôle: admin
[2025-07-13 09:25:55] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 09:25:55] Mot de passe fourni: admin123
[2025-07-13 09:25:55] Vérification du mot de passe: réussie
[2025-07-13 09:25:55] Token généré: 6a94d9b5c656865fd2b0685f25097b7c29b372cf2c34508247bbd13a96ff8175
[2025-07-13 09:25:56] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"6a94d9b5c656865fd2b0685f25097b7c29b372cf2c34508247bbd13a96ff8175"}
[2025-07-13 11:38:59] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 11:38:59] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 11:38:59] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 11:38:59] Connexion à la base de données réussie
[2025-07-13 11:38:59] Recherche de l'utilisateur: trouvé
[2025-07-13 11:38:59] ID utilisateur: 1
[2025-07-13 11:38:59] Rôle: admin
[2025-07-13 11:38:59] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 11:38:59] Mot de passe fourni: admin123
[2025-07-13 11:38:59] Vérification du mot de passe: réussie
[2025-07-13 11:38:59] Token généré: 4621dbcbc21613d4758c2e3aa2fd354900eb95a13190abec8aa2c65d688a5de9
[2025-07-13 11:38:59] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"4621dbcbc21613d4758c2e3aa2fd354900eb95a13190abec8aa2c65d688a5de9"}
[2025-07-13 12:02:35] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 12:02:35] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 12:02:35] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 12:02:35] Connexion à la base de données réussie
[2025-07-13 12:02:35] Recherche de l'utilisateur: trouvé
[2025-07-13 12:02:35] ID utilisateur: 1
[2025-07-13 12:02:35] Rôle: admin
[2025-07-13 12:02:35] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 12:02:35] Mot de passe fourni: admin123
[2025-07-13 12:02:35] Vérification du mot de passe: réussie
[2025-07-13 12:02:35] Token généré: 6be42a9dfc9c3e2b029e1095dc0a052f599bb36ead535129b816d7285fe9ba80
[2025-07-13 12:02:35] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"6be42a9dfc9c3e2b029e1095dc0a052f599bb36ead535129b816d7285fe9ba80"}
[2025-07-13 12:07:34] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 12:07:34] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 12:07:34] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 12:07:34] Connexion à la base de données réussie
[2025-07-13 12:07:34] Recherche de l'utilisateur: trouvé
[2025-07-13 12:07:34] ID utilisateur: 1
[2025-07-13 12:07:34] Rôle: admin
[2025-07-13 12:07:34] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 12:07:34] Mot de passe fourni: admin123
[2025-07-13 12:07:34] Vérification du mot de passe: réussie
[2025-07-13 12:07:34] Token généré: 67efaa9742a447487b08212490d40a241a2f5f3f8fb2ce31322654889ac321ee
[2025-07-13 12:07:34] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"67efaa9742a447487b08212490d40a241a2f5f3f8fb2ce31322654889ac321ee"}
[2025-07-13 12:11:30] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 12:11:30] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 12:11:30] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 12:11:30] Connexion à la base de données réussie
[2025-07-13 12:11:30] Recherche de l'utilisateur: trouvé
[2025-07-13 12:11:30] ID utilisateur: 1
[2025-07-13 12:11:30] Rôle: admin
[2025-07-13 12:11:30] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 12:11:30] Mot de passe fourni: admin123
[2025-07-13 12:11:30] Vérification du mot de passe: réussie
[2025-07-13 12:11:30] Token généré: 54b44d87173d8bbbde97c81dcf4a626348f98e6b755603093b07f1109cd95a40
[2025-07-13 12:11:30] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"54b44d87173d8bbbde97c81dcf4a626348f98e6b755603093b07f1109cd95a40"}
[2025-07-13 12:30:27] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 12:30:27] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 12:30:27] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 12:30:27] Connexion à la base de données réussie
[2025-07-13 12:30:27] Recherche de l'utilisateur: trouvé
[2025-07-13 12:30:27] ID utilisateur: 1
[2025-07-13 12:30:27] Rôle: admin
[2025-07-13 12:30:27] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 12:30:27] Mot de passe fourni: admin123
[2025-07-13 12:30:27] Vérification du mot de passe: réussie
[2025-07-13 12:30:27] Token généré: b3829ec4d6c8d0233ef40105c15a357ef18c4907d31e64a4da2ee793417aaf01
[2025-07-13 12:30:27] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"b3829ec4d6c8d0233ef40105c15a357ef18c4907d31e64a4da2ee793417aaf01"}
[2025-07-13 12:35:37] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 12:35:37] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 12:35:37] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 12:35:37] Connexion à la base de données réussie
[2025-07-13 12:35:37] Recherche de l'utilisateur: trouvé
[2025-07-13 12:35:37] ID utilisateur: 1
[2025-07-13 12:35:37] Rôle: admin
[2025-07-13 12:35:37] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 12:35:37] Mot de passe fourni: admin123
[2025-07-13 12:35:37] Vérification du mot de passe: réussie
[2025-07-13 12:35:37] Token généré: 7fc7fc400483b98c1eda5c4d5ab79439f1d9af5a92d0dc770671dd3c3275b07c
[2025-07-13 12:35:37] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"7fc7fc400483b98c1eda5c4d5ab79439f1d9af5a92d0dc770671dd3c3275b07c"}
[2025-07-13 13:17:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 13:17:50] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 13:17:50] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 13:17:50] Connexion à la base de données réussie
[2025-07-13 13:17:50] Recherche de l'utilisateur: trouvé
[2025-07-13 13:17:50] ID utilisateur: 1
[2025-07-13 13:17:50] Rôle: admin
[2025-07-13 13:17:50] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 13:17:50] Mot de passe fourni: admin123
[2025-07-13 13:17:50] Vérification du mot de passe: réussie
[2025-07-13 13:17:50] Token généré: b6b598b6a83eb0aa7a3574cd552f379957cf525ae875452779be2fed630f3a27
[2025-07-13 13:17:50] Connexion réussie! Réponse envoyée: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"b6b598b6a83eb0aa7a3574cd552f379957cf525ae875452779be2fed630f3a27"}
[2025-07-13 13:53:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 13:53:18] Données reçues: {"username":"admin","password":"moustaniratouK1"}
[2025-07-13 13:53:18] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 13:53:18] Connexion à la base de données réussie
[2025-07-13 13:53:18] Recherche de l'utilisateur: trouvé
[2025-07-13 13:53:18] ID utilisateur: 1
[2025-07-13 13:53:18] Rôle: admin
[2025-07-13 13:53:18] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 13:53:18] Mot de passe fourni: moustaniratouK1
[2025-07-13 13:53:18] Vérification du mot de passe: échouée
[2025-07-13 13:53:18] Échec: Mot de passe incorrect
[2025-07-13 13:53:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 13:53:26] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 13:53:26] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 13:53:26] Connexion à la base de données réussie
[2025-07-13 13:53:26] Recherche de l'utilisateur: trouvé
[2025-07-13 13:53:26] ID utilisateur: 1
[2025-07-13 13:53:26] Rôle: admin
[2025-07-13 13:53:26] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 13:53:27] Mot de passe fourni: admin123
[2025-07-13 13:53:27] Vérification du mot de passe: réussie
[2025-07-13 13:53:27] Connexion réussie! Token JWT généré et envoyé.
[2025-07-13 14:41:17] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 14:41:17] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 14:41:17] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 14:41:17] Connexion à la base de données réussie
[2025-07-13 14:41:17] Recherche de l'utilisateur: trouvé
[2025-07-13 14:41:17] ID utilisateur: 1
[2025-07-13 14:41:17] Rôle: admin
[2025-07-13 14:41:17] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 14:41:17] Mot de passe fourni: admin123
[2025-07-13 14:41:17] Vérification du mot de passe: réussie
[2025-07-13 14:41:17] Connexion réussie! Token JWT généré et envoyé.
[2025-07-13 18:32:07] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 18:32:07] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 18:32:07] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 18:32:07] Connexion à la base de données réussie
[2025-07-13 18:32:07] Recherche de l'utilisateur: trouvé
[2025-07-13 18:32:07] ID utilisateur: 1
[2025-07-13 18:32:07] Rôle: admin
[2025-07-13 18:32:07] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 18:32:07] Mot de passe fourni: admin123
[2025-07-13 18:32:07] Vérification du mot de passe: réussie
[2025-07-13 18:32:07] Connexion réussie! Token JWT généré et envoyé.
[2025-07-13 21:59:03] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-13 21:59:03] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-13 21:59:03] Tentative de connexion pour l'utilisateur: admin
[2025-07-13 21:59:03] Connexion à la base de données réussie
[2025-07-13 21:59:03] Recherche de l'utilisateur: trouvé
[2025-07-13 21:59:03] ID utilisateur: 1
[2025-07-13 21:59:03] Rôle: admin
[2025-07-13 21:59:03] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-13 21:59:03] Mot de passe fourni: admin123
[2025-07-13 21:59:03] Vérification du mot de passe: réussie
[2025-07-13 21:59:03] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 00:46:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 00:46:52] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 00:46:52] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 00:46:52] Connexion à la base de données réussie
[2025-07-14 00:46:52] Recherche de l'utilisateur: trouvé
[2025-07-14 00:46:52] ID utilisateur: 2
[2025-07-14 00:46:52] Rôle: vendeur
[2025-07-14 00:46:52] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 00:46:52] Mot de passe fourni: manager123
[2025-07-14 00:46:52] Vérification du mot de passe: réussie
[2025-07-14 00:46:52] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 01:25:15] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 01:25:15] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 01:25:15] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 01:25:15] Connexion à la base de données réussie
[2025-07-14 01:25:15] Recherche de l'utilisateur: trouvé
[2025-07-14 01:25:15] ID utilisateur: 1
[2025-07-14 01:25:15] Rôle: admin
[2025-07-14 01:25:15] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 01:25:15] Mot de passe fourni: admin123
[2025-07-14 01:25:15] Vérification du mot de passe: réussie
[2025-07-14 01:25:15] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 02:59:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 02:59:13] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 02:59:13] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 02:59:13] Connexion à la base de données réussie
[2025-07-14 02:59:13] Recherche de l'utilisateur: trouvé
[2025-07-14 02:59:13] ID utilisateur: 1
[2025-07-14 02:59:13] Rôle: admin
[2025-07-14 02:59:13] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 02:59:13] Mot de passe fourni: admin123
[2025-07-14 02:59:13] Vérification du mot de passe: réussie
[2025-07-14 02:59:13] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 04:57:49] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 04:57:49] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 04:57:49] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 04:57:49] Connexion à la base de données réussie
[2025-07-14 04:57:49] Recherche de l'utilisateur: trouvé
[2025-07-14 04:57:49] ID utilisateur: 2
[2025-07-14 04:57:49] Rôle: vendeur
[2025-07-14 04:57:49] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 04:57:49] Mot de passe fourni: manager123
[2025-07-14 04:57:49] Vérification du mot de passe: réussie
[2025-07-14 04:57:49] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 05:09:08] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 05:09:08] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 05:09:08] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 05:09:08] Connexion à la base de données réussie
[2025-07-14 05:09:08] Recherche de l'utilisateur: trouvé
[2025-07-14 05:09:08] ID utilisateur: 1
[2025-07-14 05:09:08] Rôle: admin
[2025-07-14 05:09:08] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 05:09:08] Mot de passe fourni: admin123
[2025-07-14 05:09:08] Vérification du mot de passe: réussie
[2025-07-14 05:09:08] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 11:25:28] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 11:25:28] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 11:25:28] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 11:25:28] Connexion à la base de données réussie
[2025-07-14 11:25:28] Recherche de l'utilisateur: trouvé
[2025-07-14 11:25:28] ID utilisateur: 1
[2025-07-14 11:25:28] Rôle: admin
[2025-07-14 11:25:28] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 11:25:28] Mot de passe fourni: admin123
[2025-07-14 11:25:28] Vérification du mot de passe: réussie
[2025-07-14 11:25:28] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 11:47:22] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 11:47:22] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 11:47:22] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 11:47:22] Connexion à la base de données réussie
[2025-07-14 11:47:22] Recherche de l'utilisateur: trouvé
[2025-07-14 11:47:22] ID utilisateur: 2
[2025-07-14 11:47:22] Rôle: vendeur
[2025-07-14 11:47:22] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 11:47:22] Mot de passe fourni: manager123
[2025-07-14 11:47:22] Vérification du mot de passe: réussie
[2025-07-14 11:47:22] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 13:10:25] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 13:10:25] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 13:10:25] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 13:10:25] Connexion à la base de données réussie
[2025-07-14 13:10:25] Recherche de l'utilisateur: trouvé
[2025-07-14 13:10:25] ID utilisateur: 2
[2025-07-14 13:10:25] Rôle: vendeur
[2025-07-14 13:10:25] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 13:10:25] Mot de passe fourni: manager123
[2025-07-14 13:10:25] Vérification du mot de passe: réussie
[2025-07-14 13:10:25] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 14:07:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 14:07:45] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 14:07:45] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 14:07:45] Connexion à la base de données réussie
[2025-07-14 14:07:45] Recherche de l'utilisateur: trouvé
[2025-07-14 14:07:45] ID utilisateur: 2
[2025-07-14 14:07:45] Rôle: vendeur
[2025-07-14 14:07:45] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 14:07:45] Mot de passe fourni: manager123
[2025-07-14 14:07:45] Vérification du mot de passe: réussie
[2025-07-14 14:07:45] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 14:34:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 14:34:52] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 14:34:52] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 14:34:52] Connexion à la base de données réussie
[2025-07-14 14:34:52] Recherche de l'utilisateur: trouvé
[2025-07-14 14:34:52] ID utilisateur: 1
[2025-07-14 14:34:52] Rôle: admin
[2025-07-14 14:34:52] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 14:34:52] Mot de passe fourni: admin123
[2025-07-14 14:34:52] Vérification du mot de passe: réussie
[2025-07-14 14:34:52] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 14:40:30] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 14:40:30] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 14:40:30] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 14:40:30] Connexion à la base de données réussie
[2025-07-14 14:40:30] Recherche de l'utilisateur: trouvé
[2025-07-14 14:40:30] ID utilisateur: 2
[2025-07-14 14:40:30] Rôle: vendeur
[2025-07-14 14:40:30] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 14:40:30] Mot de passe fourni: manager123
[2025-07-14 14:40:30] Vérification du mot de passe: réussie
[2025-07-14 14:40:30] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 15:34:36] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 15:34:36] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 15:34:36] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 15:34:36] Connexion à la base de données réussie
[2025-07-14 15:34:36] Recherche de l'utilisateur: trouvé
[2025-07-14 15:34:36] ID utilisateur: 2
[2025-07-14 15:34:36] Rôle: vendeur
[2025-07-14 15:34:36] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 15:34:36] Mot de passe fourni: manager123
[2025-07-14 15:34:36] Vérification du mot de passe: réussie
[2025-07-14 15:34:36] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 16:54:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 16:54:51] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 16:54:51] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 16:54:51] Connexion à la base de données réussie
[2025-07-14 16:54:51] Recherche de l'utilisateur: trouvé
[2025-07-14 16:54:51] ID utilisateur: 1
[2025-07-14 16:54:51] Rôle: admin
[2025-07-14 16:54:51] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 16:54:51] Mot de passe fourni: admin123
[2025-07-14 16:54:51] Vérification du mot de passe: réussie
[2025-07-14 16:54:51] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 16:56:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 16:56:18] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-14 16:56:18] Tentative de connexion pour l'utilisateur: manager
[2025-07-14 16:56:18] Connexion à la base de données réussie
[2025-07-14 16:56:18] Recherche de l'utilisateur: trouvé
[2025-07-14 16:56:18] ID utilisateur: 2
[2025-07-14 16:56:18] Rôle: vendeur
[2025-07-14 16:56:18] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-14 16:56:19] Mot de passe fourni: manager123
[2025-07-14 16:56:19] Vérification du mot de passe: réussie
[2025-07-14 16:56:19] Connexion réussie! Token JWT généré et envoyé.
[2025-07-14 19:59:10] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-14 19:59:10] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-14 19:59:10] Tentative de connexion pour l'utilisateur: admin
[2025-07-14 19:59:10] Connexion à la base de données réussie
[2025-07-14 19:59:10] Recherche de l'utilisateur: trouvé
[2025-07-14 19:59:10] ID utilisateur: 1
[2025-07-14 19:59:10] Rôle: admin
[2025-07-14 19:59:10] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-14 19:59:10] Mot de passe fourni: admin123
[2025-07-14 19:59:10] Vérification du mot de passe: réussie
[2025-07-14 19:59:10] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 10:08:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 10:08:38] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-15 10:08:38] Tentative de connexion pour l'utilisateur: manager
[2025-07-15 10:08:38] Connexion à la base de données réussie
[2025-07-15 10:08:38] Recherche de l'utilisateur: trouvé
[2025-07-15 10:08:38] ID utilisateur: 2
[2025-07-15 10:08:38] Rôle: vendeur
[2025-07-15 10:08:38] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-15 10:08:38] Mot de passe fourni: manager123
[2025-07-15 10:08:38] Vérification du mot de passe: réussie
[2025-07-15 10:08:38] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 11:17:41] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 11:17:41] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-15 11:17:41] Tentative de connexion pour l'utilisateur: manager
[2025-07-15 11:17:41] Connexion à la base de données réussie
[2025-07-15 11:17:41] Recherche de l'utilisateur: trouvé
[2025-07-15 11:17:41] ID utilisateur: 2
[2025-07-15 11:17:41] Rôle: vendeur
[2025-07-15 11:17:41] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-15 11:17:41] Mot de passe fourni: manager123
[2025-07-15 11:17:41] Vérification du mot de passe: réussie
[2025-07-15 11:17:41] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 14:39:39] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 14:39:39] Données reçues: {"username":"admin","password":"admin123444"}
[2025-07-15 14:39:39] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 14:39:39] Connexion à la base de données réussie
[2025-07-15 14:39:39] Recherche de l'utilisateur: trouvé
[2025-07-15 14:39:39] ID utilisateur: 1
[2025-07-15 14:39:39] Rôle: admin
[2025-07-15 14:39:39] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-15 14:39:39] Mot de passe fourni: admin123444
[2025-07-15 14:39:39] Vérification du mot de passe: échouée
[2025-07-15 14:39:39] Échec: Mot de passe incorrect
[2025-07-15 14:39:53] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 14:39:53] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 14:39:53] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 14:39:53] Connexion à la base de données réussie
[2025-07-15 14:39:53] Recherche de l'utilisateur: trouvé
[2025-07-15 14:39:53] ID utilisateur: 1
[2025-07-15 14:39:53] Rôle: admin
[2025-07-15 14:39:53] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-15 14:39:53] Mot de passe fourni: admin123
[2025-07-15 14:39:53] Vérification du mot de passe: réussie
[2025-07-15 14:39:53] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 16:00:54] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 16:00:54] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 16:00:54] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 16:00:54] Connexion à la base de données réussie
[2025-07-15 16:00:54] Recherche de l'utilisateur: trouvé
[2025-07-15 16:00:54] ID utilisateur: 1
[2025-07-15 16:00:54] Rôle: admin
[2025-07-15 16:00:54] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-15 16:00:54] Mot de passe fourni: admin123
[2025-07-15 16:00:54] Vérification du mot de passe: réussie
[2025-07-15 16:00:54] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 16:32:10] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 16:32:10] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 16:32:10] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 16:32:10] Connexion à la base de données réussie
[2025-07-15 16:32:10] Recherche de l'utilisateur: trouvé
[2025-07-15 16:32:10] ID utilisateur: 1
[2025-07-15 16:32:10] Rôle: admin
[2025-07-15 16:32:10] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-15 16:32:10] Mot de passe fourni: admin123
[2025-07-15 16:32:10] Vérification du mot de passe: réussie
[2025-07-15 16:32:10] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 16:33:24] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 16:33:24] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 16:33:24] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 16:33:24] Connexion à la base de données réussie
[2025-07-15 16:33:24] Recherche de l'utilisateur: trouvé
[2025-07-15 16:33:24] ID utilisateur: 1
[2025-07-15 16:33:24] Rôle: admin
[2025-07-15 16:33:24] Hash stocké: $2y$10$f0gTzWQDNAn9PoJxqmnp/eU6x8F9fCQyJnJZAOoapOnnuWnVoH8fK
[2025-07-15 16:33:24] Mot de passe fourni: admin123
[2025-07-15 16:33:24] Vérification du mot de passe: réussie
[2025-07-15 16:33:24] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 19:43:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 19:43:52] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-15 19:43:52] Tentative de connexion pour l'utilisateur: manager
[2025-07-15 19:43:52] Connexion à la base de données réussie
[2025-07-15 19:43:52] Recherche de l'utilisateur: trouvé
[2025-07-15 19:43:52] ID utilisateur: 2
[2025-07-15 19:43:52] Rôle: vendeur
[2025-07-15 19:43:52] Hash stocké: $2y$10$RZDFUUt7vpcKOzAdN0Ox.eA11aLFkhup5bLdMSIT1sJ/Mf7mGAFwu
[2025-07-15 19:43:52] Mot de passe fourni: manager123
[2025-07-15 19:43:52] Vérification du mot de passe: réussie
[2025-07-15 19:43:52] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 20:03:47] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:03:47] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:03:47] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:03:47] Connexion à la base de données réussie
[2025-07-15 20:03:47] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'u.role_id' in 'on clause'
[2025-07-15 20:03:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:03:50] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:03:50] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:03:50] Connexion à la base de données réussie
[2025-07-15 20:03:50] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'u.role_id' in 'on clause'
[2025-07-15 20:04:03] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:04:03] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:04:03] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:04:03] Connexion à la base de données réussie
[2025-07-15 20:04:03] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'u.role_id' in 'on clause'
[2025-07-15 20:04:33] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:04:33] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:04:33] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:04:33] Connexion à la base de données réussie
[2025-07-15 20:04:33] ERREUR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'u.role_id' in 'on clause'
[2025-07-15 20:06:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:06:13] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:06:13] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:06:13] Connexion à la base de données réussie
[2025-07-15 20:06:13] Recherche de l'utilisateur: trouvé
[2025-07-15 20:06:13] ID utilisateur: 1
[2025-07-15 20:06:13] Rôle: admin
[2025-07-15 20:06:13] Hash stocké: $2y$10$GUVDYRu0g1/CJiIH0VGmn.7uH3jgv5vjMrFlHCVsb9HZAzd/YSEtu
[2025-07-15 20:06:13] Mot de passe fourni: admin123
[2025-07-15 20:06:13] Vérification du mot de passe: échouée
[2025-07-15 20:06:13] Échec: Mot de passe incorrect
[2025-07-15 20:08:47] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:08:47] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:08:47] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:08:47] Connexion à la base de données réussie
[2025-07-15 20:08:47] Recherche de l'utilisateur: trouvé
[2025-07-15 20:08:47] ID utilisateur: 1
[2025-07-15 20:08:47] Rôle: admin
[2025-07-15 20:08:47] Hash stocké: $2y$10$GUVDYRu0g1/CJiIH0VGmn.7uH3jgv5vjMrFlHCVsb9HZAzd/YSEtu
[2025-07-15 20:08:47] Mot de passe fourni: admin123
[2025-07-15 20:08:47] Vérification du mot de passe: échouée
[2025-07-15 20:08:47] Échec: Mot de passe incorrect
[2025-07-15 20:15:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:15:12] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:15:12] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:15:12] Connexion à la base de données réussie
[2025-07-15 20:15:12] Recherche de l'utilisateur: trouvé
[2025-07-15 20:15:12] ID utilisateur: 1
[2025-07-15 20:15:12] Rôle: admin
[2025-07-15 20:15:12] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:15:12] Mot de passe fourni: admin123
[2025-07-15 20:15:12] Vérification du mot de passe: échouée
[2025-07-15 20:15:12] Échec: Mot de passe incorrect
[2025-07-15 20:15:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:15:43] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:15:43] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:15:43] Connexion à la base de données réussie
[2025-07-15 20:15:43] Recherche de l'utilisateur: trouvé
[2025-07-15 20:15:43] ID utilisateur: 1
[2025-07-15 20:15:43] Rôle: admin
[2025-07-15 20:15:43] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:15:43] Mot de passe fourni: admin123
[2025-07-15 20:15:43] Vérification du mot de passe: échouée
[2025-07-15 20:15:43] Échec: Mot de passe incorrect
[2025-07-15 20:16:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:16:13] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:16:13] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:16:13] Connexion à la base de données réussie
[2025-07-15 20:16:13] Recherche de l'utilisateur: trouvé
[2025-07-15 20:16:13] ID utilisateur: 1
[2025-07-15 20:16:13] Rôle: admin
[2025-07-15 20:16:13] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:16:13] Mot de passe fourni: admin123
[2025-07-15 20:16:13] Vérification du mot de passe: échouée
[2025-07-15 20:16:13] Échec: Mot de passe incorrect
[2025-07-15 20:16:30] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:16:30] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:16:30] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:16:30] Connexion à la base de données réussie
[2025-07-15 20:16:30] Recherche de l'utilisateur: trouvé
[2025-07-15 20:16:30] ID utilisateur: 1
[2025-07-15 20:16:30] Rôle: admin
[2025-07-15 20:16:30] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:16:30] Mot de passe fourni: admin123
[2025-07-15 20:16:30] Vérification du mot de passe: échouée
[2025-07-15 20:16:30] Échec: Mot de passe incorrect
[2025-07-15 20:18:57] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:18:57] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:18:57] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:18:57] Connexion à la base de données réussie
[2025-07-15 20:18:57] Recherche de l'utilisateur: trouvé
[2025-07-15 20:18:57] ID utilisateur: 1
[2025-07-15 20:18:57] Rôle: admin
[2025-07-15 20:18:57] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:18:57] Mot de passe fourni: admin123
[2025-07-15 20:18:57] Vérification du mot de passe: échouée
[2025-07-15 20:18:57] Échec: Mot de passe incorrect
[2025-07-15 20:23:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:23:43] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:23:43] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:23:43] Connexion à la base de données réussie
[2025-07-15 20:23:43] Recherche de l'utilisateur: trouvé
[2025-07-15 20:23:43] ID utilisateur: 1
[2025-07-15 20:23:43] Rôle: admin
[2025-07-15 20:23:43] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:23:43] Mot de passe fourni: admin123
[2025-07-15 20:23:43] Vérification du mot de passe: échouée
[2025-07-15 20:23:43] Échec: Mot de passe incorrect
[2025-07-15 20:26:17] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:26:17] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:26:17] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:26:17] Connexion à la base de données réussie
[2025-07-15 20:26:17] Recherche de l'utilisateur: trouvé
[2025-07-15 20:26:17] ID utilisateur: 1
[2025-07-15 20:26:17] Rôle: admin
[2025-07-15 20:26:17] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:26:18] Mot de passe fourni: admin123
[2025-07-15 20:26:18] Vérification du mot de passe: échouée
[2025-07-15 20:26:18] Échec: Mot de passe incorrect
[2025-07-15 20:28:15] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:28:15] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:28:15] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:28:15] Connexion à la base de données réussie
[2025-07-15 20:28:15] Recherche de l'utilisateur: trouvé
[2025-07-15 20:28:15] ID utilisateur: 1
[2025-07-15 20:28:15] Rôle: admin
[2025-07-15 20:28:15] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:28:15] Mot de passe fourni: admin123
[2025-07-15 20:28:15] Vérification du mot de passe: échouée
[2025-07-15 20:28:15] Échec: Mot de passe incorrect
[2025-07-15 20:29:59] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:29:59] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:29:59] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:29:59] Connexion à la base de données réussie
[2025-07-15 20:29:59] Recherche de l'utilisateur: trouvé
[2025-07-15 20:29:59] ID utilisateur: 1
[2025-07-15 20:29:59] Rôle: admin
[2025-07-15 20:29:59] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 20:29:59] ATTENTION: Réinitialisation forcée du mot de passe pour 'admin'.
[2025-07-15 20:29:59] Nouveau hash généré: $2y$10$wb0IUpTGDe8g1JC50BCcX.8PDXuvF3rpZDpUKrqzmFO7pLTLWI.fe
[2025-07-15 20:29:59] Le mot de passe de l'admin a été mis à jour dans la base de données.
[2025-07-15 20:29:59] Mot de passe fourni: admin123
[2025-07-15 20:29:59] Vérification du mot de passe: réussie
[2025-07-15 20:29:59] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 20:45:49] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 20:45:49] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 20:45:49] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 20:45:49] Connexion à la base de données réussie
[2025-07-15 20:45:49] Recherche de l'utilisateur: trouvé
[2025-07-15 20:45:49] ID utilisateur: 1
[2025-07-15 20:45:49] Rôle: admin
[2025-07-15 20:45:49] Hash stocké: $2y$10$wb0IUpTGDe8g1JC50BCcX.8PDXuvF3rpZDpUKrqzmFO7pLTLWI.fe
[2025-07-15 20:45:49] Mot de passe fourni: admin123
[2025-07-15 20:45:49] Vérification du mot de passe: réussie
[2025-07-15 20:45:49] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 21:59:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 21:59:42] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 21:59:42] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 21:59:42] Connexion à la base de données réussie
[2025-07-15 21:59:42] Recherche de l'utilisateur: trouvé
[2025-07-15 21:59:42] ID utilisateur: 1
[2025-07-15 21:59:42] Rôle: Non défini
[2025-07-15 21:59:42] Hash stocké: $2y$10$vuwu8ZDlXiHgKhkOBYpK8OdPuwB26aRA5jWys.DDCTc4zPGmpUgd
[2025-07-15 21:59:42] Mot de passe fourni: admin123
[2025-07-15 21:59:42] Vérification du mot de passe: échouée
[2025-07-15 21:59:42] Échec: Mot de passe incorrect
[2025-07-15 22:03:10] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 22:03:10] Données reçues: {"username":"admin","password":"admin"}
[2025-07-15 22:03:10] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 22:03:10] Connexion à la base de données réussie
[2025-07-15 22:03:10] Recherche de l'utilisateur: trouvé
[2025-07-15 22:03:10] ID utilisateur: 1
[2025-07-15 22:03:10] Rôle: Non défini
[2025-07-15 22:03:10] Hash stocké: $2y$10$hEQnAioUMugx5m8eZopWLeQAU9HHZwIM362MLA3PXXkt3zPhSH9ta
[2025-07-15 22:03:10] Mot de passe fourni: admin
[2025-07-15 22:03:10] Vérification du mot de passe: réussie
[2025-07-15 22:03:10] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 22:03:28] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 22:03:28] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-15 22:03:28] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 22:03:28] Connexion à la base de données réussie
[2025-07-15 22:03:28] Recherche de l'utilisateur: trouvé
[2025-07-15 22:03:28] ID utilisateur: 1
[2025-07-15 22:03:28] Rôle: Non défini
[2025-07-15 22:03:28] Hash stocké: $2y$10$hEQnAioUMugx5m8eZopWLeQAU9HHZwIM362MLA3PXXkt3zPhSH9ta
[2025-07-15 22:03:28] Mot de passe fourni: admin123
[2025-07-15 22:03:28] Vérification du mot de passe: échouée
[2025-07-15 22:03:28] Échec: Mot de passe incorrect
[2025-07-15 22:03:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 22:03:45] Données reçues: {"username":"admin","password":"admin"}
[2025-07-15 22:03:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 22:03:45] Connexion à la base de données réussie
[2025-07-15 22:03:45] Recherche de l'utilisateur: trouvé
[2025-07-15 22:03:45] ID utilisateur: 1
[2025-07-15 22:03:45] Rôle: Non défini
[2025-07-15 22:03:45] Hash stocké: $2y$10$hEQnAioUMugx5m8eZopWLeQAU9HHZwIM362MLA3PXXkt3zPhSH9ta
[2025-07-15 22:03:45] Mot de passe fourni: admin
[2025-07-15 22:03:45] Vérification du mot de passe: réussie
[2025-07-15 22:03:45] Connexion réussie! Token JWT généré et envoyé.
[2025-07-15 22:07:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-15 22:07:40] Données reçues: {"username":"admin","password":"admin"}
[2025-07-15 22:07:40] Tentative de connexion pour l'utilisateur: admin
[2025-07-15 22:07:40] Connexion à la base de données réussie
[2025-07-15 22:07:40] Recherche de l'utilisateur: trouvé
[2025-07-15 22:07:40] ID utilisateur: 1
[2025-07-15 22:07:40] Rôle: admin
[2025-07-15 22:07:40] Hash stocké: $2y$10$hEQnAioUMugx5m8eZopWLeQAU9HHZwIM362MLA3PXXkt3zPhSH9ta
[2025-07-15 22:07:40] Mot de passe fourni: admin
[2025-07-15 22:07:40] Vérification du mot de passe: réussie
[2025-07-15 22:07:40] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 10:40:54] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 10:40:54] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 10:40:54] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 10:40:54] Connexion à la base de données réussie
[2025-07-16 10:40:54] Recherche de l'utilisateur: trouvé
[2025-07-16 10:40:54] ID utilisateur: 2
[2025-07-16 10:40:54] Rôle: user
[2025-07-16 10:40:54] Hash stocké: $2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp
[2025-07-16 10:40:54] Mot de passe fourni: manager123
[2025-07-16 10:40:54] Vérification du mot de passe: échouée
[2025-07-16 10:40:54] Échec: Mot de passe incorrect
[2025-07-16 10:41:04] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 10:41:04] Données reçues: {"username":"manager","password":"manager"}
[2025-07-16 10:41:04] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 10:41:04] Connexion à la base de données réussie
[2025-07-16 10:41:04] Recherche de l'utilisateur: trouvé
[2025-07-16 10:41:04] ID utilisateur: 2
[2025-07-16 10:41:04] Rôle: user
[2025-07-16 10:41:04] Hash stocké: $2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp
[2025-07-16 10:41:04] Mot de passe fourni: manager
[2025-07-16 10:41:04] Vérification du mot de passe: échouée
[2025-07-16 10:41:04] Échec: Mot de passe incorrect
[2025-07-16 10:42:48] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 10:42:48] Données reçues: {"username":"admin","password":"admin"}
[2025-07-16 10:42:48] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 10:42:48] Connexion à la base de données réussie
[2025-07-16 10:42:48] Recherche de l'utilisateur: trouvé
[2025-07-16 10:42:48] ID utilisateur: 1
[2025-07-16 10:42:48] Rôle: admin
[2025-07-16 10:42:48] Hash stocké: $2y$10$hEQnAioUMugx5m8eZopWLeQAU9HHZwIM362MLA3PXXkt3zPhSH9ta
[2025-07-16 10:42:48] Mot de passe fourni: admin
[2025-07-16 10:42:48] Vérification du mot de passe: réussie
[2025-07-16 10:42:48] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:04:56] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:04:56] Données reçues: {"username":"admin","password":"admin"}
[2025-07-16 12:04:56] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 12:04:56] Connexion à la base de données réussie
[2025-07-16 12:04:56] Recherche de l'utilisateur: trouvé
[2025-07-16 12:04:56] ID utilisateur: 1
[2025-07-16 12:04:56] Rôle: admin
[2025-07-16 12:04:56] Hash stocké: $2y$10$hEQnAioUMugx5m8eZopWLeQAU9HHZwIM362MLA3PXXkt3zPhSH9ta
[2025-07-16 12:04:56] Mot de passe fourni: admin
[2025-07-16 12:04:56] Vérification du mot de passe: réussie
[2025-07-16 12:04:56] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:17:39] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:17:39] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:17:39] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:17:39] Connexion à la base de données réussie
[2025-07-16 12:17:39] Recherche de l'utilisateur: trouvé
[2025-07-16 12:17:39] ID utilisateur: 2
[2025-07-16 12:17:39] Rôle: user
[2025-07-16 12:17:39] Hash stocké: $2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp
[2025-07-16 12:17:39] Mot de passe fourni: manager123
[2025-07-16 12:17:39] Vérification du mot de passe: échouée
[2025-07-16 12:17:39] Échec: Mot de passe incorrect
[2025-07-16 12:29:35] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:29:35] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:29:35] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:29:35] Connexion à la base de données réussie
[2025-07-16 12:29:35] Recherche de l'utilisateur: trouvé
[2025-07-16 12:29:35] ID utilisateur: 2
[2025-07-16 12:29:35] Rôle: user
[2025-07-16 12:29:35] Hash stocké: $2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp
[2025-07-16 12:29:35] Mot de passe fourni: manager123
[2025-07-16 12:29:35] Vérification du mot de passe: échouée
[2025-07-16 12:29:35] Échec: Mot de passe incorrect
[2025-07-16 12:30:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:30:42] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:30:42] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:30:42] Connexion à la base de données réussie
[2025-07-16 12:30:42] Recherche de l'utilisateur: trouvé
[2025-07-16 12:30:42] ID utilisateur: 2
[2025-07-16 12:30:42] Rôle: user
[2025-07-16 12:30:42] Hash stocké: $2y$10$ZYtSOBAttu7IOY1RPRtyGepkeGQQQjkSVM.c2Vqk8QJkqNwQmQZp
[2025-07-16 12:30:42] Mot de passe fourni: manager123
[2025-07-16 12:30:42] Vérification du mot de passe: échouée
[2025-07-16 12:30:42] Échec: Mot de passe incorrect
[2025-07-16 12:37:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:37:50] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:37:50] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:37:50] Connexion à la base de données réussie
[2025-07-16 12:37:50] Recherche de l'utilisateur: trouvé
[2025-07-16 12:37:50] ID utilisateur: 2
[2025-07-16 12:37:50] Rôle: user
[2025-07-16 12:37:50] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:37:50] Mot de passe fourni: manager123
[2025-07-16 12:37:50] Vérification du mot de passe: réussie
[2025-07-16 12:37:50] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:38:57] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:38:57] Données reçues: {"username":"admin","password":"admin"}
[2025-07-16 12:38:57] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 12:38:57] Connexion à la base de données réussie
[2025-07-16 12:38:57] Recherche de l'utilisateur: trouvé
[2025-07-16 12:38:57] ID utilisateur: 1
[2025-07-16 12:38:57] Rôle: admin
[2025-07-16 12:38:57] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-16 12:38:57] Mot de passe fourni: admin
[2025-07-16 12:38:57] Vérification du mot de passe: échouée
[2025-07-16 12:38:57] Échec: Mot de passe incorrect
[2025-07-16 12:39:03] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:39:03] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-16 12:39:03] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 12:39:03] Connexion à la base de données réussie
[2025-07-16 12:39:03] Recherche de l'utilisateur: trouvé
[2025-07-16 12:39:03] ID utilisateur: 1
[2025-07-16 12:39:03] Rôle: admin
[2025-07-16 12:39:03] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-16 12:39:03] Mot de passe fourni: admin123
[2025-07-16 12:39:03] Vérification du mot de passe: réussie
[2025-07-16 12:39:03] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:41:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:41:12] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:41:12] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:41:12] Connexion à la base de données réussie
[2025-07-16 12:41:12] Recherche de l'utilisateur: trouvé
[2025-07-16 12:41:12] ID utilisateur: 2
[2025-07-16 12:41:12] Rôle: user
[2025-07-16 12:41:12] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:41:12] Mot de passe fourni: manager123
[2025-07-16 12:41:12] Vérification du mot de passe: réussie
[2025-07-16 12:41:12] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:41:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:41:45] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-16 12:41:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 12:41:45] Connexion à la base de données réussie
[2025-07-16 12:41:45] Recherche de l'utilisateur: trouvé
[2025-07-16 12:41:45] ID utilisateur: 1
[2025-07-16 12:41:45] Rôle: admin
[2025-07-16 12:41:45] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-16 12:41:45] Mot de passe fourni: admin123
[2025-07-16 12:41:45] Vérification du mot de passe: réussie
[2025-07-16 12:41:45] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:43:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:43:12] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:43:12] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:43:12] Connexion à la base de données réussie
[2025-07-16 12:43:12] Recherche de l'utilisateur: trouvé
[2025-07-16 12:43:12] ID utilisateur: 2
[2025-07-16 12:43:12] Rôle: user
[2025-07-16 12:43:12] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:43:12] Mot de passe fourni: manager123
[2025-07-16 12:43:12] Vérification du mot de passe: réussie
[2025-07-16 12:43:12] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:43:16] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:43:16] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:43:16] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:43:16] Connexion à la base de données réussie
[2025-07-16 12:43:16] Recherche de l'utilisateur: trouvé
[2025-07-16 12:43:16] ID utilisateur: 2
[2025-07-16 12:43:16] Rôle: user
[2025-07-16 12:43:16] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:43:16] Mot de passe fourni: manager123
[2025-07-16 12:43:16] Vérification du mot de passe: réussie
[2025-07-16 12:43:16] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:43:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:43:40] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:43:40] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:43:40] Connexion à la base de données réussie
[2025-07-16 12:43:40] Recherche de l'utilisateur: trouvé
[2025-07-16 12:43:40] ID utilisateur: 2
[2025-07-16 12:43:40] Rôle: user
[2025-07-16 12:43:40] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:43:40] Mot de passe fourni: manager123
[2025-07-16 12:43:40] Vérification du mot de passe: réussie
[2025-07-16 12:43:40] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:49:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:49:12] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:49:12] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:49:12] Connexion à la base de données réussie
[2025-07-16 12:49:12] Recherche de l'utilisateur: trouvé
[2025-07-16 12:49:12] ID utilisateur: 2
[2025-07-16 12:49:12] Rôle: superviseur
[2025-07-16 12:49:12] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:49:12] Mot de passe fourni: manager123
[2025-07-16 12:49:12] Vérification du mot de passe: réussie
[2025-07-16 12:49:12] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:52:16] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:52:16] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 12:52:16] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 12:52:16] Connexion à la base de données réussie
[2025-07-16 12:52:16] Recherche de l'utilisateur: trouvé
[2025-07-16 12:52:16] ID utilisateur: 2
[2025-07-16 12:52:16] Rôle: superviseur
[2025-07-16 12:52:16] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 12:52:16] Mot de passe fourni: manager123
[2025-07-16 12:52:16] Vérification du mot de passe: réussie
[2025-07-16 12:52:16] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 12:52:47] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 12:52:47] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-16 12:52:47] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 12:52:47] Connexion à la base de données réussie
[2025-07-16 12:52:47] Recherche de l'utilisateur: trouvé
[2025-07-16 12:52:47] ID utilisateur: 1
[2025-07-16 12:52:47] Rôle: admin
[2025-07-16 12:52:47] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-16 12:52:48] Mot de passe fourni: admin123
[2025-07-16 12:52:48] Vérification du mot de passe: réussie
[2025-07-16 12:52:48] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 13:47:24] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 13:47:24] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-16 13:47:24] Tentative de connexion pour l'utilisateur: manager
[2025-07-16 13:47:24] Connexion à la base de données réussie
[2025-07-16 13:47:24] Recherche de l'utilisateur: trouvé
[2025-07-16 13:47:24] ID utilisateur: 2
[2025-07-16 13:47:24] Rôle: superviseur
[2025-07-16 13:47:24] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-16 13:47:24] Mot de passe fourni: manager123
[2025-07-16 13:47:24] Vérification du mot de passe: réussie
[2025-07-16 13:47:24] Connexion réussie! Token JWT généré et envoyé.
[2025-07-16 20:08:32] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 20:08:32] Données reçues: {"username":"admin","password":"admin"}
[2025-07-16 20:08:32] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 20:08:32] Connexion à la base de données réussie
[2025-07-16 20:08:32] Recherche de l'utilisateur: trouvé
[2025-07-16 20:08:32] ID utilisateur: 1
[2025-07-16 20:08:32] Rôle: admin
[2025-07-16 20:08:32] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-16 20:08:32] Mot de passe fourni: admin
[2025-07-16 20:08:32] Vérification du mot de passe: échouée
[2025-07-16 20:08:32] Échec: Mot de passe incorrect
[2025-07-16 20:08:41] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-16 20:08:41] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-16 20:08:41] Tentative de connexion pour l'utilisateur: admin
[2025-07-16 20:08:41] Connexion à la base de données réussie
[2025-07-16 20:08:41] Recherche de l'utilisateur: trouvé
[2025-07-16 20:08:41] ID utilisateur: 1
[2025-07-16 20:08:41] Rôle: admin
[2025-07-16 20:08:41] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-16 20:08:41] Mot de passe fourni: admin123
[2025-07-16 20:08:41] Vérification du mot de passe: réussie
[2025-07-16 20:08:41] Rôle défini dans la base de données: admin
[2025-07-16 20:08:41] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-16 20:08:41] Connexion réussie! Token JWT généré et envoyé.
[2025-07-17 11:37:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 11:37:51] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 11:37:51] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 11:37:51] Connexion à la base de données réussie
[2025-07-17 11:37:51] Recherche de l'utilisateur: trouvé
[2025-07-17 11:37:51] ID utilisateur: 1
[2025-07-17 11:37:51] Rôle: admin
[2025-07-17 11:37:51] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 11:37:51] Mot de passe fourni: admin123
[2025-07-17 11:37:51] Vérification du mot de passe: réussie
[2025-07-17 11:37:51] Rôle défini dans la base de données: admin
[2025-07-17 11:37:51] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 11:37:51] Connexion réussie! Token JWT généré et envoyé.
[2025-07-17 18:12:06] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 18:12:06] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 18:12:06] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 18:12:06] Connexion à la base de données réussie
[2025-07-17 18:12:06] Recherche de l'utilisateur: trouvé
[2025-07-17 18:12:06] ID utilisateur: 1
[2025-07-17 18:12:06] Rôle: admin
[2025-07-17 18:12:06] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 18:12:06] Mot de passe fourni: admin123
[2025-07-17 18:12:06] Vérification du mot de passe: réussie
[2025-07-17 18:12:07] Rôle défini dans la base de données: admin
[2025-07-17 18:12:07] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 18:12:07] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 18:12:07] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 18:12:07] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 18:12:07] Connexion à la base de données réussie
[2025-07-17 18:12:07] Recherche de l'utilisateur: trouvé
[2025-07-17 18:12:07] ID utilisateur: 1
[2025-07-17 18:12:07] Rôle: admin
[2025-07-17 18:12:07] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 18:12:07] Mot de passe fourni: admin123
[2025-07-17 18:12:07] Vérification du mot de passe: réussie
[2025-07-17 18:12:07] Rôle défini dans la base de données: admin
[2025-07-17 18:12:07] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 18:19:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 18:19:19] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 18:19:19] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 18:19:19] Connexion à la base de données réussie
[2025-07-17 18:19:19] Recherche de l'utilisateur: trouvé
[2025-07-17 18:19:19] ID utilisateur: 1
[2025-07-17 18:19:19] Rôle: admin
[2025-07-17 18:19:19] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 18:19:19] Mot de passe fourni: admin123
[2025-07-17 18:19:19] Vérification du mot de passe: réussie
[2025-07-17 18:19:19] Rôle défini dans la base de données: admin
[2025-07-17 18:19:19] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 18:29:20] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 18:29:20] Méthode HTTP: POST
[2025-07-17 18:29:20] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-17 18:29:20] Origin: http://localhost
[2025-07-17 18:29:20] Referer: http://localhost/Gestion_moulin_wifiZone_ok/api/test_fetch_login.html
[2025-07-17 18:29:20] En-têtes de la requête:
[2025-07-17 18:29:20]   Host: localhost
[2025-07-17 18:29:20]   Connection: keep-alive
[2025-07-17 18:29:20]   Content-Length: 42
[2025-07-17 18:29:20]   Pragma: no-cache
[2025-07-17 18:29:20]   Cache-Control: no-cache
[2025-07-17 18:29:20]   sec-ch-ua-platform: "macOS"
[2025-07-17 18:29:20]   X-Requested-With: XMLHttpRequest
[2025-07-17 18:29:20]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-17 18:29:20]   sec-ch-ua: "Chromium";v="135", "Not:A Brand";v="99", "Google Chrome";v="135"
[2025-07-17 18:29:20]   Content-Type: application/json
[2025-07-17 18:29:20]   sec-ch-ua-mobile: ?0
[2025-07-17 18:29:20]   Accept-Language: en-US,en;q=0.9
[2025-07-17 18:29:20]   Accept: */*
[2025-07-17 18:29:20]   Origin: http://localhost
[2025-07-17 18:29:20]   Sec-Fetch-Site: same-origin
[2025-07-17 18:29:20]   Sec-Fetch-Mode: cors
[2025-07-17 18:29:20]   Sec-Fetch-Dest: empty
[2025-07-17 18:29:20]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/api/test_fetch_login.html
[2025-07-17 18:29:20]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-17 18:29:20] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 18:29:20] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 18:29:20] Connexion à la base de données réussie
[2025-07-17 18:29:20] Recherche de l'utilisateur: trouvé
[2025-07-17 18:29:20] ID utilisateur: 1
[2025-07-17 18:29:20] Rôle: admin
[2025-07-17 18:29:20] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 18:29:20] Mot de passe fourni: admin123
[2025-07-17 18:29:20] Vérification du mot de passe: réussie
[2025-07-17 18:29:20] Rôle défini dans la base de données: admin
[2025-07-17 18:29:20] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 18:29:20] Connexion réussie! Token JWT généré:
[2025-07-17 18:29:20] User ID: 1
[2025-07-17 18:29:20] Username: admin
[2025-07-17 18:29:20] Rôle: admin
[2025-07-17 18:29:20] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-17 18:30:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 18:30:13] Méthode HTTP: POST
[2025-07-17 18:30:13] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-17 18:30:13] Origin: http://localhost
[2025-07-17 18:30:13] Referer: http://localhost/Gestion_moulin_wifiZone_ok/api/simple_test.html
[2025-07-17 18:30:13] En-têtes de la requête:
[2025-07-17 18:30:13]   Host: localhost
[2025-07-17 18:30:13]   Connection: keep-alive
[2025-07-17 18:30:13]   Content-Length: 42
[2025-07-17 18:30:13]   Pragma: no-cache
[2025-07-17 18:30:13]   Cache-Control: no-cache
[2025-07-17 18:30:13]   sec-ch-ua-platform: "macOS"
[2025-07-17 18:30:13]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-17 18:30:13]   sec-ch-ua: "Chromium";v="135", "Not:A Brand";v="99", "Google Chrome";v="135"
[2025-07-17 18:30:13]   Content-Type: application/json
[2025-07-17 18:30:13]   sec-ch-ua-mobile: ?0
[2025-07-17 18:30:13]   Accept-Language: en-US,en;q=0.9
[2025-07-17 18:30:13]   Accept: */*
[2025-07-17 18:30:13]   Origin: http://localhost
[2025-07-17 18:30:13]   Sec-Fetch-Site: same-origin
[2025-07-17 18:30:13]   Sec-Fetch-Mode: cors
[2025-07-17 18:30:13]   Sec-Fetch-Dest: empty
[2025-07-17 18:30:13]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/api/simple_test.html
[2025-07-17 18:30:13]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-17 18:30:13] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 18:30:13] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 18:30:13] Connexion à la base de données réussie
[2025-07-17 18:30:13] Recherche de l'utilisateur: trouvé
[2025-07-17 18:30:13] ID utilisateur: 1
[2025-07-17 18:30:13] Rôle: admin
[2025-07-17 18:30:13] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 18:30:13] Mot de passe fourni: admin123
[2025-07-17 18:30:13] Vérification du mot de passe: réussie
[2025-07-17 18:30:13] Rôle défini dans la base de données: admin
[2025-07-17 18:30:13] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 18:30:13] Connexion réussie! Token JWT généré:
[2025-07-17 18:30:13] User ID: 1
[2025-07-17 18:30:13] Username: admin
[2025-07-17 18:30:13] Rôle: admin
[2025-07-17 18:30:13] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-17 18:36:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-17 18:36:52] Méthode HTTP: POST
[2025-07-17 18:36:52] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-17 18:36:52] Origin: http://localhost
[2025-07-17 18:36:52] Referer: http://localhost/Gestion_moulin_wifiZone_ok/api/simple_test.html
[2025-07-17 18:36:52] En-têtes de la requête:
[2025-07-17 18:36:52]   Host: localhost
[2025-07-17 18:36:52]   Connection: keep-alive
[2025-07-17 18:36:52]   Content-Length: 42
[2025-07-17 18:36:52]   sec-ch-ua-platform: "Windows"
[2025-07-17 18:36:52]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-17 18:36:52]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"
[2025-07-17 18:36:52]   Content-Type: application/json
[2025-07-17 18:36:52]   sec-ch-ua-mobile: ?0
[2025-07-17 18:36:52]   Accept: */*
[2025-07-17 18:36:52]   Sec-GPC: 1
[2025-07-17 18:36:52]   Accept-Language: fr-FR,fr;q=0.9
[2025-07-17 18:36:52]   Origin: http://localhost
[2025-07-17 18:36:52]   Sec-Fetch-Site: same-origin
[2025-07-17 18:36:52]   Sec-Fetch-Mode: cors
[2025-07-17 18:36:52]   Sec-Fetch-Dest: empty
[2025-07-17 18:36:52]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/api/simple_test.html
[2025-07-17 18:36:52]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-17 18:36:52] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-17 18:36:52] Tentative de connexion pour l'utilisateur: admin
[2025-07-17 18:36:52] Connexion à la base de données réussie
[2025-07-17 18:36:52] Recherche de l'utilisateur: trouvé
[2025-07-17 18:36:52] ID utilisateur: 1
[2025-07-17 18:36:52] Rôle: admin
[2025-07-17 18:36:52] Hash stocké: $2y$10$PbJGZuYDXcTOs6P.D8w0NeKRxkTuCL5CGFb8TgWsTX7EwY.1/pmc.
[2025-07-17 18:36:52] Mot de passe fourni: admin123
[2025-07-17 18:36:52] Vérification du mot de passe: réussie
[2025-07-17 18:36:52] Rôle défini dans la base de données: admin
[2025-07-17 18:36:52] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-17 18:36:52] Connexion réussie! Token JWT généré:
[2025-07-17 18:36:52] User ID: 1
[2025-07-17 18:36:52] Username: admin
[2025-07-17 18:36:52] Rôle: admin
[2025-07-17 18:36:52] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 09:24:59] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 09:24:59] Méthode HTTP: POST
[2025-07-18 09:24:59] User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4652
[2025-07-18 09:24:59] Origin: Non défini
[2025-07-18 09:24:59] Referer: Non défini
[2025-07-18 09:24:59] En-têtes de la requête:
[2025-07-18 09:24:59]   User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4652
[2025-07-18 09:24:59]   Content-Type: application/json
[2025-07-18 09:24:59]   Host: localhost
[2025-07-18 09:24:59]   Content-Length: 39
[2025-07-18 09:24:59] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 09:24:59] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 09:24:59] Connexion à la base de données réussie
[2025-07-18 09:24:59] Recherche de l'utilisateur: trouvé
[2025-07-18 09:24:59] ID utilisateur: 1
[2025-07-18 09:24:59] Rôle: admin
[2025-07-18 09:24:59] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 09:24:59] Mot de passe fourni: admin
[2025-07-18 09:24:59] Vérification du mot de passe: réussie
[2025-07-18 09:24:59] Rôle défini dans la base de données: admin
[2025-07-18 09:24:59] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 09:24:59] Connexion réussie! Token JWT généré:
[2025-07-18 09:24:59] User ID: 1
[2025-07-18 09:24:59] Username: admin
[2025-07-18 09:24:59] Rôle: admin
[2025-07-18 09:24:59] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 09:27:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 09:27:45] Méthode HTTP: POST
[2025-07-18 09:27:45] User-Agent: Non défini
[2025-07-18 09:27:45] Origin: Non défini
[2025-07-18 09:27:45] Referer: Non défini
[2025-07-18 09:27:45] En-têtes de la requête:
[2025-07-18 09:27:45]   Host: localhost
[2025-07-18 09:27:45]   Connection: close
[2025-07-18 09:27:45]   Content-Length: 39
[2025-07-18 09:27:45]   Content-Type: application/json
[2025-07-18 09:27:45] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 09:27:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 09:27:45] Connexion à la base de données réussie
[2025-07-18 09:27:45] Recherche de l'utilisateur: trouvé
[2025-07-18 09:27:45] ID utilisateur: 1
[2025-07-18 09:27:45] Rôle: admin
[2025-07-18 09:27:45] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 09:27:46] Mot de passe fourni: admin
[2025-07-18 09:27:46] Vérification du mot de passe: réussie
[2025-07-18 09:27:46] Rôle défini dans la base de données: admin
[2025-07-18 09:27:46] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 09:27:46] Connexion réussie! Token JWT généré:
[2025-07-18 09:27:46] User ID: 1
[2025-07-18 09:27:46] Username: admin
[2025-07-18 09:27:46] Rôle: admin
[2025-07-18 09:27:46] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 10:52:34] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 10:52:34] Méthode HTTP: POST
[2025-07-18 10:52:34] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:52:34] Origin: http://localhost
[2025-07-18 10:52:34] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_cors_final.html
[2025-07-18 10:52:34] En-têtes de la requête:
[2025-07-18 10:52:34]   Host: localhost
[2025-07-18 10:52:34]   Connection: keep-alive
[2025-07-18 10:52:34]   Content-Length: 39
[2025-07-18 10:52:34]   sec-ch-ua-platform: "Windows"
[2025-07-18 10:52:34]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:52:34]   X-KL-Ajax-Request: Ajax_Request
[2025-07-18 10:52:34]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 10:52:34]   Content-Type: application/json
[2025-07-18 10:52:34]   sec-ch-ua-mobile: ?0
[2025-07-18 10:52:34]   Accept: */*
[2025-07-18 10:52:34]   Origin: http://localhost
[2025-07-18 10:52:34]   Sec-Fetch-Site: same-origin
[2025-07-18 10:52:34]   Sec-Fetch-Mode: cors
[2025-07-18 10:52:34]   Sec-Fetch-Dest: empty
[2025-07-18 10:52:34]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_cors_final.html
[2025-07-18 10:52:34]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 10:52:34]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 10:52:34]   Cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 10:52:34] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 10:52:34] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 10:52:34] Connexion à la base de données réussie
[2025-07-18 10:52:34] Recherche de l'utilisateur: trouvé
[2025-07-18 10:52:34] ID utilisateur: 1
[2025-07-18 10:52:34] Rôle: admin
[2025-07-18 10:52:34] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 10:52:34] Mot de passe fourni: admin
[2025-07-18 10:52:34] Vérification du mot de passe: réussie
[2025-07-18 10:52:34] Rôle défini dans la base de données: admin
[2025-07-18 10:52:34] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 10:52:34] Connexion réussie! Token JWT généré:
[2025-07-18 10:52:34] User ID: 1
[2025-07-18 10:52:34] Username: admin
[2025-07-18 10:52:34] Rôle: admin
[2025-07-18 10:52:34] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 10:52:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 10:52:38] Méthode HTTP: POST
[2025-07-18 10:52:38] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:52:38] Origin: http://localhost
[2025-07-18 10:52:38] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_cors_final.html
[2025-07-18 10:52:38] En-têtes de la requête:
[2025-07-18 10:52:38]   Host: localhost
[2025-07-18 10:52:38]   Connection: keep-alive
[2025-07-18 10:52:38]   Content-Length: 39
[2025-07-18 10:52:38]   sec-ch-ua-platform: "Windows"
[2025-07-18 10:52:38]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:52:38]   X-KL-Ajax-Request: Ajax_Request
[2025-07-18 10:52:38]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 10:52:38]   Content-Type: application/json
[2025-07-18 10:52:38]   sec-ch-ua-mobile: ?0
[2025-07-18 10:52:38]   Accept: */*
[2025-07-18 10:52:38]   Origin: http://localhost
[2025-07-18 10:52:38]   Sec-Fetch-Site: same-origin
[2025-07-18 10:52:38]   Sec-Fetch-Mode: cors
[2025-07-18 10:52:38]   Sec-Fetch-Dest: empty
[2025-07-18 10:52:38]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_cors_final.html
[2025-07-18 10:52:38]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 10:52:38]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 10:52:38]   Cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 10:52:38] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 10:52:38] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 10:52:38] Connexion à la base de données réussie
[2025-07-18 10:52:38] Recherche de l'utilisateur: trouvé
[2025-07-18 10:52:38] ID utilisateur: 1
[2025-07-18 10:52:38] Rôle: admin
[2025-07-18 10:52:38] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 10:52:38] Mot de passe fourni: admin
[2025-07-18 10:52:38] Vérification du mot de passe: réussie
[2025-07-18 10:52:38] Rôle défini dans la base de données: admin
[2025-07-18 10:52:38] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 10:52:38] Connexion réussie! Token JWT généré:
[2025-07-18 10:52:38] User ID: 1
[2025-07-18 10:52:38] Username: admin
[2025-07-18 10:52:38] Rôle: admin
[2025-07-18 10:52:38] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 10:53:44] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 10:53:44] Méthode HTTP: POST
[2025-07-18 10:53:44] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:53:44] Origin: http://localhost:8080
[2025-07-18 10:53:44] Referer: http://localhost:8080/
[2025-07-18 10:53:44] En-têtes de la requête:
[2025-07-18 10:53:44]   Host: localhost
[2025-07-18 10:53:44]   Connection: keep-alive
[2025-07-18 10:53:44]   Content-Length: 39
[2025-07-18 10:53:44]   sec-ch-ua-platform: "Windows"
[2025-07-18 10:53:44]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:53:44]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 10:53:44]   Content-Type: application/json
[2025-07-18 10:53:44]   sec-ch-ua-mobile: ?0
[2025-07-18 10:53:44]   Accept: */*
[2025-07-18 10:53:44]   Origin: http://localhost:8080
[2025-07-18 10:53:44]   Sec-Fetch-Site: same-site
[2025-07-18 10:53:44]   Sec-Fetch-Mode: cors
[2025-07-18 10:53:44]   Sec-Fetch-Dest: empty
[2025-07-18 10:53:44]   Referer: http://localhost:8080/
[2025-07-18 10:53:44]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 10:53:44]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 10:53:44] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 10:53:44] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 10:53:44] Connexion à la base de données réussie
[2025-07-18 10:53:44] Recherche de l'utilisateur: trouvé
[2025-07-18 10:53:44] ID utilisateur: 1
[2025-07-18 10:53:44] Rôle: admin
[2025-07-18 10:53:44] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 10:53:44] Mot de passe fourni: admin
[2025-07-18 10:53:44] Vérification du mot de passe: réussie
[2025-07-18 10:53:44] Rôle défini dans la base de données: admin
[2025-07-18 10:53:44] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 10:53:44] Connexion réussie! Token JWT généré:
[2025-07-18 10:53:44] User ID: 1
[2025-07-18 10:53:44] Username: admin
[2025-07-18 10:53:44] Rôle: admin
[2025-07-18 10:53:44] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 10:59:36] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 10:59:36] Méthode HTTP: POST
[2025-07-18 10:59:36] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:59:36] Origin: http://localhost:8080
[2025-07-18 10:59:36] Referer: http://localhost:8080/
[2025-07-18 10:59:36] En-têtes de la requête:
[2025-07-18 10:59:36]   Host: localhost
[2025-07-18 10:59:36]   Connection: keep-alive
[2025-07-18 10:59:36]   Content-Length: 39
[2025-07-18 10:59:36]   sec-ch-ua-platform: "Windows"
[2025-07-18 10:59:36]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:59:36]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 10:59:36]   Content-Type: application/json
[2025-07-18 10:59:36]   sec-ch-ua-mobile: ?0
[2025-07-18 10:59:36]   Accept: */*
[2025-07-18 10:59:36]   Origin: http://localhost:8080
[2025-07-18 10:59:36]   Sec-Fetch-Site: same-site
[2025-07-18 10:59:36]   Sec-Fetch-Mode: cors
[2025-07-18 10:59:36]   Sec-Fetch-Dest: empty
[2025-07-18 10:59:36]   Referer: http://localhost:8080/
[2025-07-18 10:59:36]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 10:59:36]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 10:59:36] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 10:59:36] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 10:59:36] Connexion à la base de données réussie
[2025-07-18 10:59:36] Recherche de l'utilisateur: trouvé
[2025-07-18 10:59:36] ID utilisateur: 1
[2025-07-18 10:59:36] Rôle: admin
[2025-07-18 10:59:36] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 10:59:36] Mot de passe fourni: admin
[2025-07-18 10:59:36] Vérification du mot de passe: réussie
[2025-07-18 10:59:36] Rôle défini dans la base de données: admin
[2025-07-18 10:59:36] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 10:59:36] Connexion réussie! Token JWT généré:
[2025-07-18 10:59:36] User ID: 1
[2025-07-18 10:59:36] Username: admin
[2025-07-18 10:59:36] Rôle: admin
[2025-07-18 10:59:36] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 10:59:53] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 10:59:53] Méthode HTTP: POST
[2025-07-18 10:59:53] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:59:53] Origin: http://localhost:8080
[2025-07-18 10:59:53] Referer: http://localhost:8080/
[2025-07-18 10:59:53] En-têtes de la requête:
[2025-07-18 10:59:53]   Host: localhost
[2025-07-18 10:59:53]   Connection: keep-alive
[2025-07-18 10:59:53]   Content-Length: 39
[2025-07-18 10:59:53]   sec-ch-ua-platform: "Windows"
[2025-07-18 10:59:53]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 10:59:53]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 10:59:53]   Content-Type: application/json
[2025-07-18 10:59:53]   sec-ch-ua-mobile: ?0
[2025-07-18 10:59:53]   Accept: */*
[2025-07-18 10:59:53]   Origin: http://localhost:8080
[2025-07-18 10:59:53]   Sec-Fetch-Site: same-site
[2025-07-18 10:59:53]   Sec-Fetch-Mode: cors
[2025-07-18 10:59:53]   Sec-Fetch-Dest: empty
[2025-07-18 10:59:53]   Referer: http://localhost:8080/
[2025-07-18 10:59:53]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 10:59:53]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 10:59:53] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 10:59:53] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 10:59:53] Connexion à la base de données réussie
[2025-07-18 10:59:53] Recherche de l'utilisateur: trouvé
[2025-07-18 10:59:53] ID utilisateur: 1
[2025-07-18 10:59:53] Rôle: admin
[2025-07-18 10:59:53] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 10:59:53] Mot de passe fourni: admin
[2025-07-18 10:59:53] Vérification du mot de passe: réussie
[2025-07-18 10:59:53] Rôle défini dans la base de données: admin
[2025-07-18 10:59:53] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 10:59:53] Connexion réussie! Token JWT généré:
[2025-07-18 10:59:53] User ID: 1
[2025-07-18 10:59:53] Username: admin
[2025-07-18 10:59:53] Rôle: admin
[2025-07-18 10:59:53] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:07:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:07:38] Méthode HTTP: POST
[2025-07-18 11:07:38] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:07:38] Origin: http://localhost:8080
[2025-07-18 11:07:38] Referer: http://localhost:8080/
[2025-07-18 11:07:38] En-têtes de la requête:
[2025-07-18 11:07:38]   Host: localhost
[2025-07-18 11:07:38]   Connection: keep-alive
[2025-07-18 11:07:38]   Content-Length: 39
[2025-07-18 11:07:38]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:07:38]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:07:38]   Accept: application/json
[2025-07-18 11:07:38]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:07:38]   Content-Type: application/json
[2025-07-18 11:07:38]   sec-ch-ua-mobile: ?0
[2025-07-18 11:07:38]   Origin: http://localhost:8080
[2025-07-18 11:07:38]   Sec-Fetch-Site: same-site
[2025-07-18 11:07:38]   Sec-Fetch-Mode: cors
[2025-07-18 11:07:38]   Sec-Fetch-Dest: empty
[2025-07-18 11:07:38]   Referer: http://localhost:8080/
[2025-07-18 11:07:38]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:07:38]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:07:38] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:07:38] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:07:38] Connexion à la base de données réussie
[2025-07-18 11:07:38] Recherche de l'utilisateur: trouvé
[2025-07-18 11:07:38] ID utilisateur: 1
[2025-07-18 11:07:38] Rôle: admin
[2025-07-18 11:07:38] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:07:38] Mot de passe fourni: admin
[2025-07-18 11:07:38] Vérification du mot de passe: réussie
[2025-07-18 11:07:38] Rôle défini dans la base de données: admin
[2025-07-18 11:07:38] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:07:38] Connexion réussie! Token JWT généré:
[2025-07-18 11:07:38] User ID: 1
[2025-07-18 11:07:38] Username: admin
[2025-07-18 11:07:38] Rôle: admin
[2025-07-18 11:07:38] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:07:53] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:07:53] Méthode HTTP: POST
[2025-07-18 11:07:53] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:07:53] Origin: http://localhost:8080
[2025-07-18 11:07:53] Referer: http://localhost:8080/
[2025-07-18 11:07:53] En-têtes de la requête:
[2025-07-18 11:07:53]   Host: localhost
[2025-07-18 11:07:53]   Connection: keep-alive
[2025-07-18 11:07:53]   Content-Length: 42
[2025-07-18 11:07:53]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:07:53]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:07:53]   Accept: application/json
[2025-07-18 11:07:53]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:07:53]   Content-Type: application/json
[2025-07-18 11:07:53]   sec-ch-ua-mobile: ?0
[2025-07-18 11:07:53]   Origin: http://localhost:8080
[2025-07-18 11:07:53]   Sec-Fetch-Site: same-site
[2025-07-18 11:07:53]   Sec-Fetch-Mode: cors
[2025-07-18 11:07:53]   Sec-Fetch-Dest: empty
[2025-07-18 11:07:53]   Referer: http://localhost:8080/
[2025-07-18 11:07:53]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:07:53]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:07:53] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-18 11:07:53] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:07:53] Connexion à la base de données réussie
[2025-07-18 11:07:53] Recherche de l'utilisateur: trouvé
[2025-07-18 11:07:53] ID utilisateur: 1
[2025-07-18 11:07:53] Rôle: admin
[2025-07-18 11:07:53] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:07:54] Mot de passe fourni: admin123
[2025-07-18 11:07:54] Vérification du mot de passe: échouée
[2025-07-18 11:07:54] Échec: Mot de passe incorrect
[2025-07-18 11:08:06] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:08:06] Méthode HTTP: POST
[2025-07-18 11:08:06] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:08:06] Origin: http://localhost:8080
[2025-07-18 11:08:06] Referer: http://localhost:8080/
[2025-07-18 11:08:06] En-têtes de la requête:
[2025-07-18 11:08:06]   Host: localhost
[2025-07-18 11:08:06]   Connection: keep-alive
[2025-07-18 11:08:06]   Content-Length: 39
[2025-07-18 11:08:06]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:08:06]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:08:06]   Accept: application/json
[2025-07-18 11:08:06]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:08:06]   Content-Type: application/json
[2025-07-18 11:08:06]   sec-ch-ua-mobile: ?0
[2025-07-18 11:08:06]   Origin: http://localhost:8080
[2025-07-18 11:08:06]   Sec-Fetch-Site: same-site
[2025-07-18 11:08:06]   Sec-Fetch-Mode: cors
[2025-07-18 11:08:06]   Sec-Fetch-Dest: empty
[2025-07-18 11:08:06]   Referer: http://localhost:8080/
[2025-07-18 11:08:06]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:08:06]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:08:06] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:08:06] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:08:06] Connexion à la base de données réussie
[2025-07-18 11:08:06] Recherche de l'utilisateur: trouvé
[2025-07-18 11:08:06] ID utilisateur: 1
[2025-07-18 11:08:06] Rôle: admin
[2025-07-18 11:08:06] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:08:06] Mot de passe fourni: admin
[2025-07-18 11:08:06] Vérification du mot de passe: réussie
[2025-07-18 11:08:06] Rôle défini dans la base de données: admin
[2025-07-18 11:08:06] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:08:06] Connexion réussie! Token JWT généré:
[2025-07-18 11:08:06] User ID: 1
[2025-07-18 11:08:06] Username: admin
[2025-07-18 11:08:06] Rôle: admin
[2025-07-18 11:08:06] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:14:36] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:14:36] Méthode HTTP: POST
[2025-07-18 11:14:36] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:14:36] Origin: http://localhost:8080
[2025-07-18 11:14:36] Referer: http://localhost:8080/
[2025-07-18 11:14:36] En-têtes de la requête:
[2025-07-18 11:14:36]   Host: localhost
[2025-07-18 11:14:36]   Connection: keep-alive
[2025-07-18 11:14:36]   Content-Length: 39
[2025-07-18 11:14:36]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:14:36]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:14:36]   Accept: application/json
[2025-07-18 11:14:36]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:14:36]   Content-Type: application/json
[2025-07-18 11:14:36]   sec-ch-ua-mobile: ?0
[2025-07-18 11:14:36]   Origin: http://localhost:8080
[2025-07-18 11:14:36]   Sec-Fetch-Site: same-site
[2025-07-18 11:14:36]   Sec-Fetch-Mode: cors
[2025-07-18 11:14:36]   Sec-Fetch-Dest: empty
[2025-07-18 11:14:36]   Referer: http://localhost:8080/
[2025-07-18 11:14:36]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:14:36]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:14:36] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:14:36] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:14:36] Connexion à la base de données réussie
[2025-07-18 11:14:36] Recherche de l'utilisateur: trouvé
[2025-07-18 11:14:36] ID utilisateur: 1
[2025-07-18 11:14:36] Rôle: admin
[2025-07-18 11:14:36] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:14:36] Mot de passe fourni: admin
[2025-07-18 11:14:36] Vérification du mot de passe: réussie
[2025-07-18 11:14:36] Rôle défini dans la base de données: admin
[2025-07-18 11:14:36] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:14:36] Connexion réussie! Token JWT généré:
[2025-07-18 11:14:36] User ID: 1
[2025-07-18 11:14:36] Username: admin
[2025-07-18 11:14:36] Rôle: admin
[2025-07-18 11:14:36] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:14:36] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:14:36] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMDA3NiwiZXhwIjoxNzUyOTE2NDc2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.LxrkhFNrIrXttSGl7bPepneKmIfl3SCc1ypjBt2NkI8"}
[2025-07-18 11:16:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:16:42] Méthode HTTP: GET
[2025-07-18 11:16:42] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:16:42] Origin: Non défini
[2025-07-18 11:16:42] Referer: Non défini
[2025-07-18 11:16:42] En-têtes de la requête:
[2025-07-18 11:16:42]   Host: localhost
[2025-07-18 11:16:42]   Connection: keep-alive
[2025-07-18 11:16:42]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:16:42]   sec-ch-ua-mobile: ?0
[2025-07-18 11:16:42]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:16:42]   Upgrade-Insecure-Requests: 1
[2025-07-18 11:16:42]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:16:42]   Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
[2025-07-18 11:16:42]   Sec-Fetch-Site: none
[2025-07-18 11:16:42]   Sec-Fetch-Mode: navigate
[2025-07-18 11:16:42]   Sec-Fetch-User: ?1
[2025-07-18 11:16:42]   Sec-Fetch-Dest: document
[2025-07-18 11:16:42]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:16:42]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:16:42]   Cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 11:16:42] Méthode non autorisée: GET
[2025-07-18 11:18:11] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:18:11] Méthode HTTP: POST
[2025-07-18 11:18:11] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:18:11] Origin: http://localhost:8080
[2025-07-18 11:18:11] Referer: http://localhost:8080/login
[2025-07-18 11:18:11] En-têtes de la requête:
[2025-07-18 11:18:11]   host: localhost
[2025-07-18 11:18:11]   connection: close
[2025-07-18 11:18:11]   content-length: 39
[2025-07-18 11:18:11]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:18:11]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:18:11]   accept: application/json
[2025-07-18 11:18:11]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:18:11]   content-type: application/json
[2025-07-18 11:18:11]   sec-ch-ua-mobile: ?0
[2025-07-18 11:18:11]   origin: http://localhost:8080
[2025-07-18 11:18:11]   sec-fetch-site: same-origin
[2025-07-18 11:18:11]   sec-fetch-mode: cors
[2025-07-18 11:18:11]   sec-fetch-dest: empty
[2025-07-18 11:18:11]   referer: http://localhost:8080/login
[2025-07-18 11:18:11]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 11:18:11]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:18:11]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 11:18:11] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:18:11] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:18:11] Connexion à la base de données réussie
[2025-07-18 11:18:11] Recherche de l'utilisateur: trouvé
[2025-07-18 11:18:11] ID utilisateur: 1
[2025-07-18 11:18:11] Rôle: admin
[2025-07-18 11:18:11] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:18:11] Mot de passe fourni: admin
[2025-07-18 11:18:11] Vérification du mot de passe: réussie
[2025-07-18 11:18:11] Rôle défini dans la base de données: admin
[2025-07-18 11:18:11] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:18:11] Connexion réussie! Token JWT généré:
[2025-07-18 11:18:11] User ID: 1
[2025-07-18 11:18:11] Username: admin
[2025-07-18 11:18:11] Rôle: admin
[2025-07-18 11:18:11] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:18:11] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:18:11] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMDI5MSwiZXhwIjoxNzUyOTE2NjkxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.jJ25_PB2iDX-E_KRX8PszVgWlUHiLSYoeKi_3afb4XY"}
[2025-07-18 11:20:57] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:20:57] Méthode HTTP: POST
[2025-07-18 11:20:57] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:20:57] Origin: http://localhost:8081
[2025-07-18 11:20:57] Referer: http://localhost:8081/
[2025-07-18 11:20:57] En-têtes de la requête:
[2025-07-18 11:20:57]   Host: localhost
[2025-07-18 11:20:57]   Connection: keep-alive
[2025-07-18 11:20:57]   Content-Length: 39
[2025-07-18 11:20:57]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:20:57]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:20:57]   Accept: application/json
[2025-07-18 11:20:57]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:20:57]   Content-Type: application/json
[2025-07-18 11:20:57]   sec-ch-ua-mobile: ?0
[2025-07-18 11:20:57]   Origin: http://localhost:8081
[2025-07-18 11:20:57]   Sec-Fetch-Site: same-site
[2025-07-18 11:20:57]   Sec-Fetch-Mode: cors
[2025-07-18 11:20:57]   Sec-Fetch-Dest: empty
[2025-07-18 11:20:57]   Referer: http://localhost:8081/
[2025-07-18 11:20:57]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:20:57]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:20:57] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:20:57] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:20:57] Connexion à la base de données réussie
[2025-07-18 11:20:57] Recherche de l'utilisateur: trouvé
[2025-07-18 11:20:57] ID utilisateur: 1
[2025-07-18 11:20:57] Rôle: admin
[2025-07-18 11:20:57] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:20:58] Mot de passe fourni: admin
[2025-07-18 11:20:58] Vérification du mot de passe: réussie
[2025-07-18 11:20:58] Rôle défini dans la base de données: admin
[2025-07-18 11:20:58] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:20:58] Connexion réussie! Token JWT généré:
[2025-07-18 11:20:58] User ID: 1
[2025-07-18 11:20:58] Username: admin
[2025-07-18 11:20:58] Rôle: admin
[2025-07-18 11:20:58] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:20:58] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:20:58] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMDQ1OCwiZXhwIjoxNzUyOTE2ODU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.83wHaGEZwPX-JldLMvig6G2YmTXbSN7C2YF5rqMcVjY"}
[2025-07-18 11:21:12] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:21:12] Méthode HTTP: POST
[2025-07-18 11:21:13] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:21:13] Origin: http://localhost:8081
[2025-07-18 11:21:13] Referer: http://localhost:8081/
[2025-07-18 11:21:13] En-têtes de la requête:
[2025-07-18 11:21:13]   Host: localhost
[2025-07-18 11:21:13]   Connection: keep-alive
[2025-07-18 11:21:13]   Content-Length: 46
[2025-07-18 11:21:13]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:21:13]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:21:13]   Accept: application/json
[2025-07-18 11:21:13]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:21:13]   Content-Type: application/json
[2025-07-18 11:21:13]   sec-ch-ua-mobile: ?0
[2025-07-18 11:21:13]   Origin: http://localhost:8081
[2025-07-18 11:21:13]   Sec-Fetch-Site: same-site
[2025-07-18 11:21:13]   Sec-Fetch-Mode: cors
[2025-07-18 11:21:13]   Sec-Fetch-Dest: empty
[2025-07-18 11:21:13]   Referer: http://localhost:8081/
[2025-07-18 11:21:13]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 11:21:13]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:21:13] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-18 11:21:13] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 11:21:13] Connexion à la base de données réussie
[2025-07-18 11:21:13] Recherche de l'utilisateur: trouvé
[2025-07-18 11:21:13] ID utilisateur: 2
[2025-07-18 11:21:13] Rôle: manager
[2025-07-18 11:21:13] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-18 11:21:13] Mot de passe fourni: manager123
[2025-07-18 11:21:13] Vérification du mot de passe: réussie
[2025-07-18 11:21:13] Rôle défini dans la base de données: manager
[2025-07-18 11:21:13] Connexion réussie! Token JWT généré:
[2025-07-18 11:21:13] User ID: 2
[2025-07-18 11:21:13] Username: manager
[2025-07-18 11:21:13] Rôle: manager
[2025-07-18 11:21:13] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:21:13] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:21:13] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMDQ3MywiZXhwIjoxNzUyOTE2ODczLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.glGIujbVqifOXNXGA6m4Pi_ceYcBoDbnxnU2Wh3U1J4"}
[2025-07-18 11:21:48] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:21:48] Méthode HTTP: POST
[2025-07-18 11:21:48] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:21:48] Origin: http://localhost:8080
[2025-07-18 11:21:48] Referer: http://localhost:8080/login
[2025-07-18 11:21:48] En-têtes de la requête:
[2025-07-18 11:21:48]   host: localhost
[2025-07-18 11:21:48]   connection: close
[2025-07-18 11:21:48]   content-length: 39
[2025-07-18 11:21:48]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:21:48]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:21:48]   accept: application/json
[2025-07-18 11:21:48]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:21:48]   content-type: application/json
[2025-07-18 11:21:48]   sec-ch-ua-mobile: ?0
[2025-07-18 11:21:48]   origin: http://localhost:8080
[2025-07-18 11:21:48]   sec-fetch-site: same-origin
[2025-07-18 11:21:48]   sec-fetch-mode: cors
[2025-07-18 11:21:48]   sec-fetch-dest: empty
[2025-07-18 11:21:48]   referer: http://localhost:8080/login
[2025-07-18 11:21:48]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 11:21:48]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:21:48]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 11:21:48] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:21:48] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:21:48] Connexion à la base de données réussie
[2025-07-18 11:21:48] Recherche de l'utilisateur: trouvé
[2025-07-18 11:21:48] ID utilisateur: 1
[2025-07-18 11:21:48] Rôle: admin
[2025-07-18 11:21:48] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:21:48] Mot de passe fourni: admin
[2025-07-18 11:21:48] Vérification du mot de passe: réussie
[2025-07-18 11:21:48] Rôle défini dans la base de données: admin
[2025-07-18 11:21:48] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:21:48] Connexion réussie! Token JWT généré:
[2025-07-18 11:21:48] User ID: 1
[2025-07-18 11:21:48] Username: admin
[2025-07-18 11:21:48] Rôle: admin
[2025-07-18 11:21:48] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:21:48] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:21:48] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMDUwOCwiZXhwIjoxNzUyOTE2OTA4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.bdVMK12Nr8dfjtEX7EYZDqKs5kYvYW_g7UUDUU12fYI"}
[2025-07-18 11:30:09] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:30:09] Méthode HTTP: POST
[2025-07-18 11:30:09] User-Agent: Non défini
[2025-07-18 11:30:09] Origin: Non défini
[2025-07-18 11:30:09] Referer: Non défini
[2025-07-18 11:30:09] En-têtes de la requête:
[2025-07-18 11:30:09]   Host: localhost
[2025-07-18 11:30:09]   Connection: close
[2025-07-18 11:30:09]   Content-Length: 39
[2025-07-18 11:30:09]   Content-Type: application/json
[2025-07-18 11:30:09] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:30:09] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:30:09] Connexion à la base de données réussie
[2025-07-18 11:30:09] Recherche de l'utilisateur: trouvé
[2025-07-18 11:30:09] ID utilisateur: 1
[2025-07-18 11:30:09] Rôle: admin
[2025-07-18 11:30:09] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:30:09] Mot de passe fourni: admin
[2025-07-18 11:30:09] Vérification du mot de passe: réussie
[2025-07-18 11:30:09] Rôle défini dans la base de données: admin
[2025-07-18 11:30:09] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:30:09] Connexion réussie! Token JWT généré:
[2025-07-18 11:30:09] User ID: 1
[2025-07-18 11:30:09] Username: admin
[2025-07-18 11:30:09] Rôle: admin
[2025-07-18 11:30:09] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:30:09] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:30:09] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMTAwOSwiZXhwIjoxNzUyOTE3NDA5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.d82h5yFQMZShrYywUTQgGYf8byZbhVphgRgUIbL2V9Q"}
[2025-07-18 11:33:16] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:33:16] Méthode HTTP: POST
[2025-07-18 11:33:16] User-Agent: Non défini
[2025-07-18 11:33:16] Origin: Non défini
[2025-07-18 11:33:16] Referer: Non défini
[2025-07-18 11:33:16] En-têtes de la requête:
[2025-07-18 11:33:16]   Host: localhost
[2025-07-18 11:33:16]   Connection: close
[2025-07-18 11:33:16]   Content-Length: 39
[2025-07-18 11:33:16]   Content-Type: application/json
[2025-07-18 11:33:16] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:33:16] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:33:16] Connexion à la base de données réussie
[2025-07-18 11:33:16] Recherche de l'utilisateur: trouvé
[2025-07-18 11:33:16] ID utilisateur: 1
[2025-07-18 11:33:16] Rôle: admin
[2025-07-18 11:33:16] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:33:16] Mot de passe fourni: admin
[2025-07-18 11:33:16] Vérification du mot de passe: réussie
[2025-07-18 11:33:16] Rôle défini dans la base de données: admin
[2025-07-18 11:33:16] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:33:16] Connexion réussie! Token JWT généré:
[2025-07-18 11:33:16] User ID: 1
[2025-07-18 11:33:16] Username: admin
[2025-07-18 11:33:16] Rôle: admin
[2025-07-18 11:33:16] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:33:16] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:33:16] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMTE5NiwiZXhwIjoxNzUyOTE3NTk2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.tx3CCa2EtJ77b7UrGETRwE_xxxdTOUMKU6n2tT-GOJo"}
[2025-07-18 11:34:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:34:51] Méthode HTTP: POST
[2025-07-18 11:34:51] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:34:51] Origin: http://localhost:8080
[2025-07-18 11:34:51] Referer: http://localhost:8080/login
[2025-07-18 11:34:51] En-têtes de la requête:
[2025-07-18 11:34:51]   host: localhost
[2025-07-18 11:34:51]   connection: close
[2025-07-18 11:34:51]   content-length: 39
[2025-07-18 11:34:51]   sec-ch-ua-platform: "Windows"
[2025-07-18 11:34:51]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 11:34:51]   accept: application/json
[2025-07-18 11:34:51]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 11:34:51]   content-type: application/json
[2025-07-18 11:34:51]   sec-ch-ua-mobile: ?0
[2025-07-18 11:34:51]   origin: http://localhost:8080
[2025-07-18 11:34:51]   sec-fetch-site: same-origin
[2025-07-18 11:34:51]   sec-fetch-mode: cors
[2025-07-18 11:34:51]   sec-fetch-dest: empty
[2025-07-18 11:34:51]   referer: http://localhost:8080/login
[2025-07-18 11:34:51]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 11:34:51]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 11:34:51]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 11:34:51] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:34:51] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:34:51] Connexion à la base de données réussie
[2025-07-18 11:34:51] Recherche de l'utilisateur: trouvé
[2025-07-18 11:34:51] ID utilisateur: 1
[2025-07-18 11:34:51] Rôle: admin
[2025-07-18 11:34:51] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:34:51] Mot de passe fourni: admin
[2025-07-18 11:34:51] Vérification du mot de passe: réussie
[2025-07-18 11:34:51] Rôle défini dans la base de données: admin
[2025-07-18 11:34:51] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:34:51] Connexion réussie! Token JWT généré:
[2025-07-18 11:34:51] User ID: 1
[2025-07-18 11:34:51] Username: admin
[2025-07-18 11:34:51] Rôle: admin
[2025-07-18 11:34:51] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:34:51] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:34:51] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMTI5MSwiZXhwIjoxNzUyOTE3NjkxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.c9erNjrIbM9UJfbb224gyDzTtkxBLq1UBAmW80lPOOE"}
[2025-07-18 11:52:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 11:52:55] Méthode HTTP: POST
[2025-07-18 11:52:55] User-Agent: Non défini
[2025-07-18 11:52:55] Origin: Non défini
[2025-07-18 11:52:55] Referer: Non défini
[2025-07-18 11:52:55] En-têtes de la requête:
[2025-07-18 11:52:55]   Host: localhost
[2025-07-18 11:52:55]   Connection: close
[2025-07-18 11:52:55]   Content-Length: 39
[2025-07-18 11:52:55]   Content-Type: application/json
[2025-07-18 11:52:55] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 11:52:55] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 11:52:55] Connexion à la base de données réussie
[2025-07-18 11:52:55] Recherche de l'utilisateur: trouvé
[2025-07-18 11:52:55] ID utilisateur: 1
[2025-07-18 11:52:55] Rôle: admin
[2025-07-18 11:52:55] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 11:52:56] Mot de passe fourni: admin
[2025-07-18 11:52:56] Vérification du mot de passe: réussie
[2025-07-18 11:52:56] Rôle défini dans la base de données: admin
[2025-07-18 11:52:56] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 11:52:56] Connexion réussie! Token JWT généré:
[2025-07-18 11:52:56] User ID: 1
[2025-07-18 11:52:56] Username: admin
[2025-07-18 11:52:56] Rôle: admin
[2025-07-18 11:52:56] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 11:52:56] Préparation de l'envoi de la réponse JSON:
[2025-07-18 11:52:56] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMjM3NiwiZXhwIjoxNzUyOTE4Nzc2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.fetmPp_QV2qxLzKrNzyi99fkWC1Kl1rWxtnsNvpKte4"}
[2025-07-18 12:05:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 12:05:51] Méthode HTTP: POST
[2025-07-18 12:05:51] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:05:51] Origin: http://localhost:8080
[2025-07-18 12:05:51] Referer: http://localhost:8080/login
[2025-07-18 12:05:51] En-têtes de la requête:
[2025-07-18 12:05:51]   host: localhost
[2025-07-18 12:05:51]   connection: close
[2025-07-18 12:05:51]   content-length: 46
[2025-07-18 12:05:51]   sec-ch-ua-platform: "Windows"
[2025-07-18 12:05:51]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:05:51]   accept: application/json
[2025-07-18 12:05:51]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"
[2025-07-18 12:05:51]   content-type: application/json
[2025-07-18 12:05:51]   sec-ch-ua-mobile: ?0
[2025-07-18 12:05:51]   sec-gpc: 1
[2025-07-18 12:05:51]   accept-language: fr-FR,fr;q=0.9
[2025-07-18 12:05:51]   origin: http://localhost:8080
[2025-07-18 12:05:51]   sec-fetch-site: same-origin
[2025-07-18 12:05:51]   sec-fetch-mode: cors
[2025-07-18 12:05:51]   sec-fetch-dest: empty
[2025-07-18 12:05:51]   referer: http://localhost:8080/login
[2025-07-18 12:05:51]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 12:05:51] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-18 12:05:51] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 12:05:51] Connexion à la base de données réussie
[2025-07-18 12:05:51] Recherche de l'utilisateur: trouvé
[2025-07-18 12:05:51] ID utilisateur: 2
[2025-07-18 12:05:51] Rôle: manager
[2025-07-18 12:05:51] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-18 12:05:51] Mot de passe fourni: manager123
[2025-07-18 12:05:51] Vérification du mot de passe: réussie
[2025-07-18 12:05:51] Rôle défini dans la base de données: manager
[2025-07-18 12:05:51] Connexion réussie! Token JWT généré:
[2025-07-18 12:05:51] User ID: 2
[2025-07-18 12:05:51] Username: manager
[2025-07-18 12:05:51] Rôle: manager
[2025-07-18 12:05:51] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 12:05:51] Préparation de l'envoi de la réponse JSON:
[2025-07-18 12:05:51] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMzE1MSwiZXhwIjoxNzUyOTE5NTUxLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.MUrn3hdkCKZwg24xMky8X_ddKqsyFOwSZVPnTjyAqps"}
[2025-07-18 12:47:20] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 12:47:20] Méthode HTTP: POST
[2025-07-18 12:47:20] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:47:20] Origin: http://localhost:8080
[2025-07-18 12:47:20] Referer: http://localhost:8080/login
[2025-07-18 12:47:20] En-têtes de la requête:
[2025-07-18 12:47:20]   host: localhost
[2025-07-18 12:47:20]   connection: close
[2025-07-18 12:47:20]   content-length: 39
[2025-07-18 12:47:20]   sec-ch-ua-platform: "Windows"
[2025-07-18 12:47:20]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzMTI5MSwiZXhwIjoxNzUyOTE3NjkxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.c9erNjrIbM9UJfbb224gyDzTtkxBLq1UBAmW80lPOOE
[2025-07-18 12:47:20]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 12:47:20]   sec-ch-ua-mobile: ?0
[2025-07-18 12:47:20]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:47:20]   accept: application/json
[2025-07-18 12:47:20]   x-kl-ajax-request: Ajax_Request
[2025-07-18 12:47:20]   content-type: application/json
[2025-07-18 12:47:20]   origin: http://localhost:8080
[2025-07-18 12:47:20]   sec-fetch-site: same-origin
[2025-07-18 12:47:20]   sec-fetch-mode: cors
[2025-07-18 12:47:20]   sec-fetch-dest: empty
[2025-07-18 12:47:20]   referer: http://localhost:8080/login
[2025-07-18 12:47:20]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 12:47:20]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 12:47:20]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 12:47:20] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 12:47:20] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 12:47:20] Connexion à la base de données réussie
[2025-07-18 12:47:20] Recherche de l'utilisateur: trouvé
[2025-07-18 12:47:20] ID utilisateur: 1
[2025-07-18 12:47:20] Rôle: admin
[2025-07-18 12:47:20] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 12:47:20] Mot de passe fourni: admin
[2025-07-18 12:47:20] Vérification du mot de passe: réussie
[2025-07-18 12:47:20] Rôle défini dans la base de données: admin
[2025-07-18 12:47:20] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 12:47:20] Connexion réussie! Token JWT généré:
[2025-07-18 12:47:20] User ID: 1
[2025-07-18 12:47:20] Username: admin
[2025-07-18 12:47:20] Rôle: admin
[2025-07-18 12:47:20] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 12:47:20] Préparation de l'envoi de la réponse JSON:
[2025-07-18 12:47:20] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNTY0MCwiZXhwIjoxNzUyOTIyMDQwLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.BFzsZgdaNalWUlK0F7HDeamhXP04xheI9ZOapLutw2Y"}
[2025-07-18 12:47:33] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 12:47:33] Méthode HTTP: POST
[2025-07-18 12:47:33] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:47:33] Origin: http://localhost:8080
[2025-07-18 12:47:33] Referer: http://localhost:8080/login
[2025-07-18 12:47:33] En-têtes de la requête:
[2025-07-18 12:47:33]   host: localhost
[2025-07-18 12:47:33]   connection: close
[2025-07-18 12:47:33]   content-length: 39
[2025-07-18 12:47:33]   sec-ch-ua-platform: "Windows"
[2025-07-18 12:47:33]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNTY0MCwiZXhwIjoxNzUyOTIyMDQwLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.BFzsZgdaNalWUlK0F7HDeamhXP04xheI9ZOapLutw2Y
[2025-07-18 12:47:33]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 12:47:33]   sec-ch-ua-mobile: ?0
[2025-07-18 12:47:33]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:47:33]   accept: application/json
[2025-07-18 12:47:33]   x-kl-ajax-request: Ajax_Request
[2025-07-18 12:47:33]   content-type: application/json
[2025-07-18 12:47:33]   origin: http://localhost:8080
[2025-07-18 12:47:33]   sec-fetch-site: same-origin
[2025-07-18 12:47:33]   sec-fetch-mode: cors
[2025-07-18 12:47:33]   sec-fetch-dest: empty
[2025-07-18 12:47:33]   referer: http://localhost:8080/login
[2025-07-18 12:47:33]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 12:47:33]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 12:47:33]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 12:47:33] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 12:47:33] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 12:47:33] Connexion à la base de données réussie
[2025-07-18 12:47:33] Recherche de l'utilisateur: trouvé
[2025-07-18 12:47:33] ID utilisateur: 1
[2025-07-18 12:47:33] Rôle: admin
[2025-07-18 12:47:33] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 12:47:33] Mot de passe fourni: admin
[2025-07-18 12:47:33] Vérification du mot de passe: réussie
[2025-07-18 12:47:33] Rôle défini dans la base de données: admin
[2025-07-18 12:47:33] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 12:47:33] Connexion réussie! Token JWT généré:
[2025-07-18 12:47:33] User ID: 1
[2025-07-18 12:47:33] Username: admin
[2025-07-18 12:47:33] Rôle: admin
[2025-07-18 12:47:33] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 12:47:33] Préparation de l'envoi de la réponse JSON:
[2025-07-18 12:47:33] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNTY1MywiZXhwIjoxNzUyOTIyMDUzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.BGkGoN3KZ6tBosJATmv1JygbBVpRESrHn9eDEgz6ztk"}
[2025-07-18 12:55:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 12:55:52] Méthode HTTP: POST
[2025-07-18 12:55:52] User-Agent: Non défini
[2025-07-18 12:55:52] Origin: Non défini
[2025-07-18 12:55:52] Referer: Non défini
[2025-07-18 12:55:52] En-têtes de la requête:
[2025-07-18 12:55:52]   Host: localhost
[2025-07-18 12:55:52]   Accept: */*
[2025-07-18 12:55:52]   Content-Type: application/json
[2025-07-18 12:55:52]   Content-Length: 39
[2025-07-18 12:55:52] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 12:55:52] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 12:55:52] Connexion à la base de données réussie
[2025-07-18 12:55:52] Recherche de l'utilisateur: trouvé
[2025-07-18 12:55:52] ID utilisateur: 1
[2025-07-18 12:55:52] Rôle: admin
[2025-07-18 12:55:52] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 12:55:52] Mot de passe fourni: admin
[2025-07-18 12:55:52] Vérification du mot de passe: réussie
[2025-07-18 12:55:52] Rôle défini dans la base de données: admin
[2025-07-18 12:55:52] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 12:55:52] Connexion réussie! Token JWT généré:
[2025-07-18 12:55:52] User ID: 1
[2025-07-18 12:55:52] Username: admin
[2025-07-18 12:55:52] Rôle: admin
[2025-07-18 12:55:52] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 12:55:52] Préparation de l'envoi de la réponse JSON:
[2025-07-18 12:55:52] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjE1MiwiZXhwIjoxNzUyOTIyNTUyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.g8BHGSwbmravdkZhHlHJJsgqdJhKQzpG_nWnh9Eryd4"}
[2025-07-18 12:58:22] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 12:58:22] Méthode HTTP: POST
[2025-07-18 12:58:22] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:58:22] Origin: http://localhost:8080
[2025-07-18 12:58:22] Referer: http://localhost:8080/login
[2025-07-18 12:58:22] En-têtes de la requête:
[2025-07-18 12:58:22]   host: localhost
[2025-07-18 12:58:22]   connection: close
[2025-07-18 12:58:22]   content-length: 39
[2025-07-18 12:58:22]   sec-ch-ua-platform: "Windows"
[2025-07-18 12:58:22]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNTY1MywiZXhwIjoxNzUyOTIyMDUzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.BGkGoN3KZ6tBosJATmv1JygbBVpRESrHn9eDEgz6ztk
[2025-07-18 12:58:22]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 12:58:22]   sec-ch-ua-mobile: ?0
[2025-07-18 12:58:22]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 12:58:22]   accept: application/json
[2025-07-18 12:58:22]   x-kl-ajax-request: Ajax_Request
[2025-07-18 12:58:22]   content-type: application/json
[2025-07-18 12:58:22]   origin: http://localhost:8080
[2025-07-18 12:58:22]   sec-fetch-site: same-origin
[2025-07-18 12:58:22]   sec-fetch-mode: cors
[2025-07-18 12:58:22]   sec-fetch-dest: empty
[2025-07-18 12:58:22]   referer: http://localhost:8080/login
[2025-07-18 12:58:22]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 12:58:22]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 12:58:22]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 12:58:22] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 12:58:22] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 12:58:22] Connexion à la base de données réussie
[2025-07-18 12:58:22] Recherche de l'utilisateur: trouvé
[2025-07-18 12:58:22] ID utilisateur: 1
[2025-07-18 12:58:22] Rôle: admin
[2025-07-18 12:58:22] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 12:58:22] Mot de passe fourni: admin
[2025-07-18 12:58:22] Vérification du mot de passe: réussie
[2025-07-18 12:58:22] Rôle défini dans la base de données: admin
[2025-07-18 12:58:22] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 12:58:22] Connexion réussie! Token JWT généré:
[2025-07-18 12:58:22] User ID: 1
[2025-07-18 12:58:22] Username: admin
[2025-07-18 12:58:22] Rôle: admin
[2025-07-18 12:58:22] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 12:58:22] Préparation de l'envoi de la réponse JSON:
[2025-07-18 12:58:22] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjMwMiwiZXhwIjoxNzUyOTIyNzAyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.P088hY8AMlN0H6_WyHbQ6qH5LSWMLxkprSErf89Xdek"}
[2025-07-18 13:01:19] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:01:19] Méthode HTTP: POST
[2025-07-18 13:01:19] User-Agent: Non défini
[2025-07-18 13:01:19] Origin: Non défini
[2025-07-18 13:01:19] Referer: Non défini
[2025-07-18 13:01:19] En-têtes de la requête:
[2025-07-18 13:01:19]   Host: localhost
[2025-07-18 13:01:19]   Accept: */*
[2025-07-18 13:01:19]   Content-Type: application/json
[2025-07-18 13:01:19]   Content-Length: 39
[2025-07-18 13:01:19] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:01:19] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:01:19] Connexion à la base de données réussie
[2025-07-18 13:01:19] Recherche de l'utilisateur: trouvé
[2025-07-18 13:01:19] ID utilisateur: 1
[2025-07-18 13:01:19] Rôle: admin
[2025-07-18 13:01:19] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:01:19] Mot de passe fourni: admin
[2025-07-18 13:01:19] Vérification du mot de passe: réussie
[2025-07-18 13:01:19] Rôle défini dans la base de données: admin
[2025-07-18 13:01:19] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:01:19] Connexion réussie! Token JWT généré:
[2025-07-18 13:01:19] User ID: 1
[2025-07-18 13:01:19] Username: admin
[2025-07-18 13:01:19] Rôle: admin
[2025-07-18 13:01:19] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:01:19] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:01:19] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjQ3OSwiZXhwIjoxNzUyOTIyODc5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.6vNSlA-sMx00uj-6lIfCga7-vi4eTa3Rnqg9SMufyzk"}
[2025-07-18 13:01:53] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:01:53] Méthode HTTP: POST
[2025-07-18 13:01:53] User-Agent: Non défini
[2025-07-18 13:01:53] Origin: Non défini
[2025-07-18 13:01:53] Referer: Non défini
[2025-07-18 13:01:53] En-têtes de la requête:
[2025-07-18 13:01:53]   Host: localhost
[2025-07-18 13:01:53]   Accept: */*
[2025-07-18 13:01:53]   Content-Type: application/json
[2025-07-18 13:01:53]   Content-Length: 39
[2025-07-18 13:01:53] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:01:53] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:01:53] Connexion à la base de données réussie
[2025-07-18 13:01:53] Recherche de l'utilisateur: trouvé
[2025-07-18 13:01:53] ID utilisateur: 1
[2025-07-18 13:01:53] Rôle: admin
[2025-07-18 13:01:53] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:01:53] Mot de passe fourni: admin
[2025-07-18 13:01:53] Vérification du mot de passe: réussie
[2025-07-18 13:01:53] Rôle défini dans la base de données: admin
[2025-07-18 13:01:53] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:01:53] Connexion réussie! Token JWT généré:
[2025-07-18 13:01:53] User ID: 1
[2025-07-18 13:01:53] Username: admin
[2025-07-18 13:01:53] Rôle: admin
[2025-07-18 13:01:53] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:01:53] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:01:53] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjUxMywiZXhwIjoxNzUyOTIyOTEzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.RJKuYkRvGx8DItordKwk5NRXt5ZwCL2UKIHHym3YRtU"}
[2025-07-18 13:08:27] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:08:27] Méthode HTTP: POST
[2025-07-18 13:08:27] User-Agent: Non défini
[2025-07-18 13:08:27] Origin: Non défini
[2025-07-18 13:08:27] Referer: Non défini
[2025-07-18 13:08:27] En-têtes de la requête:
[2025-07-18 13:08:27]   Host: localhost
[2025-07-18 13:08:27]   Accept: */*
[2025-07-18 13:08:27]   Content-Type: application/json
[2025-07-18 13:08:27]   Content-Length: 39
[2025-07-18 13:08:27] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:08:27] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:08:27] Connexion à la base de données réussie
[2025-07-18 13:08:27] Recherche de l'utilisateur: trouvé
[2025-07-18 13:08:27] ID utilisateur: 1
[2025-07-18 13:08:27] Rôle: admin
[2025-07-18 13:08:27] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:08:27] Mot de passe fourni: admin
[2025-07-18 13:08:27] Vérification du mot de passe: réussie
[2025-07-18 13:08:27] Rôle défini dans la base de données: admin
[2025-07-18 13:08:27] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:08:27] Connexion réussie! Token JWT généré:
[2025-07-18 13:08:27] User ID: 1
[2025-07-18 13:08:27] Username: admin
[2025-07-18 13:08:27] Rôle: admin
[2025-07-18 13:08:27] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:08:27] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:08:27] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjkwNywiZXhwIjoxNzUyOTIzMzA3LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.zuW1jNhMseVKrp2jXBp2nHjmkF5MDe1yabS_KzolJkE"}
[2025-07-18 13:09:59] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:09:59] Méthode HTTP: POST
[2025-07-18 13:09:59] User-Agent: Non défini
[2025-07-18 13:09:59] Origin: Non défini
[2025-07-18 13:09:59] Referer: Non défini
[2025-07-18 13:09:59] En-têtes de la requête:
[2025-07-18 13:09:59]   Host: localhost
[2025-07-18 13:09:59]   Accept: */*
[2025-07-18 13:09:59]   Content-Type: application/json
[2025-07-18 13:09:59]   Content-Length: 39
[2025-07-18 13:09:59] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:09:59] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:09:59] Connexion à la base de données réussie
[2025-07-18 13:09:59] Recherche de l'utilisateur: trouvé
[2025-07-18 13:09:59] ID utilisateur: 1
[2025-07-18 13:09:59] Rôle: admin
[2025-07-18 13:09:59] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:09:59] Mot de passe fourni: admin
[2025-07-18 13:09:59] Vérification du mot de passe: réussie
[2025-07-18 13:09:59] Rôle défini dans la base de données: admin
[2025-07-18 13:09:59] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:09:59] Connexion réussie! Token JWT généré:
[2025-07-18 13:09:59] User ID: 1
[2025-07-18 13:09:59] Username: admin
[2025-07-18 13:09:59] Rôle: admin
[2025-07-18 13:09:59] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:09:59] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:09:59] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjk5OSwiZXhwIjoxNzUyOTIzMzk5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.CFe9rG7CJx9PWfOA8B-Qo2akdvVUCK1n4qfDzlbGy4M"}
[2025-07-18 13:12:01] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:12:01] Méthode HTTP: POST
[2025-07-18 13:12:01] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:12:01] Origin: http://localhost:8080
[2025-07-18 13:12:01] Referer: http://localhost:8080/login
[2025-07-18 13:12:01] En-têtes de la requête:
[2025-07-18 13:12:01]   host: localhost
[2025-07-18 13:12:01]   connection: close
[2025-07-18 13:12:01]   content-length: 39
[2025-07-18 13:12:01]   sec-ch-ua-platform: "Windows"
[2025-07-18 13:12:01]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNjMwMiwiZXhwIjoxNzUyOTIyNzAyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.P088hY8AMlN0H6_WyHbQ6qH5LSWMLxkprSErf89Xdek
[2025-07-18 13:12:01]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 13:12:01]   sec-ch-ua-mobile: ?0
[2025-07-18 13:12:01]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:12:01]   accept: application/json
[2025-07-18 13:12:01]   x-kl-ajax-request: Ajax_Request
[2025-07-18 13:12:01]   content-type: application/json
[2025-07-18 13:12:01]   origin: http://localhost:8080
[2025-07-18 13:12:01]   sec-fetch-site: same-origin
[2025-07-18 13:12:01]   sec-fetch-mode: cors
[2025-07-18 13:12:01]   sec-fetch-dest: empty
[2025-07-18 13:12:01]   referer: http://localhost:8080/login
[2025-07-18 13:12:01]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 13:12:01]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 13:12:01]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 13:12:01] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:12:01] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:12:01] Connexion à la base de données réussie
[2025-07-18 13:12:01] Recherche de l'utilisateur: trouvé
[2025-07-18 13:12:01] ID utilisateur: 1
[2025-07-18 13:12:01] Rôle: admin
[2025-07-18 13:12:01] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:12:01] Mot de passe fourni: admin
[2025-07-18 13:12:01] Vérification du mot de passe: réussie
[2025-07-18 13:12:01] Rôle défini dans la base de données: admin
[2025-07-18 13:12:01] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:12:01] Connexion réussie! Token JWT généré:
[2025-07-18 13:12:01] User ID: 1
[2025-07-18 13:12:01] Username: admin
[2025-07-18 13:12:01] Rôle: admin
[2025-07-18 13:12:01] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:12:01] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:12:01] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNzEyMSwiZXhwIjoxNzUyOTIzNTIxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.t-3vo5TbUDBLZRQp5M_OKRRpMUabvUCE6bWNIlh5H_g"}
[2025-07-18 13:15:28] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:15:28] Méthode HTTP: POST
[2025-07-18 13:15:28] User-Agent: Non défini
[2025-07-18 13:15:28] Origin: Non défini
[2025-07-18 13:15:28] Referer: Non défini
[2025-07-18 13:15:28] En-têtes de la requête:
[2025-07-18 13:15:28]   Host: localhost
[2025-07-18 13:15:28]   Accept: */*
[2025-07-18 13:15:28]   Content-Type: application/json
[2025-07-18 13:15:28]   Content-Length: 39
[2025-07-18 13:15:28] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:15:28] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:15:28] Connexion à la base de données réussie
[2025-07-18 13:15:28] Recherche de l'utilisateur: trouvé
[2025-07-18 13:15:28] ID utilisateur: 1
[2025-07-18 13:15:28] Rôle: admin
[2025-07-18 13:15:28] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:15:28] Mot de passe fourni: admin
[2025-07-18 13:15:28] Vérification du mot de passe: réussie
[2025-07-18 13:15:28] Rôle défini dans la base de données: admin
[2025-07-18 13:15:28] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:15:28] Connexion réussie! Token JWT généré:
[2025-07-18 13:15:28] User ID: 1
[2025-07-18 13:15:28] Username: admin
[2025-07-18 13:15:28] Rôle: admin
[2025-07-18 13:15:28] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:15:28] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:15:28] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************.jb2tqT_VofILw2vaBcyWd7enPT_avCii3npIJ-F7REY"}
[2025-07-18 13:16:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:16:52] Méthode HTTP: POST
[2025-07-18 13:16:52] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:16:52] Origin: http://localhost:8080
[2025-07-18 13:16:52] Referer: http://localhost:8080/login
[2025-07-18 13:16:52] En-têtes de la requête:
[2025-07-18 13:16:52]   host: localhost
[2025-07-18 13:16:52]   connection: close
[2025-07-18 13:16:52]   content-length: 39
[2025-07-18 13:16:52]   sec-ch-ua-platform: "Windows"
[2025-07-18 13:16:52]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:16:52]   accept: application/json
[2025-07-18 13:16:52]   x-kl-ajax-request: Ajax_Request
[2025-07-18 13:16:52]   content-type: application/json
[2025-07-18 13:16:52]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 13:16:52]   sec-ch-ua-mobile: ?0
[2025-07-18 13:16:52]   origin: http://localhost:8080
[2025-07-18 13:16:52]   sec-fetch-site: same-origin
[2025-07-18 13:16:52]   sec-fetch-mode: cors
[2025-07-18 13:16:52]   sec-fetch-dest: empty
[2025-07-18 13:16:52]   referer: http://localhost:8080/login
[2025-07-18 13:16:52]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 13:16:52]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 13:16:52]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 13:16:52] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:16:52] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:16:52] Connexion à la base de données réussie
[2025-07-18 13:16:52] Recherche de l'utilisateur: trouvé
[2025-07-18 13:16:52] ID utilisateur: 1
[2025-07-18 13:16:52] Rôle: admin
[2025-07-18 13:16:52] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:16:52] Mot de passe fourni: admin
[2025-07-18 13:16:52] Vérification du mot de passe: réussie
[2025-07-18 13:16:52] Rôle défini dans la base de données: admin
[2025-07-18 13:16:52] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:16:52] Connexion réussie! Token JWT généré:
[2025-07-18 13:16:52] User ID: 1
[2025-07-18 13:16:52] Username: admin
[2025-07-18 13:16:52] Rôle: admin
[2025-07-18 13:16:52] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:16:52] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:16:52] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNzQxMiwiZXhwIjoxNzUyOTIzODEyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.ZJM65iItOhOVtPBZflSFsGA3peKEaEJq0yGoXANA4D4"}
[2025-07-18 13:17:14] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:17:14] Méthode HTTP: POST
[2025-07-18 13:17:14] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:17:14] Origin: http://localhost:8080
[2025-07-18 13:17:14] Referer: http://localhost:8080/login
[2025-07-18 13:17:14] En-têtes de la requête:
[2025-07-18 13:17:14]   host: localhost
[2025-07-18 13:17:14]   connection: close
[2025-07-18 13:17:14]   content-length: 39
[2025-07-18 13:17:14]   sec-ch-ua-platform: "Windows"
[2025-07-18 13:17:14]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNzQxMiwiZXhwIjoxNzUyOTIzODEyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.ZJM65iItOhOVtPBZflSFsGA3peKEaEJq0yGoXANA4D4
[2025-07-18 13:17:14]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 13:17:14]   sec-ch-ua-mobile: ?0
[2025-07-18 13:17:14]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:17:14]   accept: application/json
[2025-07-18 13:17:14]   x-kl-ajax-request: Ajax_Request
[2025-07-18 13:17:14]   content-type: application/json
[2025-07-18 13:17:14]   origin: http://localhost:8080
[2025-07-18 13:17:14]   sec-fetch-site: same-origin
[2025-07-18 13:17:14]   sec-fetch-mode: cors
[2025-07-18 13:17:14]   sec-fetch-dest: empty
[2025-07-18 13:17:14]   referer: http://localhost:8080/login
[2025-07-18 13:17:14]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 13:17:14]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 13:17:14]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 13:17:14] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:17:14] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:17:14] Connexion à la base de données réussie
[2025-07-18 13:17:14] Recherche de l'utilisateur: trouvé
[2025-07-18 13:17:14] ID utilisateur: 1
[2025-07-18 13:17:14] Rôle: admin
[2025-07-18 13:17:14] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:17:14] Mot de passe fourni: admin
[2025-07-18 13:17:14] Vérification du mot de passe: réussie
[2025-07-18 13:17:14] Rôle défini dans la base de données: admin
[2025-07-18 13:17:14] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:17:14] Connexion réussie! Token JWT généré:
[2025-07-18 13:17:14] User ID: 1
[2025-07-18 13:17:14] Username: admin
[2025-07-18 13:17:14] Rôle: admin
[2025-07-18 13:17:14] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:17:14] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:17:14] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNzQzNCwiZXhwIjoxNzUyOTIzODM0LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.tjMQI0tUHRlV4T2Ea_R2QeBxQ9SdGZvaGU89gbdL6xI"}
[2025-07-18 13:22:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:22:51] Méthode HTTP: POST
[2025-07-18 13:22:51] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:22:51] Origin: http://localhost:8080
[2025-07-18 13:22:51] Referer: http://localhost:8080/login
[2025-07-18 13:22:51] En-têtes de la requête:
[2025-07-18 13:22:51]   host: localhost
[2025-07-18 13:22:51]   connection: close
[2025-07-18 13:22:51]   content-length: 39
[2025-07-18 13:22:51]   sec-ch-ua-platform: "Windows"
[2025-07-18 13:22:51]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:22:51]   accept: application/json
[2025-07-18 13:22:51]   x-kl-ajax-request: Ajax_Request
[2025-07-18 13:22:51]   content-type: application/json
[2025-07-18 13:22:51]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 13:22:51]   sec-ch-ua-mobile: ?0
[2025-07-18 13:22:51]   origin: http://localhost:8080
[2025-07-18 13:22:51]   sec-fetch-site: same-origin
[2025-07-18 13:22:51]   sec-fetch-mode: cors
[2025-07-18 13:22:51]   sec-fetch-dest: empty
[2025-07-18 13:22:51]   referer: http://localhost:8080/login
[2025-07-18 13:22:51]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 13:22:51]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 13:22:51]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 13:22:51] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:22:51] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:22:51] Connexion à la base de données réussie
[2025-07-18 13:22:51] Recherche de l'utilisateur: trouvé
[2025-07-18 13:22:51] ID utilisateur: 1
[2025-07-18 13:22:51] Rôle: admin
[2025-07-18 13:22:51] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:22:51] Mot de passe fourni: admin
[2025-07-18 13:22:51] Vérification du mot de passe: réussie
[2025-07-18 13:22:51] Rôle défini dans la base de données: admin
[2025-07-18 13:22:51] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:22:51] Connexion réussie! Token JWT généré:
[2025-07-18 13:22:51] User ID: 1
[2025-07-18 13:22:51] Username: admin
[2025-07-18 13:22:51] Rôle: admin
[2025-07-18 13:22:51] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:22:51] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:22:51] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNzc3MSwiZXhwIjoxNzUyOTI0MTcxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.KxyuByqFTvjFpS4VjGn1Yh4IQTPA6bONvlfWTW31iSU"}
[2025-07-18 13:55:47] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 13:55:47] Méthode HTTP: POST
[2025-07-18 13:55:47] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:55:47] Origin: http://localhost:8080
[2025-07-18 13:55:47] Referer: http://localhost:8080/login
[2025-07-18 13:55:47] En-têtes de la requête:
[2025-07-18 13:55:47]   host: localhost
[2025-07-18 13:55:47]   connection: close
[2025-07-18 13:55:47]   content-length: 39
[2025-07-18 13:55:47]   sec-ch-ua-platform: "Windows"
[2025-07-18 13:55:47]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzNzc3MSwiZXhwIjoxNzUyOTI0MTcxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.KxyuByqFTvjFpS4VjGn1Yh4IQTPA6bONvlfWTW31iSU
[2025-07-18 13:55:47]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 13:55:47]   sec-ch-ua-mobile: ?0
[2025-07-18 13:55:47]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 13:55:47]   accept: application/json
[2025-07-18 13:55:47]   x-kl-ajax-request: Ajax_Request
[2025-07-18 13:55:47]   content-type: application/json
[2025-07-18 13:55:47]   origin: http://localhost:8080
[2025-07-18 13:55:47]   sec-fetch-site: same-origin
[2025-07-18 13:55:47]   sec-fetch-mode: cors
[2025-07-18 13:55:47]   sec-fetch-dest: empty
[2025-07-18 13:55:47]   referer: http://localhost:8080/login
[2025-07-18 13:55:47]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 13:55:47]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 13:55:47]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 13:55:47] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 13:55:47] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 13:55:47] Connexion à la base de données réussie
[2025-07-18 13:55:47] Recherche de l'utilisateur: trouvé
[2025-07-18 13:55:47] ID utilisateur: 1
[2025-07-18 13:55:47] Rôle: admin
[2025-07-18 13:55:47] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 13:55:48] Mot de passe fourni: admin
[2025-07-18 13:55:48] Vérification du mot de passe: réussie
[2025-07-18 13:55:48] Rôle défini dans la base de données: admin
[2025-07-18 13:55:48] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 13:55:48] Connexion réussie! Token JWT généré:
[2025-07-18 13:55:48] User ID: 1
[2025-07-18 13:55:48] Username: admin
[2025-07-18 13:55:48] Rôle: admin
[2025-07-18 13:55:48] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 13:55:48] Préparation de l'envoi de la réponse JSON:
[2025-07-18 13:55:48] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjgzOTc0OCwiZXhwIjoxNzUyOTI2MTQ4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.L5xja5oSm1oZjEcbYacUDeyJ6Yc0VfN-cVXRLzrPAA8"}
[2025-07-18 15:42:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 15:42:18] Méthode HTTP: POST
[2025-07-18 15:42:18] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 15:42:18] Origin: http://localhost:8080
[2025-07-18 15:42:18] Referer: http://localhost:8080/login
[2025-07-18 15:42:18] En-têtes de la requête:
[2025-07-18 15:42:18]   host: localhost
[2025-07-18 15:42:18]   connection: close
[2025-07-18 15:42:18]   content-length: 46
[2025-07-18 15:42:18]   sec-ch-ua-platform: "Windows"
[2025-07-18 15:42:18]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MjY2MzEzNiwiZXhwIjoxNzUyNzQ5NTM2LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoic3VwZXJ2aXNldXIifX0.eJ9pxJ_9T96e69ps68n_PoeuHFRWwNFurFBFsWCzoRE
[2025-07-18 15:42:18]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 15:42:18]   sec-ch-ua-mobile: ?0
[2025-07-18 15:42:18]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 15:42:18]   accept: application/json
[2025-07-18 15:42:18]   x-kl-ajax-request: Ajax_Request
[2025-07-18 15:42:18]   content-type: application/json
[2025-07-18 15:42:18]   origin: http://localhost:8080
[2025-07-18 15:42:18]   sec-fetch-site: same-origin
[2025-07-18 15:42:18]   sec-fetch-mode: cors
[2025-07-18 15:42:18]   sec-fetch-dest: empty
[2025-07-18 15:42:18]   referer: http://localhost:8080/login
[2025-07-18 15:42:18]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 15:42:18]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 15:42:18] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-18 15:42:18] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 15:42:18] Connexion à la base de données réussie
[2025-07-18 15:42:18] Recherche de l'utilisateur: trouvé
[2025-07-18 15:42:18] ID utilisateur: 2
[2025-07-18 15:42:18] Rôle: manager
[2025-07-18 15:42:18] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-18 15:42:18] Mot de passe fourni: manager123
[2025-07-18 15:42:18] Vérification du mot de passe: réussie
[2025-07-18 15:42:18] Rôle défini dans la base de données: manager
[2025-07-18 15:42:18] Connexion réussie! Token JWT généré:
[2025-07-18 15:42:18] User ID: 2
[2025-07-18 15:42:18] Username: manager
[2025-07-18 15:42:18] Rôle: manager
[2025-07-18 15:42:18] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 15:42:18] Préparation de l'envoi de la réponse JSON:
[2025-07-18 15:42:18] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg0NjEzOCwiZXhwIjoxNzUyOTMyNTM4LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.Ri50yDLcw--x__CA9EPYO6AVpTlhrrWr0o3b_Fjn7Dk"}
[2025-07-18 15:46:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 15:46:51] Méthode HTTP: POST
[2025-07-18 15:46:51] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 15:46:51] Origin: http://localhost:8080
[2025-07-18 15:46:51] Referer: http://localhost:8080/login
[2025-07-18 15:46:51] En-têtes de la requête:
[2025-07-18 15:46:51]   host: localhost
[2025-07-18 15:46:51]   connection: close
[2025-07-18 15:46:51]   content-length: 39
[2025-07-18 15:46:51]   sec-ch-ua-platform: "Windows"
[2025-07-18 15:46:51]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 15:46:51]   accept: application/json
[2025-07-18 15:46:51]   x-kl-ajax-request: Ajax_Request
[2025-07-18 15:46:51]   content-type: application/json
[2025-07-18 15:46:51]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 15:46:51]   sec-ch-ua-mobile: ?0
[2025-07-18 15:46:51]   origin: http://localhost:8080
[2025-07-18 15:46:51]   sec-fetch-site: same-origin
[2025-07-18 15:46:51]   sec-fetch-mode: cors
[2025-07-18 15:46:51]   sec-fetch-dest: empty
[2025-07-18 15:46:51]   referer: http://localhost:8080/login
[2025-07-18 15:46:51]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 15:46:51]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 15:46:51]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 15:46:51] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 15:46:51] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 15:46:51] Connexion à la base de données réussie
[2025-07-18 15:46:51] Recherche de l'utilisateur: trouvé
[2025-07-18 15:46:51] ID utilisateur: 1
[2025-07-18 15:46:51] Rôle: admin
[2025-07-18 15:46:51] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 15:46:51] Mot de passe fourni: admin
[2025-07-18 15:46:51] Vérification du mot de passe: réussie
[2025-07-18 15:46:51] Rôle défini dans la base de données: admin
[2025-07-18 15:46:51] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 15:46:51] Connexion réussie! Token JWT généré:
[2025-07-18 15:46:51] User ID: 1
[2025-07-18 15:46:51] Username: admin
[2025-07-18 15:46:51] Rôle: admin
[2025-07-18 15:46:51] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 15:46:51] Préparation de l'envoi de la réponse JSON:
[2025-07-18 15:46:51] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg0NjQxMSwiZXhwIjoxNzUyOTMyODExLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.906QXsjLtXNwW9VQExdC5gGQZ-TQwcqFcLyIcC1aJIw"}
[2025-07-18 16:00:59] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:00:59] Méthode HTTP: POST
[2025-07-18 16:00:59] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:00:59] Origin: http://localhost:8080
[2025-07-18 16:00:59] Referer: http://localhost:8080/login
[2025-07-18 16:00:59] En-têtes de la requête:
[2025-07-18 16:00:59]   host: localhost
[2025-07-18 16:00:59]   connection: close
[2025-07-18 16:00:59]   content-length: 39
[2025-07-18 16:00:59]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:00:59]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:00:59]   accept: application/json
[2025-07-18 16:00:59]   x-kl-ajax-request: Ajax_Request
[2025-07-18 16:00:59]   content-type: application/json
[2025-07-18 16:00:59]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:00:59]   sec-ch-ua-mobile: ?0
[2025-07-18 16:00:59]   origin: http://localhost:8080
[2025-07-18 16:00:59]   sec-fetch-site: same-origin
[2025-07-18 16:00:59]   sec-fetch-mode: cors
[2025-07-18 16:00:59]   sec-fetch-dest: empty
[2025-07-18 16:00:59]   referer: http://localhost:8080/login
[2025-07-18 16:00:59]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 16:00:59]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:00:59]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:00:59] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 16:00:59] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 16:00:59] Connexion à la base de données réussie
[2025-07-18 16:00:59] Recherche de l'utilisateur: trouvé
[2025-07-18 16:00:59] ID utilisateur: 1
[2025-07-18 16:00:59] Rôle: admin
[2025-07-18 16:00:59] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 16:00:59] Mot de passe fourni: admin
[2025-07-18 16:00:59] Vérification du mot de passe: réussie
[2025-07-18 16:00:59] Rôle défini dans la base de données: admin
[2025-07-18 16:00:59] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 16:00:59] Connexion réussie! Token JWT généré:
[2025-07-18 16:00:59] User ID: 1
[2025-07-18 16:00:59] Username: admin
[2025-07-18 16:00:59] Rôle: admin
[2025-07-18 16:00:59] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 16:00:59] Préparation de l'envoi de la réponse JSON:
[2025-07-18 16:00:59] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg0NzI1OSwiZXhwIjoxNzUyOTMzNjU5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.Vpg4_z4ZPvsJun9q89lnIHujbCjQ2osErEz8elxMQS4"}
[2025-07-18 16:08:29] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:08:29] Méthode HTTP: POST
[2025-07-18 16:08:29] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:08:29] Origin: http://localhost
[2025-07-18 16:08:29] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_cors_final.html
[2025-07-18 16:08:29] En-têtes de la requête:
[2025-07-18 16:08:29]   Host: localhost
[2025-07-18 16:08:29]   Connection: keep-alive
[2025-07-18 16:08:29]   Content-Length: 39
[2025-07-18 16:08:29]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:08:29]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:08:29]   X-KL-Ajax-Request: Ajax_Request
[2025-07-18 16:08:29]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:08:29]   Content-Type: application/json
[2025-07-18 16:08:29]   sec-ch-ua-mobile: ?0
[2025-07-18 16:08:29]   Accept: */*
[2025-07-18 16:08:29]   Origin: http://localhost
[2025-07-18 16:08:29]   Sec-Fetch-Site: same-origin
[2025-07-18 16:08:29]   Sec-Fetch-Mode: cors
[2025-07-18 16:08:29]   Sec-Fetch-Dest: empty
[2025-07-18 16:08:29]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_cors_final.html
[2025-07-18 16:08:29]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 16:08:29]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:08:29] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 16:08:29] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 16:08:29] Connexion à la base de données réussie
[2025-07-18 16:08:29] Recherche de l'utilisateur: trouvé
[2025-07-18 16:08:29] ID utilisateur: 1
[2025-07-18 16:08:29] Rôle: admin
[2025-07-18 16:08:29] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 16:08:29] Mot de passe fourni: admin
[2025-07-18 16:08:29] Vérification du mot de passe: réussie
[2025-07-18 16:08:29] Rôle défini dans la base de données: admin
[2025-07-18 16:08:29] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 16:08:29] Connexion réussie! Token JWT généré:
[2025-07-18 16:08:29] User ID: 1
[2025-07-18 16:08:29] Username: admin
[2025-07-18 16:08:29] Rôle: admin
[2025-07-18 16:08:29] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 16:08:29] Préparation de l'envoi de la réponse JSON:
[2025-07-18 16:08:29] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg0NzcwOSwiZXhwIjoxNzUyOTM0MTA5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.I01ws0juVKvL9X2cLqW-lsgyEbmEB-ik32gwrVMx170"}
[2025-07-18 16:09:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:09:55] Méthode HTTP: POST
[2025-07-18 16:09:55] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:09:55] Origin: http://localhost:8080
[2025-07-18 16:09:55] Referer: http://localhost:8080/login
[2025-07-18 16:09:55] En-têtes de la requête:
[2025-07-18 16:09:55]   host: localhost
[2025-07-18 16:09:55]   connection: close
[2025-07-18 16:09:55]   content-length: 39
[2025-07-18 16:09:55]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:09:55]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:09:55]   accept: application/json
[2025-07-18 16:09:55]   x-kl-ajax-request: Ajax_Request
[2025-07-18 16:09:55]   content-type: application/json
[2025-07-18 16:09:55]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:09:55]   sec-ch-ua-mobile: ?0
[2025-07-18 16:09:55]   origin: http://localhost:8080
[2025-07-18 16:09:55]   sec-fetch-site: same-origin
[2025-07-18 16:09:55]   sec-fetch-mode: cors
[2025-07-18 16:09:55]   sec-fetch-dest: empty
[2025-07-18 16:09:55]   referer: http://localhost:8080/login
[2025-07-18 16:09:55]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 16:09:55]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:09:55]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:09:55] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 16:09:55] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 16:09:55] Connexion à la base de données réussie
[2025-07-18 16:09:55] Recherche de l'utilisateur: trouvé
[2025-07-18 16:09:55] ID utilisateur: 1
[2025-07-18 16:09:55] Rôle: admin
[2025-07-18 16:09:55] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 16:09:55] Mot de passe fourni: admin
[2025-07-18 16:09:55] Vérification du mot de passe: réussie
[2025-07-18 16:09:55] Rôle défini dans la base de données: admin
[2025-07-18 16:09:55] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 16:09:55] Connexion réussie! Token JWT généré:
[2025-07-18 16:09:55] User ID: 1
[2025-07-18 16:09:55] Username: admin
[2025-07-18 16:09:55] Rôle: admin
[2025-07-18 16:09:55] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 16:09:55] Préparation de l'envoi de la réponse JSON:
[2025-07-18 16:09:55] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg0Nzc5NSwiZXhwIjoxNzUyOTM0MTk1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.wHg4mAmNYt5wGDuH5-gbOQ3W0CqR3DnQmPChisTKnW4"}
[2025-07-18 16:14:14] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:14:14] Méthode HTTP: GET
[2025-07-18 16:14:14] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:14:14] Origin: http://localhost:8080
[2025-07-18 16:14:14] Referer: Non défini
[2025-07-18 16:14:14] En-têtes de la requête:
[2025-07-18 16:14:14]   Host: localhost
[2025-07-18 16:14:14]   Connection: keep-alive
[2025-07-18 16:14:14]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:14:14]   sec-ch-ua-mobile: ?0
[2025-07-18 16:14:14]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:14:14]   Upgrade-Insecure-Requests: 1
[2025-07-18 16:14:14]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:14:14]   Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
[2025-07-18 16:14:14]   Sec-Fetch-Site: none
[2025-07-18 16:14:14]   Sec-Fetch-Mode: navigate
[2025-07-18 16:14:14]   Sec-Fetch-User: ?1
[2025-07-18 16:14:14]   Sec-Fetch-Dest: document
[2025-07-18 16:14:14]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 16:14:14]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:14:14]   Cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:14:14] Méthode non autorisée: GET
[2025-07-18 16:14:44] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:14:44] Méthode HTTP: GET
[2025-07-18 16:14:44] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:14:44] Origin: http://localhost:8080
[2025-07-18 16:14:44] Referer: Non défini
[2025-07-18 16:14:44] En-têtes de la requête:
[2025-07-18 16:14:44]   Host: localhost
[2025-07-18 16:14:44]   Connection: keep-alive
[2025-07-18 16:14:44]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:14:44]   sec-ch-ua-mobile: ?0
[2025-07-18 16:14:44]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:14:44]   Upgrade-Insecure-Requests: 1
[2025-07-18 16:14:44]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:14:44]   Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
[2025-07-18 16:14:44]   Sec-Fetch-Site: none
[2025-07-18 16:14:44]   Sec-Fetch-Mode: navigate
[2025-07-18 16:14:44]   Sec-Fetch-User: ?1
[2025-07-18 16:14:44]   Sec-Fetch-Dest: document
[2025-07-18 16:14:44]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 16:14:44]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:14:44]   Cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:14:44] Méthode non autorisée: GET
[2025-07-18 16:16:46] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:16:46] Méthode HTTP: POST
[2025-07-18 16:16:46] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:16:46] Origin: http://localhost:8080
[2025-07-18 16:16:46] Referer: http://localhost:8080/login
[2025-07-18 16:16:46] En-têtes de la requête:
[2025-07-18 16:16:46]   host: localhost
[2025-07-18 16:16:46]   connection: close
[2025-07-18 16:16:46]   content-length: 39
[2025-07-18 16:16:46]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:16:46]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:16:46]   accept: application/json
[2025-07-18 16:16:46]   x-kl-ajax-request: Ajax_Request
[2025-07-18 16:16:46]   content-type: application/json
[2025-07-18 16:16:46]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:16:46]   sec-ch-ua-mobile: ?0
[2025-07-18 16:16:46]   origin: http://localhost:8080
[2025-07-18 16:16:46]   sec-fetch-site: same-origin
[2025-07-18 16:16:46]   sec-fetch-mode: cors
[2025-07-18 16:16:46]   sec-fetch-dest: empty
[2025-07-18 16:16:46]   referer: http://localhost:8080/login
[2025-07-18 16:16:46]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 16:16:46]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:16:46]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:16:46] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 16:16:46] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 16:16:46] Connexion à la base de données réussie
[2025-07-18 16:16:46] Recherche de l'utilisateur: trouvé
[2025-07-18 16:16:46] ID utilisateur: 1
[2025-07-18 16:16:46] Rôle: admin
[2025-07-18 16:16:46] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 16:16:46] Mot de passe fourni: admin
[2025-07-18 16:16:46] Vérification du mot de passe: réussie
[2025-07-18 16:16:46] Rôle défini dans la base de données: admin
[2025-07-18 16:16:46] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 16:16:46] Connexion réussie! Token JWT généré:
[2025-07-18 16:16:46] User ID: 1
[2025-07-18 16:16:46] Username: admin
[2025-07-18 16:16:46] Rôle: admin
[2025-07-18 16:16:46] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 16:16:46] Préparation de l'envoi de la réponse JSON:
[2025-07-18 16:16:46] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg0ODIwNiwiZXhwIjoxNzUyOTM0NjA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.NlS1s5_b350BRBYVNzwUQgAxcq4y-23lj2I19wsJwOo"}
[2025-07-18 16:51:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:51:59] Méthode HTTP: POST
[2025-07-18 16:51:59] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:51:59] Origin: http://localhost:8080
[2025-07-18 16:51:59] Referer: http://localhost:8080/login
[2025-07-18 16:51:59] En-têtes de la requête:
[2025-07-18 16:51:59]   host: localhost
[2025-07-18 16:51:59]   connection: close
[2025-07-18 16:51:59]   content-length: 39
[2025-07-18 16:51:59]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:51:59]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:51:59]   accept: application/json
[2025-07-18 16:51:59]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:51:59]   content-type: application/json
[2025-07-18 16:51:59]   sec-ch-ua-mobile: ?0
[2025-07-18 16:51:59]   origin: http://localhost:8080
[2025-07-18 16:51:59]   sec-fetch-site: same-origin
[2025-07-18 16:51:59]   sec-fetch-mode: cors
[2025-07-18 16:51:59]   sec-fetch-dest: empty
[2025-07-18 16:51:59]   referer: http://localhost:8080/login
[2025-07-18 16:51:59]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 16:51:59]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:51:59]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:51:59] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 16:51:59] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 16:51:59] Connexion à la base de données réussie
[2025-07-18 16:51:59] Recherche de l'utilisateur: trouvé
[2025-07-18 16:51:59] ID utilisateur: 1
[2025-07-18 16:51:59] Rôle: admin
[2025-07-18 16:51:59] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 16:51:59] Mot de passe fourni: admin
[2025-07-18 16:51:59] Vérification du mot de passe: réussie
[2025-07-18 16:51:59] Rôle défini dans la base de données: admin
[2025-07-18 16:51:59] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 16:51:59] Connexion réussie! Token JWT généré:
[2025-07-18 16:51:59] User ID: 1
[2025-07-18 16:51:59] Username: admin
[2025-07-18 16:51:59] Rôle: admin
[2025-07-18 16:51:59] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 16:51:59] Préparation de l'envoi de la réponse JSON:
[2025-07-18 16:51:59] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MDMxOSwiZXhwIjoxNzUyOTM2NzE5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.hgxryI8v37Df0xVP-6YV2Qm4-zwEVpMwsaoF8FCN_jM"}
[2025-07-18 16:54:01] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 16:54:01] Méthode HTTP: POST
[2025-07-18 16:54:01] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:54:01] Origin: http://localhost:8080
[2025-07-18 16:54:01] Referer: http://localhost:8080/login
[2025-07-18 16:54:01] En-têtes de la requête:
[2025-07-18 16:54:01]   host: localhost
[2025-07-18 16:54:01]   connection: close
[2025-07-18 16:54:01]   content-length: 39
[2025-07-18 16:54:01]   sec-ch-ua-platform: "Windows"
[2025-07-18 16:54:01]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 16:54:01]   accept: application/json
[2025-07-18 16:54:01]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 16:54:01]   content-type: application/json
[2025-07-18 16:54:01]   sec-ch-ua-mobile: ?0
[2025-07-18 16:54:01]   origin: http://localhost:8080
[2025-07-18 16:54:01]   sec-fetch-site: same-origin
[2025-07-18 16:54:01]   sec-fetch-mode: cors
[2025-07-18 16:54:01]   sec-fetch-dest: empty
[2025-07-18 16:54:01]   referer: http://localhost:8080/login
[2025-07-18 16:54:01]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 16:54:01]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 16:54:01]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 16:54:01] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 16:54:01] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 16:54:01] Connexion à la base de données réussie
[2025-07-18 16:54:01] Recherche de l'utilisateur: trouvé
[2025-07-18 16:54:01] ID utilisateur: 1
[2025-07-18 16:54:01] Rôle: admin
[2025-07-18 16:54:01] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 16:54:01] Mot de passe fourni: admin
[2025-07-18 16:54:01] Vérification du mot de passe: réussie
[2025-07-18 16:54:01] Rôle défini dans la base de données: admin
[2025-07-18 16:54:01] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 16:54:01] Connexion réussie! Token JWT généré:
[2025-07-18 16:54:01] User ID: 1
[2025-07-18 16:54:01] Username: admin
[2025-07-18 16:54:01] Rôle: admin
[2025-07-18 16:54:01] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 16:54:01] Préparation de l'envoi de la réponse JSON:
[2025-07-18 16:54:01] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MDQ0MSwiZXhwIjoxNzUyOTM2ODQxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.iBTt5BXcpZOMJrF12shZWu6P6JfUsVcWno_WIPrkmVg"}
[2025-07-18 17:16:39] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:16:39] Méthode HTTP: POST
[2025-07-18 17:16:39] User-Agent: Non défini
[2025-07-18 17:16:39] Origin: Non défini
[2025-07-18 17:16:39] Referer: Non défini
[2025-07-18 17:16:39] En-têtes de la requête:
[2025-07-18 17:16:39]   Host: localhost
[2025-07-18 17:16:39]   Accept: */*
[2025-07-18 17:16:39]   Content-Type: application/json
[2025-07-18 17:16:39]   Content-Length: 39
[2025-07-18 17:16:39] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:16:39] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:16:39] Connexion à la base de données réussie
[2025-07-18 17:16:39] Recherche de l'utilisateur: trouvé
[2025-07-18 17:16:39] ID utilisateur: 1
[2025-07-18 17:16:39] Rôle: admin
[2025-07-18 17:16:39] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:16:39] Mot de passe fourni: admin
[2025-07-18 17:16:39] Vérification du mot de passe: réussie
[2025-07-18 17:16:39] Rôle défini dans la base de données: admin
[2025-07-18 17:16:39] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:16:39] Connexion réussie! Token JWT généré:
[2025-07-18 17:16:39] User ID: 1
[2025-07-18 17:16:39] Username: admin
[2025-07-18 17:16:39] Rôle: admin
[2025-07-18 17:16:39] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:16:39] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:16:39] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MTc5OSwiZXhwIjoxNzUyOTM4MTk5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.H05_PPPHMfZdd50e5ft1l-cFpIx3yF18JiQZx5AkDzA"}
[2025-07-18 17:21:25] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:21:25] Méthode HTTP: POST
[2025-07-18 17:21:25] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 17:21:25] Origin: http://localhost
[2025-07-18 17:21:25] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_assignment.html
[2025-07-18 17:21:25] En-têtes de la requête:
[2025-07-18 17:21:25]   Host: localhost
[2025-07-18 17:21:25]   Connection: keep-alive
[2025-07-18 17:21:25]   Content-Length: 39
[2025-07-18 17:21:25]   sec-ch-ua-platform: "Windows"
[2025-07-18 17:21:25]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 17:21:25]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 17:21:25]   Content-Type: application/json
[2025-07-18 17:21:25]   sec-ch-ua-mobile: ?0
[2025-07-18 17:21:25]   Accept: */*
[2025-07-18 17:21:25]   Origin: http://localhost
[2025-07-18 17:21:25]   Sec-Fetch-Site: same-origin
[2025-07-18 17:21:25]   Sec-Fetch-Mode: cors
[2025-07-18 17:21:25]   Sec-Fetch-Dest: empty
[2025-07-18 17:21:25]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_assignment.html
[2025-07-18 17:21:25]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 17:21:25]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 17:21:25] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:21:25] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:21:25] Connexion à la base de données réussie
[2025-07-18 17:21:25] Recherche de l'utilisateur: trouvé
[2025-07-18 17:21:25] ID utilisateur: 1
[2025-07-18 17:21:25] Rôle: admin
[2025-07-18 17:21:25] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:21:25] Mot de passe fourni: admin
[2025-07-18 17:21:25] Vérification du mot de passe: réussie
[2025-07-18 17:21:25] Rôle défini dans la base de données: admin
[2025-07-18 17:21:25] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:21:25] Connexion réussie! Token JWT généré:
[2025-07-18 17:21:25] User ID: 1
[2025-07-18 17:21:25] Username: admin
[2025-07-18 17:21:25] Rôle: admin
[2025-07-18 17:21:25] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:21:25] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:21:25] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjA4NSwiZXhwIjoxNzUyOTM4NDg1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.uDoXAiULDoEyAecsuPU26i3JWw7q8YSKbR7zkjKMFXs"}
[2025-07-18 17:27:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:27:38] Méthode HTTP: POST
[2025-07-18 17:27:38] User-Agent: Non défini
[2025-07-18 17:27:38] Origin: Non défini
[2025-07-18 17:27:38] Referer: Non défini
[2025-07-18 17:27:38] En-têtes de la requête:
[2025-07-18 17:27:38]   Host: localhost
[2025-07-18 17:27:38]   Accept: */*
[2025-07-18 17:27:38]   Content-Type: application/json
[2025-07-18 17:27:38]   Content-Length: 39
[2025-07-18 17:27:38] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:27:38] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:27:38] Connexion à la base de données réussie
[2025-07-18 17:27:38] Recherche de l'utilisateur: trouvé
[2025-07-18 17:27:38] ID utilisateur: 1
[2025-07-18 17:27:38] Rôle: admin
[2025-07-18 17:27:38] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:27:38] Mot de passe fourni: admin
[2025-07-18 17:27:38] Vérification du mot de passe: réussie
[2025-07-18 17:27:38] Rôle défini dans la base de données: admin
[2025-07-18 17:27:38] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:27:38] Connexion réussie! Token JWT généré:
[2025-07-18 17:27:38] User ID: 1
[2025-07-18 17:27:38] Username: admin
[2025-07-18 17:27:38] Rôle: admin
[2025-07-18 17:27:38] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:27:38] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:27:38] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjQ1OCwiZXhwIjoxNzUyOTM4ODU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.rutuA4hTcl14oTh6WjkgUbIpA2sjISwBf7rBnvQQnQE"}
[2025-07-18 17:28:45] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:28:45] Méthode HTTP: POST
[2025-07-18 17:28:45] User-Agent: Non défini
[2025-07-18 17:28:45] Origin: Non défini
[2025-07-18 17:28:45] Referer: Non défini
[2025-07-18 17:28:45] En-têtes de la requête:
[2025-07-18 17:28:45]   Host: localhost
[2025-07-18 17:28:45]   Accept: */*
[2025-07-18 17:28:45]   Content-Type: application/json
[2025-07-18 17:28:45]   Content-Length: 39
[2025-07-18 17:28:45] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:28:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:28:45] Connexion à la base de données réussie
[2025-07-18 17:28:45] Recherche de l'utilisateur: trouvé
[2025-07-18 17:28:45] ID utilisateur: 1
[2025-07-18 17:28:45] Rôle: admin
[2025-07-18 17:28:45] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:28:45] Mot de passe fourni: admin
[2025-07-18 17:28:45] Vérification du mot de passe: réussie
[2025-07-18 17:28:45] Rôle défini dans la base de données: admin
[2025-07-18 17:28:45] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:28:45] Connexion réussie! Token JWT généré:
[2025-07-18 17:28:45] User ID: 1
[2025-07-18 17:28:45] Username: admin
[2025-07-18 17:28:45] Rôle: admin
[2025-07-18 17:28:45] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:28:45] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:28:45] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjUyNSwiZXhwIjoxNzUyOTM4OTI1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.-E0r_Ff4ytEZ7olPQLP6HVUIp78RBSb2vHcn8QWAO4A"}
[2025-07-18 17:29:22] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:29:22] Méthode HTTP: POST
[2025-07-18 17:29:22] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 17:29:22] Origin: http://localhost
[2025-07-18 17:29:22] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_assignment.html
[2025-07-18 17:29:22] En-têtes de la requête:
[2025-07-18 17:29:22]   Host: localhost
[2025-07-18 17:29:22]   Connection: keep-alive
[2025-07-18 17:29:22]   Content-Length: 39
[2025-07-18 17:29:22]   sec-ch-ua-platform: "Windows"
[2025-07-18 17:29:22]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 17:29:22]   X-KL-Ajax-Request: Ajax_Request
[2025-07-18 17:29:22]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 17:29:22]   Content-Type: application/json
[2025-07-18 17:29:22]   sec-ch-ua-mobile: ?0
[2025-07-18 17:29:22]   Accept: */*
[2025-07-18 17:29:22]   Origin: http://localhost
[2025-07-18 17:29:22]   Sec-Fetch-Site: same-origin
[2025-07-18 17:29:22]   Sec-Fetch-Mode: cors
[2025-07-18 17:29:22]   Sec-Fetch-Dest: empty
[2025-07-18 17:29:22]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_assignment.html
[2025-07-18 17:29:22]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 17:29:22]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 17:29:22] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:29:22] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:29:22] Connexion à la base de données réussie
[2025-07-18 17:29:22] Recherche de l'utilisateur: trouvé
[2025-07-18 17:29:22] ID utilisateur: 1
[2025-07-18 17:29:22] Rôle: admin
[2025-07-18 17:29:22] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:29:22] Mot de passe fourni: admin
[2025-07-18 17:29:22] Vérification du mot de passe: réussie
[2025-07-18 17:29:22] Rôle défini dans la base de données: admin
[2025-07-18 17:29:22] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:29:22] Connexion réussie! Token JWT généré:
[2025-07-18 17:29:22] User ID: 1
[2025-07-18 17:29:22] Username: admin
[2025-07-18 17:29:22] Rôle: admin
[2025-07-18 17:29:22] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:29:22] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:29:22] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjU2MiwiZXhwIjoxNzUyOTM4OTYyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.UNTA8Zf1Yo7J2cywGk3h59P0S4f_uhBxO8NLEvmW-V8"}
[2025-07-18 17:33:51] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:33:51] Méthode HTTP: POST
[2025-07-18 17:33:51] User-Agent: Non défini
[2025-07-18 17:33:51] Origin: Non défini
[2025-07-18 17:33:51] Referer: Non défini
[2025-07-18 17:33:51] En-têtes de la requête:
[2025-07-18 17:33:51]   Host: localhost
[2025-07-18 17:33:51]   Accept: */*
[2025-07-18 17:33:51]   Content-Type: application/json
[2025-07-18 17:33:51]   Content-Length: 39
[2025-07-18 17:33:51] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:33:51] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:33:51] Connexion à la base de données réussie
[2025-07-18 17:33:51] Recherche de l'utilisateur: trouvé
[2025-07-18 17:33:51] ID utilisateur: 1
[2025-07-18 17:33:51] Rôle: admin
[2025-07-18 17:33:51] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:33:51] Mot de passe fourni: admin
[2025-07-18 17:33:51] Vérification du mot de passe: réussie
[2025-07-18 17:33:51] Rôle défini dans la base de données: admin
[2025-07-18 17:33:51] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:33:51] Connexion réussie! Token JWT généré:
[2025-07-18 17:33:51] User ID: 1
[2025-07-18 17:33:51] Username: admin
[2025-07-18 17:33:51] Rôle: admin
[2025-07-18 17:33:51] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:33:51] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:33:51] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjgzMSwiZXhwIjoxNzUyOTM5MjMxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.blB4kCRTdBH2anHYB_RwNyyW_o9cvctK6gzuLWxOux8"}
[2025-07-18 17:48:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:48:26] Méthode HTTP: POST
[2025-07-18 17:48:26] User-Agent: Non défini
[2025-07-18 17:48:26] Origin: Non défini
[2025-07-18 17:48:26] Referer: Non défini
[2025-07-18 17:48:26] En-têtes de la requête:
[2025-07-18 17:48:26]   Host: localhost
[2025-07-18 17:48:26]   Accept: */*
[2025-07-18 17:48:26]   Content-Type: application/json
[2025-07-18 17:48:26]   Content-Length: 39
[2025-07-18 17:48:26] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:48:26] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:48:26] Connexion à la base de données réussie
[2025-07-18 17:48:26] Recherche de l'utilisateur: trouvé
[2025-07-18 17:48:26] ID utilisateur: 1
[2025-07-18 17:48:26] Rôle: admin
[2025-07-18 17:48:26] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:48:26] Mot de passe fourni: admin
[2025-07-18 17:48:26] Vérification du mot de passe: réussie
[2025-07-18 17:48:26] Rôle défini dans la base de données: admin
[2025-07-18 17:48:26] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:48:26] Connexion réussie! Token JWT généré:
[2025-07-18 17:48:26] User ID: 1
[2025-07-18 17:48:26] Username: admin
[2025-07-18 17:48:26] Rôle: admin
[2025-07-18 17:48:26] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:48:26] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:48:26] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzcwNiwiZXhwIjoxNzUyOTQwMTA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.nISLty_IOdtLv5LWamB9FukVdD7E6OieY5a2mhb4LAk"}
[2025-07-18 17:48:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:48:26] Méthode HTTP: POST
[2025-07-18 17:48:26] User-Agent: Non défini
[2025-07-18 17:48:26] Origin: Non défini
[2025-07-18 17:48:26] Referer: Non défini
[2025-07-18 17:48:26] En-têtes de la requête:
[2025-07-18 17:48:26]   Host: localhost
[2025-07-18 17:48:26]   Accept: */*
[2025-07-18 17:48:26]   Content-Type: application/json
[2025-07-18 17:48:26]   Content-Length: 39
[2025-07-18 17:48:26] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:48:26] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:48:26] Connexion à la base de données réussie
[2025-07-18 17:48:26] Recherche de l'utilisateur: trouvé
[2025-07-18 17:48:26] ID utilisateur: 1
[2025-07-18 17:48:26] Rôle: admin
[2025-07-18 17:48:26] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:48:26] Mot de passe fourni: admin
[2025-07-18 17:48:26] Vérification du mot de passe: réussie
[2025-07-18 17:48:26] Rôle défini dans la base de données: admin
[2025-07-18 17:48:26] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:48:26] Connexion réussie! Token JWT généré:
[2025-07-18 17:48:26] User ID: 1
[2025-07-18 17:48:26] Username: admin
[2025-07-18 17:48:26] Rôle: admin
[2025-07-18 17:48:26] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:48:26] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:48:26] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzcwNiwiZXhwIjoxNzUyOTQwMTA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.nISLty_IOdtLv5LWamB9FukVdD7E6OieY5a2mhb4LAk"}
[2025-07-18 17:48:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:48:26] Méthode HTTP: POST
[2025-07-18 17:48:26] User-Agent: Non défini
[2025-07-18 17:48:26] Origin: Non défini
[2025-07-18 17:48:26] Referer: Non défini
[2025-07-18 17:48:26] En-têtes de la requête:
[2025-07-18 17:48:26]   Host: localhost
[2025-07-18 17:48:26]   Accept: */*
[2025-07-18 17:48:26]   Content-Type: application/json
[2025-07-18 17:48:26]   Content-Length: 39
[2025-07-18 17:48:26] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:48:26] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:48:26] Connexion à la base de données réussie
[2025-07-18 17:48:26] Recherche de l'utilisateur: trouvé
[2025-07-18 17:48:26] ID utilisateur: 1
[2025-07-18 17:48:26] Rôle: admin
[2025-07-18 17:48:26] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:48:26] Mot de passe fourni: admin
[2025-07-18 17:48:26] Vérification du mot de passe: réussie
[2025-07-18 17:48:26] Rôle défini dans la base de données: admin
[2025-07-18 17:48:26] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:48:26] Connexion réussie! Token JWT généré:
[2025-07-18 17:48:26] User ID: 1
[2025-07-18 17:48:26] Username: admin
[2025-07-18 17:48:26] Rôle: admin
[2025-07-18 17:48:26] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:48:26] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:48:26] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzcwNiwiZXhwIjoxNzUyOTQwMTA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.nISLty_IOdtLv5LWamB9FukVdD7E6OieY5a2mhb4LAk"}
[2025-07-18 17:50:34] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 17:50:34] Méthode HTTP: POST
[2025-07-18 17:50:34] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 17:50:34] Origin: http://localhost
[2025-07-18 17:50:34] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_assignment_ui.html
[2025-07-18 17:50:34] En-têtes de la requête:
[2025-07-18 17:50:34]   Host: localhost
[2025-07-18 17:50:34]   Connection: keep-alive
[2025-07-18 17:50:34]   Content-Length: 39
[2025-07-18 17:50:34]   sec-ch-ua-platform: "Windows"
[2025-07-18 17:50:34]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 17:50:34]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 17:50:34]   Content-Type: application/json
[2025-07-18 17:50:34]   sec-ch-ua-mobile: ?0
[2025-07-18 17:50:34]   Accept: */*
[2025-07-18 17:50:34]   Origin: http://localhost
[2025-07-18 17:50:34]   Sec-Fetch-Site: same-origin
[2025-07-18 17:50:34]   Sec-Fetch-Mode: cors
[2025-07-18 17:50:34]   Sec-Fetch-Dest: empty
[2025-07-18 17:50:34]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_assignment_ui.html
[2025-07-18 17:50:34]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 17:50:34]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 17:50:34]   Cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 17:50:34] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 17:50:34] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 17:50:34] Connexion à la base de données réussie
[2025-07-18 17:50:34] Recherche de l'utilisateur: trouvé
[2025-07-18 17:50:34] ID utilisateur: 1
[2025-07-18 17:50:34] Rôle: admin
[2025-07-18 17:50:34] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 17:50:34] Mot de passe fourni: admin
[2025-07-18 17:50:34] Vérification du mot de passe: réussie
[2025-07-18 17:50:34] Rôle défini dans la base de données: admin
[2025-07-18 17:50:34] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 17:50:34] Connexion réussie! Token JWT généré:
[2025-07-18 17:50:34] User ID: 1
[2025-07-18 17:50:34] Username: admin
[2025-07-18 17:50:34] Rôle: admin
[2025-07-18 17:50:34] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 17:50:34] Préparation de l'envoi de la réponse JSON:
[2025-07-18 17:50:34] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzgzNCwiZXhwIjoxNzUyOTQwMjM0LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.6pEIJ3os2bF3oCjS9alme3vXFrFaJE_85teH9slkZZ0"}
[2025-07-18 18:14:26] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 18:14:26] Méthode HTTP: POST
[2025-07-18 18:14:26] User-Agent: Non défini
[2025-07-18 18:14:26] Origin: Non défini
[2025-07-18 18:14:26] Referer: Non défini
[2025-07-18 18:14:26] En-têtes de la requête:
[2025-07-18 18:14:26]   Host: localhost
[2025-07-18 18:14:26]   Accept: */*
[2025-07-18 18:14:26]   Content-Type: application/json
[2025-07-18 18:14:26]   Content-Length: 39
[2025-07-18 18:14:26] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 18:14:26] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 18:14:26] Connexion à la base de données réussie
[2025-07-18 18:14:26] Recherche de l'utilisateur: trouvé
[2025-07-18 18:14:26] ID utilisateur: 1
[2025-07-18 18:14:26] Rôle: admin
[2025-07-18 18:14:26] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 18:14:26] Mot de passe fourni: admin
[2025-07-18 18:14:26] Vérification du mot de passe: réussie
[2025-07-18 18:14:26] Rôle défini dans la base de données: admin
[2025-07-18 18:14:26] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 18:14:26] Connexion réussie! Token JWT généré:
[2025-07-18 18:14:26] User ID: 1
[2025-07-18 18:14:26] Username: admin
[2025-07-18 18:14:26] Rôle: admin
[2025-07-18 18:14:26] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 18:14:26] Préparation de l'envoi de la réponse JSON:
[2025-07-18 18:14:26] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NTI2NiwiZXhwIjoxNzUyOTQxNjY2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.21GHZSg0Xi4xRzfIFXKEzIbyQM1BhGiQxNw2-vPhOGY"}
[2025-07-18 18:15:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 18:15:58] Méthode HTTP: POST
[2025-07-18 18:15:58] User-Agent: Non défini
[2025-07-18 18:15:58] Origin: Non défini
[2025-07-18 18:15:58] Referer: Non défini
[2025-07-18 18:15:58] En-têtes de la requête:
[2025-07-18 18:15:58]   Host: localhost
[2025-07-18 18:15:58]   Accept: */*
[2025-07-18 18:15:58]   Content-Type: application/json
[2025-07-18 18:15:58]   Content-Length: 39
[2025-07-18 18:15:58] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 18:15:58] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 18:15:58] Connexion à la base de données réussie
[2025-07-18 18:15:58] Recherche de l'utilisateur: trouvé
[2025-07-18 18:15:58] ID utilisateur: 1
[2025-07-18 18:15:58] Rôle: admin
[2025-07-18 18:15:58] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 18:15:58] Mot de passe fourni: admin
[2025-07-18 18:15:58] Vérification du mot de passe: réussie
[2025-07-18 18:15:58] Rôle défini dans la base de données: admin
[2025-07-18 18:15:58] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 18:15:58] Connexion réussie! Token JWT généré:
[2025-07-18 18:15:58] User ID: 1
[2025-07-18 18:15:58] Username: admin
[2025-07-18 18:15:58] Rôle: admin
[2025-07-18 18:15:58] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 18:15:58] Préparation de l'envoi de la réponse JSON:
[2025-07-18 18:15:58] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NTM1OCwiZXhwIjoxNzUyOTQxNzU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.zT7txtbRSjB4T93LgCUbtDD00MGlLDz-BRbLf08HR_M"}
[2025-07-18 18:17:56] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 18:17:56] Méthode HTTP: POST
[2025-07-18 18:17:56] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 18:17:56] Origin: http://localhost:8080
[2025-07-18 18:17:56] Referer: http://localhost:8080/login
[2025-07-18 18:17:56] En-têtes de la requête:
[2025-07-18 18:17:56]   host: localhost
[2025-07-18 18:17:56]   connection: close
[2025-07-18 18:17:56]   content-length: 39
[2025-07-18 18:17:56]   sec-ch-ua-platform: "Windows"
[2025-07-18 18:17:56]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 18:17:56]   accept: application/json
[2025-07-18 18:17:56]   x-kl-ajax-request: Ajax_Request
[2025-07-18 18:17:56]   content-type: application/json
[2025-07-18 18:17:56]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 18:17:56]   sec-ch-ua-mobile: ?0
[2025-07-18 18:17:56]   origin: http://localhost:8080
[2025-07-18 18:17:56]   sec-fetch-site: same-origin
[2025-07-18 18:17:56]   sec-fetch-mode: cors
[2025-07-18 18:17:56]   sec-fetch-dest: empty
[2025-07-18 18:17:56]   referer: http://localhost:8080/login
[2025-07-18 18:17:56]   accept-encoding: gzip, deflate, br, zstd
[2025-07-18 18:17:56]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 18:17:56]   cookie: connect.sid=s%3AodVtEyLgH8pS2xBPQgNqbceHO7dnqV58.h8YC3bWRNUvlrd5VbG8rhcH%2BhGRwk279Slf2iXf%2Bx3I; sidebar:state=false
[2025-07-18 18:17:56] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 18:17:56] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 18:17:56] Connexion à la base de données réussie
[2025-07-18 18:17:57] Recherche de l'utilisateur: trouvé
[2025-07-18 18:17:57] ID utilisateur: 1
[2025-07-18 18:17:57] Rôle: admin
[2025-07-18 18:17:57] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 18:17:57] Mot de passe fourni: admin
[2025-07-18 18:17:57] Vérification du mot de passe: réussie
[2025-07-18 18:17:57] Rôle défini dans la base de données: admin
[2025-07-18 18:17:57] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 18:17:57] Connexion réussie! Token JWT généré:
[2025-07-18 18:17:57] User ID: 1
[2025-07-18 18:17:57] Username: admin
[2025-07-18 18:17:57] Rôle: admin
[2025-07-18 18:17:57] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 18:17:57] Préparation de l'envoi de la réponse JSON:
[2025-07-18 18:17:57] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NTQ3NywiZXhwIjoxNzUyOTQxODc3LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.NbmYCiv2QJHtE645jS968QEAquBeqDBrmWHM9U948lk"}
[2025-07-18 18:31:47] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 18:31:47] Méthode HTTP: POST
[2025-07-18 18:31:47] User-Agent: Non défini
[2025-07-18 18:31:47] Origin: Non défini
[2025-07-18 18:31:47] Referer: Non défini
[2025-07-18 18:31:47] En-têtes de la requête:
[2025-07-18 18:31:47]   Host: localhost
[2025-07-18 18:31:47]   Content-Type: application/json
[2025-07-18 18:31:47]   Accept: application/json
[2025-07-18 18:31:47]   Content-Length: 43
[2025-07-18 18:31:47] Données reçues: {"username":"manager","password":"manager"}
[2025-07-18 18:31:47] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 18:31:47] Connexion à la base de données réussie
[2025-07-18 18:31:47] Recherche de l'utilisateur: trouvé
[2025-07-18 18:31:47] ID utilisateur: 2
[2025-07-18 18:31:47] Rôle: manager
[2025-07-18 18:31:47] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[2025-07-18 18:31:47] Mot de passe fourni: manager
[2025-07-18 18:31:47] Vérification du mot de passe: échouée
[2025-07-18 18:31:47] Échec: Mot de passe incorrect
[2025-07-18 18:32:50] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 18:32:50] Méthode HTTP: POST
[2025-07-18 18:32:50] User-Agent: Non défini
[2025-07-18 18:32:50] Origin: Non défini
[2025-07-18 18:32:50] Referer: Non défini
[2025-07-18 18:32:50] En-têtes de la requête:
[2025-07-18 18:32:50]   Host: localhost
[2025-07-18 18:32:50]   Content-Type: application/json
[2025-07-18 18:32:50]   Accept: application/json
[2025-07-18 18:32:50]   Content-Length: 43
[2025-07-18 18:32:50] Données reçues: {"username":"manager","password":"manager"}
[2025-07-18 18:32:50] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 18:32:50] Connexion à la base de données réussie
[2025-07-18 18:32:50] Recherche de l'utilisateur: trouvé
[2025-07-18 18:32:50] ID utilisateur: 2
[2025-07-18 18:32:50] Rôle: manager
[2025-07-18 18:32:50] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-18 18:32:50] Mot de passe fourni: manager
[2025-07-18 18:32:50] Vérification du mot de passe: réussie
[2025-07-18 18:32:50] Rôle défini dans la base de données: manager
[2025-07-18 18:32:50] Connexion réussie! Token JWT généré:
[2025-07-18 18:32:50] User ID: 2
[2025-07-18 18:32:50] Username: manager
[2025-07-18 18:32:50] Rôle: manager
[2025-07-18 18:32:50] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 18:32:50] Préparation de l'envoi de la réponse JSON:
[2025-07-18 18:32:50] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NjM3MCwiZXhwIjoxNzUyOTQyNzcwLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.vX2mDEovZacLImEheU64REhcwKA1sCMewjFNyJzuZQQ"}
[2025-07-18 18:42:27] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 18:42:27] Méthode HTTP: POST
[2025-07-18 18:42:27] User-Agent: Non défini
[2025-07-18 18:42:27] Origin: Non défini
[2025-07-18 18:42:27] Referer: Non défini
[2025-07-18 18:42:27] En-têtes de la requête:
[2025-07-18 18:42:27]   Host: localhost
[2025-07-18 18:42:27]   Content-Type: application/json
[2025-07-18 18:42:27]   Accept: application/json
[2025-07-18 18:42:27]   Content-Length: 43
[2025-07-18 18:42:27] Données reçues: {"username":"manager","password":"manager"}
[2025-07-18 18:42:27] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 18:42:27] Connexion à la base de données réussie
[2025-07-18 18:42:27] Recherche de l'utilisateur: trouvé
[2025-07-18 18:42:27] ID utilisateur: 2
[2025-07-18 18:42:27] Rôle: manager
[2025-07-18 18:42:27] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-18 18:42:27] Mot de passe fourni: manager
[2025-07-18 18:42:27] Vérification du mot de passe: réussie
[2025-07-18 18:42:27] Rôle défini dans la base de données: manager
[2025-07-18 18:42:27] Connexion réussie! Token JWT généré:
[2025-07-18 18:42:27] User ID: 2
[2025-07-18 18:42:27] Username: manager
[2025-07-18 18:42:27] Rôle: manager
[2025-07-18 18:42:27] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 18:42:27] Préparation de l'envoi de la réponse JSON:
[2025-07-18 18:42:27] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1Njk0NywiZXhwIjoxNzUyOTQzMzQ3LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.RO4bBccg4aM3AOFdCig4FmYCU1fcCyvcCXeEsFoqb_Q"}
[2025-07-18 19:55:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 19:55:42] Méthode HTTP: POST
[2025-07-18 19:55:42] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 19:55:42] Origin: http://localhost
[2025-07-18 19:55:42] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_application_complete.html
[2025-07-18 19:55:42] En-têtes de la requête:
[2025-07-18 19:55:42]   Host: localhost
[2025-07-18 19:55:42]   Connection: keep-alive
[2025-07-18 19:55:42]   Content-Length: 39
[2025-07-18 19:55:42]   sec-ch-ua-platform: "Windows"
[2025-07-18 19:55:42]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 19:55:42]   X-KL-Ajax-Request: Ajax_Request
[2025-07-18 19:55:42]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 19:55:42]   Content-Type: application/json
[2025-07-18 19:55:42]   sec-ch-ua-mobile: ?0
[2025-07-18 19:55:42]   Accept: */*
[2025-07-18 19:55:42]   Origin: http://localhost
[2025-07-18 19:55:42]   Sec-Fetch-Site: same-origin
[2025-07-18 19:55:42]   Sec-Fetch-Mode: cors
[2025-07-18 19:55:42]   Sec-Fetch-Dest: empty
[2025-07-18 19:55:42]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_application_complete.html
[2025-07-18 19:55:42]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 19:55:42]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 19:55:42] Données reçues: {"username":"admin","password":"admin"}
[2025-07-18 19:55:42] Tentative de connexion pour l'utilisateur: admin
[2025-07-18 19:55:42] Connexion à la base de données réussie
[2025-07-18 19:55:42] Recherche de l'utilisateur: trouvé
[2025-07-18 19:55:42] ID utilisateur: 1
[2025-07-18 19:55:42] Rôle: admin
[2025-07-18 19:55:42] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-18 19:55:42] Mot de passe fourni: admin
[2025-07-18 19:55:42] Vérification du mot de passe: réussie
[2025-07-18 19:55:42] Rôle défini dans la base de données: admin
[2025-07-18 19:55:42] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-18 19:55:42] Connexion réussie! Token JWT généré:
[2025-07-18 19:55:42] User ID: 1
[2025-07-18 19:55:42] Username: admin
[2025-07-18 19:55:42] Rôle: admin
[2025-07-18 19:55:42] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 19:55:42] Préparation de l'envoi de la réponse JSON:
[2025-07-18 19:55:42] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg2MTM0MiwiZXhwIjoxNzUyOTQ3NzQyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.2X11xQzi4iE63z0isZnEnmCYXGILycOvQvpClNC5Agg"}
[2025-07-18 20:04:05] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 20:04:05] Méthode HTTP: POST
[2025-07-18 20:04:05] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 20:04:05] Origin: http://localhost
[2025-07-18 20:04:05] Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_final_cors.html
[2025-07-18 20:04:05] En-têtes de la requête:
[2025-07-18 20:04:05]   Host: localhost
[2025-07-18 20:04:05]   Connection: keep-alive
[2025-07-18 20:04:05]   Content-Length: 43
[2025-07-18 20:04:05]   sec-ch-ua-platform: "Windows"
[2025-07-18 20:04:05]   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-18 20:04:05]   X-KL-Ajax-Request: Ajax_Request
[2025-07-18 20:04:05]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-18 20:04:05]   Content-Type: application/json
[2025-07-18 20:04:05]   sec-ch-ua-mobile: ?0
[2025-07-18 20:04:05]   Accept: */*
[2025-07-18 20:04:05]   Origin: http://localhost
[2025-07-18 20:04:05]   Sec-Fetch-Site: same-origin
[2025-07-18 20:04:05]   Sec-Fetch-Mode: cors
[2025-07-18 20:04:05]   Sec-Fetch-Dest: empty
[2025-07-18 20:04:05]   Referer: http://localhost/Gestion_moulin_wifiZone_ok/test_final_cors.html
[2025-07-18 20:04:05]   Accept-Encoding: gzip, deflate, br, zstd
[2025-07-18 20:04:05]   Accept-Language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-18 20:04:05] Données reçues: {"username":"manager","password":"manager"}
[2025-07-18 20:04:05] Tentative de connexion pour l'utilisateur: manager
[2025-07-18 20:04:05] Connexion à la base de données réussie
[2025-07-18 20:04:05] Recherche de l'utilisateur: trouvé
[2025-07-18 20:04:05] ID utilisateur: 2
[2025-07-18 20:04:05] Rôle: manager
[2025-07-18 20:04:05] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-18 20:04:05] Mot de passe fourni: manager
[2025-07-18 20:04:05] Vérification du mot de passe: réussie
[2025-07-18 20:04:05] Rôle défini dans la base de données: manager
[2025-07-18 20:04:05] Connexion réussie! Token JWT généré:
[2025-07-18 20:04:05] User ID: 2
[2025-07-18 20:04:05] Username: manager
[2025-07-18 20:04:05] Rôle: manager
[2025-07-18 20:04:05] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-18 20:04:05] Préparation de l'envoi de la réponse JSON:
[2025-07-18 20:04:05] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg2MTg0NSwiZXhwIjoxNzUyOTQ4MjQ1LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.7hrfg3xS8R44uW2jlniv-uBXVsPjRBKgzwV5WRnYj04"}
[2025-07-18 20:38:22] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 20:38:22] Méthode HTTP: HEAD
[2025-07-18 20:38:22] User-Agent: Non défini
[2025-07-18 20:38:22] Origin: Non défini
[2025-07-18 20:38:22] Referer: Non défini
[2025-07-18 20:38:22] En-têtes de la requête:
[2025-07-18 20:38:22]   Host: localhost
[2025-07-18 20:38:22]   Accept: */*
[2025-07-18 20:38:22] Méthode non autorisée: HEAD
[2025-07-18 20:39:34] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-18 20:39:34] Méthode HTTP: HEAD
[2025-07-18 20:39:34] User-Agent: Non défini
[2025-07-18 20:39:34] Origin: Non défini
[2025-07-18 20:39:34] Referer: Non défini
[2025-07-18 20:39:34] En-têtes de la requête:
[2025-07-18 20:39:34]   Host: localhost
[2025-07-18 20:39:34]   Accept: */*
[2025-07-18 20:39:34] Méthode non autorisée: HEAD
[2025-07-21 10:28:38] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-21 10:28:38] Méthode HTTP: POST
[2025-07-21 10:28:38] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:28:38] Origin: http://localhost:8080
[2025-07-21 10:28:38] Referer: http://localhost:8080/login
[2025-07-21 10:28:38] En-têtes de la requête:
[2025-07-21 10:28:38]   host: localhost
[2025-07-21 10:28:38]   connection: close
[2025-07-21 10:28:38]   content-length: 39
[2025-07-21 10:28:38]   sec-ch-ua-platform: "Windows"
[2025-07-21 10:28:38]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:28:38]   accept: application/json
[2025-07-21 10:28:38]   content-type: application/json
[2025-07-21 10:28:38]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-21 10:28:38]   sec-ch-ua-mobile: ?0
[2025-07-21 10:28:38]   origin: http://localhost:8080
[2025-07-21 10:28:38]   sec-fetch-site: same-origin
[2025-07-21 10:28:38]   sec-fetch-mode: cors
[2025-07-21 10:28:38]   sec-fetch-dest: empty
[2025-07-21 10:28:38]   referer: http://localhost:8080/login
[2025-07-21 10:28:38]   accept-encoding: gzip, deflate, br, zstd
[2025-07-21 10:28:38]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-21 10:28:39] Données reçues: {"username":"admin","password":"admin"}
[2025-07-21 10:28:39] Tentative de connexion pour l'utilisateur: admin
[2025-07-21 10:28:39] Connexion à la base de données réussie
[2025-07-21 10:28:39] Recherche de l'utilisateur: trouvé
[2025-07-21 10:28:39] ID utilisateur: 1
[2025-07-21 10:28:39] Rôle: admin
[2025-07-21 10:28:39] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-21 10:28:39] Mot de passe fourni: admin
[2025-07-21 10:28:39] Vérification du mot de passe: réussie
[2025-07-21 10:28:39] Rôle défini dans la base de données: admin
[2025-07-21 10:28:39] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-21 10:28:39] Connexion réussie! Token JWT généré:
[2025-07-21 10:28:39] User ID: 1
[2025-07-21 10:28:39] Username: admin
[2025-07-21 10:28:39] Rôle: admin
[2025-07-21 10:28:39] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-21 10:28:39] Préparation de l'envoi de la réponse JSON:
[2025-07-21 10:28:39] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzA4NjUxOSwiZXhwIjoxNzUzMTcyOTE5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.RJ5krXGIuAJAXnbmP1pfCnZsdYhQveNrvOMyL0XEy_s"}
[2025-07-21 10:29:55] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-21 10:29:55] Méthode HTTP: POST
[2025-07-21 10:29:55] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:29:55] Origin: http://localhost:8080
[2025-07-21 10:29:55] Referer: http://localhost:8080/login
[2025-07-21 10:29:55] En-têtes de la requête:
[2025-07-21 10:29:55]   host: localhost
[2025-07-21 10:29:55]   connection: close
[2025-07-21 10:29:55]   content-length: 39
[2025-07-21 10:29:55]   sec-ch-ua-platform: "Windows"
[2025-07-21 10:29:55]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:29:55]   accept: application/json
[2025-07-21 10:29:55]   x-kl-ajax-request: Ajax_Request
[2025-07-21 10:29:55]   content-type: application/json
[2025-07-21 10:29:55]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-21 10:29:55]   sec-ch-ua-mobile: ?0
[2025-07-21 10:29:55]   origin: http://localhost:8080
[2025-07-21 10:29:55]   sec-fetch-site: same-origin
[2025-07-21 10:29:55]   sec-fetch-mode: cors
[2025-07-21 10:29:55]   sec-fetch-dest: empty
[2025-07-21 10:29:55]   referer: http://localhost:8080/login
[2025-07-21 10:29:55]   accept-encoding: gzip, deflate, br, zstd
[2025-07-21 10:29:55]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-21 10:29:55] Données reçues: {"username":"admin","password":"admin"}
[2025-07-21 10:29:55] Tentative de connexion pour l'utilisateur: admin
[2025-07-21 10:29:55] Connexion à la base de données réussie
[2025-07-21 10:29:55] Recherche de l'utilisateur: trouvé
[2025-07-21 10:29:55] ID utilisateur: 1
[2025-07-21 10:29:55] Rôle: admin
[2025-07-21 10:29:55] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-21 10:29:55] Mot de passe fourni: admin
[2025-07-21 10:29:55] Vérification du mot de passe: réussie
[2025-07-21 10:29:55] Rôle défini dans la base de données: admin
[2025-07-21 10:29:55] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-21 10:29:55] Connexion réussie! Token JWT généré:
[2025-07-21 10:29:55] User ID: 1
[2025-07-21 10:29:55] Username: admin
[2025-07-21 10:29:55] Rôle: admin
[2025-07-21 10:29:55] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-21 10:29:55] Préparation de l'envoi de la réponse JSON:
[2025-07-21 10:29:56] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzA4NjU5NSwiZXhwIjoxNzUzMTcyOTk1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.XRGSeMJzL-OA-OKD7-fhw3tlxPSjHdYoLpS4_uKOocA"}
[2025-07-21 10:30:24] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-21 10:30:24] Méthode HTTP: POST
[2025-07-21 10:30:24] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:30:24] Origin: http://localhost:8080
[2025-07-21 10:30:24] Referer: http://localhost:8080/login
[2025-07-21 10:30:24] En-têtes de la requête:
[2025-07-21 10:30:24]   host: localhost
[2025-07-21 10:30:24]   connection: close
[2025-07-21 10:30:24]   content-length: 46
[2025-07-21 10:30:24]   sec-ch-ua-platform: "Windows"
[2025-07-21 10:30:24]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:30:24]   accept: application/json
[2025-07-21 10:30:24]   content-type: application/json
[2025-07-21 10:30:24]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-21 10:30:24]   sec-ch-ua-mobile: ?0
[2025-07-21 10:30:24]   origin: http://localhost:8080
[2025-07-21 10:30:24]   sec-fetch-site: same-origin
[2025-07-21 10:30:24]   sec-fetch-mode: cors
[2025-07-21 10:30:24]   sec-fetch-dest: empty
[2025-07-21 10:30:24]   referer: http://localhost:8080/login
[2025-07-21 10:30:24]   accept-encoding: gzip, deflate, br, zstd
[2025-07-21 10:30:24]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-21 10:30:24] Données reçues: {"username":"manager","password":"maneger123"}
[2025-07-21 10:30:24] Tentative de connexion pour l'utilisateur: manager
[2025-07-21 10:30:24] Connexion à la base de données réussie
[2025-07-21 10:30:24] Recherche de l'utilisateur: trouvé
[2025-07-21 10:30:24] ID utilisateur: 2
[2025-07-21 10:30:24] Rôle: manager
[2025-07-21 10:30:24] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-21 10:30:24] Mot de passe fourni: maneger123
[2025-07-21 10:30:24] Vérification du mot de passe: échouée
[2025-07-21 10:30:24] Échec: Mot de passe incorrect
[2025-07-21 10:30:34] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-21 10:30:34] Méthode HTTP: POST
[2025-07-21 10:30:34] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:30:34] Origin: http://localhost:8080
[2025-07-21 10:30:34] Referer: http://localhost:8080/login
[2025-07-21 10:30:34] En-têtes de la requête:
[2025-07-21 10:30:34]   host: localhost
[2025-07-21 10:30:34]   connection: close
[2025-07-21 10:30:34]   content-length: 43
[2025-07-21 10:30:34]   sec-ch-ua-platform: "Windows"
[2025-07-21 10:30:34]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-21 10:30:34]   accept: application/json
[2025-07-21 10:30:34]   content-type: application/json
[2025-07-21 10:30:34]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-21 10:30:34]   sec-ch-ua-mobile: ?0
[2025-07-21 10:30:34]   origin: http://localhost:8080
[2025-07-21 10:30:34]   sec-fetch-site: same-origin
[2025-07-21 10:30:34]   sec-fetch-mode: cors
[2025-07-21 10:30:34]   sec-fetch-dest: empty
[2025-07-21 10:30:34]   referer: http://localhost:8080/login
[2025-07-21 10:30:34]   accept-encoding: gzip, deflate, br, zstd
[2025-07-21 10:30:34]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-21 10:30:34] Données reçues: {"username":"manager","password":"manager"}
[2025-07-21 10:30:34] Tentative de connexion pour l'utilisateur: manager
[2025-07-21 10:30:34] Connexion à la base de données réussie
[2025-07-21 10:30:34] Recherche de l'utilisateur: trouvé
[2025-07-21 10:30:34] ID utilisateur: 2
[2025-07-21 10:30:34] Rôle: manager
[2025-07-21 10:30:34] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-21 10:30:34] Mot de passe fourni: manager
[2025-07-21 10:30:34] Vérification du mot de passe: réussie
[2025-07-21 10:30:34] Rôle défini dans la base de données: manager
[2025-07-21 10:30:34] Connexion réussie! Token JWT généré:
[2025-07-21 10:30:34] User ID: 2
[2025-07-21 10:30:34] Username: manager
[2025-07-21 10:30:34] Rôle: manager
[2025-07-21 10:30:34] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-21 10:30:34] Préparation de l'envoi de la réponse JSON:
[2025-07-21 10:30:34] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzA4NjYzNCwiZXhwIjoxNzUzMTczMDM0LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.p-whn_zP50867LwAH4-XN03P2KcQUuOf3arbtjePYqE"}
[2025-07-22 23:02:29] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-22 23:02:29] Méthode HTTP: POST
[2025-07-22 23:02:29] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:02:29] Origin: http://localhost:8080
[2025-07-22 23:02:29] Referer: http://localhost:8080/login
[2025-07-22 23:02:29] En-têtes de la requête:
[2025-07-22 23:02:29]   host: localhost
[2025-07-22 23:02:29]   connection: close
[2025-07-22 23:02:29]   content-length: 46
[2025-07-22 23:02:29]   sec-ch-ua-platform: "Windows"
[2025-07-22 23:02:29]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:02:29]   accept: application/json
[2025-07-22 23:02:29]   x-kl-ajax-request: Ajax_Request
[2025-07-22 23:02:29]   content-type: application/json
[2025-07-22 23:02:29]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-22 23:02:29]   sec-ch-ua-mobile: ?0
[2025-07-22 23:02:29]   origin: http://localhost:8080
[2025-07-22 23:02:29]   sec-fetch-site: same-origin
[2025-07-22 23:02:29]   sec-fetch-mode: cors
[2025-07-22 23:02:29]   sec-fetch-dest: empty
[2025-07-22 23:02:29]   referer: http://localhost:8080/login
[2025-07-22 23:02:29]   accept-encoding: gzip, deflate, br, zstd
[2025-07-22 23:02:29]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-22 23:02:30] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-22 23:02:30] Tentative de connexion pour l'utilisateur: manager
[2025-07-22 23:02:30] Connexion à la base de données réussie
[2025-07-22 23:02:30] Recherche de l'utilisateur: trouvé
[2025-07-22 23:02:30] ID utilisateur: 2
[2025-07-22 23:02:30] Rôle: manager
[2025-07-22 23:02:30] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-22 23:02:30] Mot de passe fourni: manager123
[2025-07-22 23:02:30] Vérification du mot de passe: échouée
[2025-07-22 23:02:30] Échec: Mot de passe incorrect
[2025-07-22 23:02:32] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-22 23:02:32] Méthode HTTP: POST
[2025-07-22 23:02:32] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:02:32] Origin: http://localhost:8080
[2025-07-22 23:02:32] Referer: http://localhost:8080/login
[2025-07-22 23:02:32] En-têtes de la requête:
[2025-07-22 23:02:32]   host: localhost
[2025-07-22 23:02:32]   connection: close
[2025-07-22 23:02:32]   content-length: 46
[2025-07-22 23:02:32]   sec-ch-ua-platform: "Windows"
[2025-07-22 23:02:32]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:02:32]   accept: application/json
[2025-07-22 23:02:32]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-22 23:02:32]   content-type: application/json
[2025-07-22 23:02:32]   sec-ch-ua-mobile: ?0
[2025-07-22 23:02:32]   origin: http://localhost:8080
[2025-07-22 23:02:32]   sec-fetch-site: same-origin
[2025-07-22 23:02:32]   sec-fetch-mode: cors
[2025-07-22 23:02:32]   sec-fetch-dest: empty
[2025-07-22 23:02:32]   referer: http://localhost:8080/login
[2025-07-22 23:02:32]   accept-encoding: gzip, deflate, br, zstd
[2025-07-22 23:02:32]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-22 23:02:32] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-22 23:02:32] Tentative de connexion pour l'utilisateur: manager
[2025-07-22 23:02:32] Connexion à la base de données réussie
[2025-07-22 23:02:32] Recherche de l'utilisateur: trouvé
[2025-07-22 23:02:32] ID utilisateur: 2
[2025-07-22 23:02:32] Rôle: manager
[2025-07-22 23:02:32] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-22 23:02:32] Mot de passe fourni: manager123
[2025-07-22 23:02:32] Vérification du mot de passe: échouée
[2025-07-22 23:02:32] Échec: Mot de passe incorrect
[2025-07-22 23:02:43] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-22 23:02:43] Méthode HTTP: POST
[2025-07-22 23:02:43] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:02:43] Origin: http://localhost:8080
[2025-07-22 23:02:43] Referer: http://localhost:8080/login
[2025-07-22 23:02:43] En-têtes de la requête:
[2025-07-22 23:02:43]   host: localhost
[2025-07-22 23:02:43]   connection: close
[2025-07-22 23:02:43]   content-length: 43
[2025-07-22 23:02:43]   sec-ch-ua-platform: "Windows"
[2025-07-22 23:02:43]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:02:43]   accept: application/json
[2025-07-22 23:02:43]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-22 23:02:43]   content-type: application/json
[2025-07-22 23:02:43]   sec-ch-ua-mobile: ?0
[2025-07-22 23:02:43]   origin: http://localhost:8080
[2025-07-22 23:02:43]   sec-fetch-site: same-origin
[2025-07-22 23:02:43]   sec-fetch-mode: cors
[2025-07-22 23:02:43]   sec-fetch-dest: empty
[2025-07-22 23:02:43]   referer: http://localhost:8080/login
[2025-07-22 23:02:43]   accept-encoding: gzip, deflate, br, zstd
[2025-07-22 23:02:43]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-22 23:02:43] Données reçues: {"username":"manager","password":"manager"}
[2025-07-22 23:02:43] Tentative de connexion pour l'utilisateur: manager
[2025-07-22 23:02:43] Connexion à la base de données réussie
[2025-07-22 23:02:43] Recherche de l'utilisateur: trouvé
[2025-07-22 23:02:43] ID utilisateur: 2
[2025-07-22 23:02:43] Rôle: manager
[2025-07-22 23:02:43] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-22 23:02:43] Mot de passe fourni: manager
[2025-07-22 23:02:43] Vérification du mot de passe: réussie
[2025-07-22 23:02:43] Rôle défini dans la base de données: manager
[2025-07-22 23:02:43] Connexion réussie! Token JWT généré:
[2025-07-22 23:02:43] User ID: 2
[2025-07-22 23:02:43] Username: manager
[2025-07-22 23:02:43] Rôle: manager
[2025-07-22 23:02:43] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-22 23:02:43] Préparation de l'envoi de la réponse JSON:
[2025-07-22 23:02:43] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzIxODE2MywiZXhwIjoxNzUzMzA0NTYzLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.1Os0VCOf1PTnRmXojA-K8ZQ7xTe85t0AMVhwNijwL1E"}
[2025-07-22 23:43:04] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-22 23:43:04] Méthode HTTP: POST
[2025-07-22 23:43:04] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:43:04] Origin: http://localhost:8080
[2025-07-22 23:43:04] Referer: http://localhost:8080/login
[2025-07-22 23:43:04] En-têtes de la requête:
[2025-07-22 23:43:04]   host: localhost
[2025-07-22 23:43:04]   connection: close
[2025-07-22 23:43:04]   content-length: 46
[2025-07-22 23:43:04]   sec-ch-ua-platform: "Windows"
[2025-07-22 23:43:04]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:43:04]   accept: application/json
[2025-07-22 23:43:04]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-22 23:43:04]   content-type: application/json
[2025-07-22 23:43:04]   sec-ch-ua-mobile: ?0
[2025-07-22 23:43:04]   origin: http://localhost:8080
[2025-07-22 23:43:04]   sec-fetch-site: same-origin
[2025-07-22 23:43:04]   sec-fetch-mode: cors
[2025-07-22 23:43:04]   sec-fetch-dest: empty
[2025-07-22 23:43:04]   referer: http://localhost:8080/login
[2025-07-22 23:43:04]   accept-encoding: gzip, deflate, br, zstd
[2025-07-22 23:43:04]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-22 23:43:04] Données reçues: {"username":"manager","password":"manager123"}
[2025-07-22 23:43:04] Tentative de connexion pour l'utilisateur: manager
[2025-07-22 23:43:04] Connexion à la base de données réussie
[2025-07-22 23:43:04] Recherche de l'utilisateur: trouvé
[2025-07-22 23:43:04] ID utilisateur: 2
[2025-07-22 23:43:04] Rôle: manager
[2025-07-22 23:43:04] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-22 23:43:04] Mot de passe fourni: manager123
[2025-07-22 23:43:04] Vérification du mot de passe: échouée
[2025-07-22 23:43:04] Échec: Mot de passe incorrect
[2025-07-22 23:43:18] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-22 23:43:18] Méthode HTTP: POST
[2025-07-22 23:43:18] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:43:18] Origin: http://localhost:8080
[2025-07-22 23:43:18] Referer: http://localhost:8080/login
[2025-07-22 23:43:18] En-têtes de la requête:
[2025-07-22 23:43:18]   host: localhost
[2025-07-22 23:43:18]   connection: close
[2025-07-22 23:43:18]   content-length: 43
[2025-07-22 23:43:18]   sec-ch-ua-platform: "Windows"
[2025-07-22 23:43:18]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-22 23:43:18]   accept: application/json
[2025-07-22 23:43:18]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-22 23:43:18]   content-type: application/json
[2025-07-22 23:43:18]   sec-ch-ua-mobile: ?0
[2025-07-22 23:43:18]   origin: http://localhost:8080
[2025-07-22 23:43:18]   sec-fetch-site: same-origin
[2025-07-22 23:43:18]   sec-fetch-mode: cors
[2025-07-22 23:43:18]   sec-fetch-dest: empty
[2025-07-22 23:43:18]   referer: http://localhost:8080/login
[2025-07-22 23:43:18]   accept-encoding: gzip, deflate, br, zstd
[2025-07-22 23:43:18]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-22 23:43:18] Données reçues: {"username":"manager","password":"manager"}
[2025-07-22 23:43:18] Tentative de connexion pour l'utilisateur: manager
[2025-07-22 23:43:18] Connexion à la base de données réussie
[2025-07-22 23:43:18] Recherche de l'utilisateur: trouvé
[2025-07-22 23:43:18] ID utilisateur: 2
[2025-07-22 23:43:18] Rôle: manager
[2025-07-22 23:43:18] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-22 23:43:18] Mot de passe fourni: manager
[2025-07-22 23:43:18] Vérification du mot de passe: réussie
[2025-07-22 23:43:18] Rôle défini dans la base de données: manager
[2025-07-22 23:43:18] Connexion réussie! Token JWT généré:
[2025-07-22 23:43:18] User ID: 2
[2025-07-22 23:43:18] Username: manager
[2025-07-22 23:43:18] Rôle: manager
[2025-07-22 23:43:18] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-22 23:43:18] Préparation de l'envoi de la réponse JSON:
[2025-07-22 23:43:18] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzIyMDU5OCwiZXhwIjoxNzUzMzA2OTk4LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.HqLS8pHnlA26MAC5XcSRI46v5ugewo_tvBgwqacnKpQ"}
[2025-07-23 10:38:13] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-23 10:38:13] Méthode HTTP: POST
[2025-07-23 10:38:13] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-23 10:38:13] Origin: http://localhost:8080
[2025-07-23 10:38:13] Referer: http://localhost:8080/login
[2025-07-23 10:38:13] En-têtes de la requête:
[2025-07-23 10:38:13]   host: localhost
[2025-07-23 10:38:13]   connection: close
[2025-07-23 10:38:13]   content-length: 39
[2025-07-23 10:38:13]   sec-ch-ua-platform: "Windows"
[2025-07-23 10:38:13]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-23 10:38:13]   accept: application/json
[2025-07-23 10:38:13]   content-type: application/json
[2025-07-23 10:38:13]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-23 10:38:13]   sec-ch-ua-mobile: ?0
[2025-07-23 10:38:13]   origin: http://localhost:8080
[2025-07-23 10:38:13]   sec-fetch-site: same-origin
[2025-07-23 10:38:13]   sec-fetch-mode: cors
[2025-07-23 10:38:13]   sec-fetch-dest: empty
[2025-07-23 10:38:13]   referer: http://localhost:8080/login
[2025-07-23 10:38:13]   accept-encoding: gzip, deflate, br, zstd
[2025-07-23 10:38:13]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-23 10:38:13] Données reçues: {"username":"admin","password":"admin"}
[2025-07-23 10:38:13] Tentative de connexion pour l'utilisateur: admin
[2025-07-23 10:38:13] Connexion à la base de données réussie
[2025-07-23 10:38:13] Recherche de l'utilisateur: trouvé
[2025-07-23 10:38:13] ID utilisateur: 1
[2025-07-23 10:38:13] Rôle: admin
[2025-07-23 10:38:13] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-23 10:38:13] Mot de passe fourni: admin
[2025-07-23 10:38:13] Vérification du mot de passe: réussie
[2025-07-23 10:38:13] Rôle défini dans la base de données: admin
[2025-07-23 10:38:13] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-23 10:38:13] Connexion réussie! Token JWT généré:
[2025-07-23 10:38:13] User ID: 1
[2025-07-23 10:38:13] Username: admin
[2025-07-23 10:38:13] Rôle: admin
[2025-07-23 10:38:13] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-23 10:38:13] Préparation de l'envoi de la réponse JSON:
[2025-07-23 10:38:13] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzI1OTg5MywiZXhwIjoxNzUzMzQ2MjkzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.R8Mz77rOZ_Mu5ZTBC6pqUYeUgfkdzl4tjptVdl3_xQs"}
[2025-07-23 10:38:42] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-23 10:38:42] Méthode HTTP: POST
[2025-07-23 10:38:42] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-23 10:38:42] Origin: http://localhost:8080
[2025-07-23 10:38:42] Referer: http://localhost:8080/login
[2025-07-23 10:38:42] En-têtes de la requête:
[2025-07-23 10:38:42]   host: localhost
[2025-07-23 10:38:42]   connection: close
[2025-07-23 10:38:42]   content-length: 43
[2025-07-23 10:38:42]   sec-ch-ua-platform: "Windows"
[2025-07-23 10:38:42]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-23 10:38:42]   accept: application/json
[2025-07-23 10:38:42]   content-type: application/json
[2025-07-23 10:38:42]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-23 10:38:42]   sec-ch-ua-mobile: ?0
[2025-07-23 10:38:42]   origin: http://localhost:8080
[2025-07-23 10:38:42]   sec-fetch-site: same-origin
[2025-07-23 10:38:42]   sec-fetch-mode: cors
[2025-07-23 10:38:42]   sec-fetch-dest: empty
[2025-07-23 10:38:42]   referer: http://localhost:8080/login
[2025-07-23 10:38:42]   accept-encoding: gzip, deflate, br, zstd
[2025-07-23 10:38:42]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-23 10:38:42] Données reçues: {"username":"manager","password":"manager"}
[2025-07-23 10:38:42] Tentative de connexion pour l'utilisateur: manager
[2025-07-23 10:38:42] Connexion à la base de données réussie
[2025-07-23 10:38:42] Recherche de l'utilisateur: trouvé
[2025-07-23 10:38:42] ID utilisateur: 2
[2025-07-23 10:38:42] Rôle: manager
[2025-07-23 10:38:42] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-23 10:38:42] Mot de passe fourni: manager
[2025-07-23 10:38:42] Vérification du mot de passe: réussie
[2025-07-23 10:38:42] Rôle défini dans la base de données: manager
[2025-07-23 10:38:42] Connexion réussie! Token JWT généré:
[2025-07-23 10:38:42] User ID: 2
[2025-07-23 10:38:42] Username: manager
[2025-07-23 10:38:42] Rôle: manager
[2025-07-23 10:38:42] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-23 10:38:42] Préparation de l'envoi de la réponse JSON:
[2025-07-23 10:38:42] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzI1OTkyMiwiZXhwIjoxNzUzMzQ2MzIyLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.4wg6sLxo2iGQgmDXFABUQl-rh9BRyvFY13pTpLdBKEQ"}
[2025-07-26 20:51:02] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-26 20:51:02] Méthode HTTP: POST
[2025-07-26 20:51:02] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-26 20:51:02] Origin: http://localhost:8080
[2025-07-26 20:51:02] Referer: http://localhost:8080/login
[2025-07-26 20:51:02] En-têtes de la requête:
[2025-07-26 20:51:02]   host: localhost
[2025-07-26 20:51:02]   connection: close
[2025-07-26 20:51:02]   content-length: 39
[2025-07-26 20:51:02]   sec-ch-ua-platform: "Windows"
[2025-07-26 20:51:02]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-26 20:51:02]   accept: application/json
[2025-07-26 20:51:02]   content-type: application/json
[2025-07-26 20:51:02]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-26 20:51:02]   sec-ch-ua-mobile: ?0
[2025-07-26 20:51:02]   origin: http://localhost:8080
[2025-07-26 20:51:02]   sec-fetch-site: same-origin
[2025-07-26 20:51:02]   sec-fetch-mode: cors
[2025-07-26 20:51:02]   sec-fetch-dest: empty
[2025-07-26 20:51:02]   referer: http://localhost:8080/login
[2025-07-26 20:51:02]   accept-encoding: gzip, deflate, br, zstd
[2025-07-26 20:51:02]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-26 20:51:02] Données reçues: {"username":"admin","password":"admin"}
[2025-07-26 20:51:02] Tentative de connexion pour l'utilisateur: admin
[2025-07-26 20:51:02] Connexion à la base de données réussie
[2025-07-26 20:51:02] Recherche de l'utilisateur: trouvé
[2025-07-26 20:51:02] ID utilisateur: 1
[2025-07-26 20:51:02] Rôle: admin
[2025-07-26 20:51:02] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-26 20:51:02] Mot de passe fourni: admin
[2025-07-26 20:51:02] Vérification du mot de passe: réussie
[2025-07-26 20:51:02] Rôle défini dans la base de données: admin
[2025-07-26 20:51:02] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-26 20:51:02] Connexion réussie! Token JWT généré:
[2025-07-26 20:51:02] User ID: 1
[2025-07-26 20:51:02] Username: admin
[2025-07-26 20:51:02] Rôle: admin
[2025-07-26 20:51:02] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-26 20:51:02] Préparation de l'envoi de la réponse JSON:
[2025-07-26 20:51:02] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzU1NTg2MiwiZXhwIjoxNzUzNjQyMjYyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.DBVX6qzTUZs7ueoAxqFLxdQlRzstm_RD8wnkMQqUdxU"}
[2025-07-27 10:03:03] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-27 10:03:03] Méthode HTTP: POST
[2025-07-27 10:03:03] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-27 10:03:03] Origin: http://localhost:8080
[2025-07-27 10:03:03] Referer: http://localhost:8080/login
[2025-07-27 10:03:03] En-têtes de la requête:
[2025-07-27 10:03:03]   host: localhost
[2025-07-27 10:03:03]   connection: close
[2025-07-27 10:03:03]   content-length: 39
[2025-07-27 10:03:03]   sec-ch-ua-platform: "Windows"
[2025-07-27 10:03:03]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-27 10:03:03]   accept: application/json
[2025-07-27 10:03:03]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-27 10:03:03]   content-type: application/json
[2025-07-27 10:03:03]   sec-ch-ua-mobile: ?0
[2025-07-27 10:03:03]   origin: http://localhost:8080
[2025-07-27 10:03:03]   sec-fetch-site: same-origin
[2025-07-27 10:03:03]   sec-fetch-mode: cors
[2025-07-27 10:03:03]   sec-fetch-dest: empty
[2025-07-27 10:03:03]   referer: http://localhost:8080/login
[2025-07-27 10:03:03]   accept-encoding: gzip, deflate, br, zstd
[2025-07-27 10:03:03]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-27 10:03:03] Données reçues: {"username":"admin","password":"admin"}
[2025-07-27 10:03:03] Tentative de connexion pour l'utilisateur: admin
[2025-07-27 10:03:03] Connexion à la base de données réussie
[2025-07-27 10:03:03] Recherche de l'utilisateur: trouvé
[2025-07-27 10:03:03] ID utilisateur: 1
[2025-07-27 10:03:03] Rôle: admin
[2025-07-27 10:03:03] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-27 10:03:04] Mot de passe fourni: admin
[2025-07-27 10:03:04] Vérification du mot de passe: réussie
[2025-07-27 10:03:04] Rôle défini dans la base de données: admin
[2025-07-27 10:03:04] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-27 10:03:04] Connexion réussie! Token JWT généré:
[2025-07-27 10:03:04] User ID: 1
[2025-07-27 10:03:04] Username: admin
[2025-07-27 10:03:04] Rôle: admin
[2025-07-27 10:03:04] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-27 10:03:04] Préparation de l'envoi de la réponse JSON:
[2025-07-27 10:03:04] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzYwMzM4NCwiZXhwIjoxNzUzNjg5Nzg0LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.ql-RZ1lD9G_3WN4rrmsO_qABAToqNssKU09F3HF3qN8"}
[2025-07-28 19:31:40] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 19:31:41] Méthode HTTP: POST
[2025-07-28 19:31:41] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 19:31:41] Origin: http://localhost:8080
[2025-07-28 19:31:41] Referer: http://localhost:8080/login
[2025-07-28 19:31:41] En-têtes de la requête:
[2025-07-28 19:31:41]   host: localhost
[2025-07-28 19:31:41]   connection: close
[2025-07-28 19:31:41]   content-length: 39
[2025-07-28 19:31:41]   sec-ch-ua-platform: "Windows"
[2025-07-28 19:31:41]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 19:31:41]   accept: application/json
[2025-07-28 19:31:41]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-28 19:31:41]   content-type: application/json
[2025-07-28 19:31:41]   sec-ch-ua-mobile: ?0
[2025-07-28 19:31:41]   origin: http://localhost:8080
[2025-07-28 19:31:41]   sec-fetch-site: same-origin
[2025-07-28 19:31:41]   sec-fetch-mode: cors
[2025-07-28 19:31:41]   sec-fetch-dest: empty
[2025-07-28 19:31:41]   referer: http://localhost:8080/login
[2025-07-28 19:31:41]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 19:31:41]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-28 19:31:41] Données reçues: {"username":"admin","password":"admin"}
[2025-07-28 19:31:41] Tentative de connexion pour l'utilisateur: admin
[2025-07-28 19:31:41] Connexion à la base de données réussie
[2025-07-28 19:31:41] Recherche de l'utilisateur: trouvé
[2025-07-28 19:31:41] ID utilisateur: 1
[2025-07-28 19:31:41] Rôle: admin
[2025-07-28 19:31:41] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-28 19:31:41] Mot de passe fourni: admin
[2025-07-28 19:31:41] Vérification du mot de passe: réussie
[2025-07-28 19:31:41] Rôle défini dans la base de données: admin
[2025-07-28 19:31:41] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-28 19:31:41] Connexion réussie! Token JWT généré:
[2025-07-28 19:31:41] User ID: 1
[2025-07-28 19:31:41] Username: admin
[2025-07-28 19:31:41] Rôle: admin
[2025-07-28 19:31:41] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 19:31:41] Préparation de l'envoi de la réponse JSON:
[2025-07-28 19:31:41] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyMzkwMSwiZXhwIjoxNzUzODEwMzAxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.dme8mWITb4-bBCLdf2a790U6cQHaH7Z4JriuLmJXOMs"}
[2025-07-28 19:45:44] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 19:45:44] Méthode HTTP: POST
[2025-07-28 19:45:44] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-28 19:45:44] Origin: http://localhost:8080
[2025-07-28 19:45:44] Referer: http://localhost:8080/login
[2025-07-28 19:45:44] En-têtes de la requête:
[2025-07-28 19:45:44]   host: localhost
[2025-07-28 19:45:44]   connection: close
[2025-07-28 19:45:44]   content-length: 39
[2025-07-28 19:45:44]   sec-ch-ua-platform: "Windows"
[2025-07-28 19:45:44]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-28 19:45:44]   accept: application/json
[2025-07-28 19:45:44]   content-type: application/json
[2025-07-28 19:45:44]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-28 19:45:44]   sec-ch-ua-mobile: ?0
[2025-07-28 19:45:44]   origin: http://localhost:8080
[2025-07-28 19:45:44]   sec-fetch-site: same-origin
[2025-07-28 19:45:44]   sec-fetch-mode: cors
[2025-07-28 19:45:44]   sec-fetch-dest: empty
[2025-07-28 19:45:44]   referer: http://localhost:8080/login
[2025-07-28 19:45:44]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 19:45:44]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-28 19:45:44]   cookie: rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vWSfsrwr4%2FRKIWh2d7G3khF2weJaydWU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX1%2FIDSf2LTOiTspDuJ77sAo%2BTqMzqARt0vk%3D; n8n-auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6Ijg3MjY4Mjg5LTliYTUtNDc0Zi1hMjViLTI1NDVkMDcxNzg4ZSIsImhhc2giOiJZY1k0NE1VbDBMIiwiYnJvd3NlcklkIjoiL0VrMFg4TGM3L1FoWGZ4eU9TcGM3djg2c2pJTHB5VmRySkJ5TFMzSE1CUT0iLCJ1c2VkTWZhIjpmYWxzZSwiaWF0IjoxNzUzMjYzMDE0LCJleHAiOjE3NTM4Njc4MTR9.4ULFEEJ6kJWXwBzOIQFWaevFKPYyIwYsxePeMzEynN0; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX19nbGsmsRMcHKfLljGdrDn80e%2BpuFwiJCVRDipNPoV2d5g3C%2FIjDw9EuC3HULj%2Bqx7qRDHSTsK2OA%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2Bho9jkNdJxZHUCrAGaW05IdqD%2FLEFwiTGa2c6BhImN9WV7u3hxtVjWA965DNIfcGkwYdZq3HXZohhsg8I8gqKP9UwaqhX4K4tJTjLc%2FT6zdPsugTMxv4Dc2MjZ9O7tf%2FoxEqr%2Bw4Hm5RJnViLHI0DHu7bC6kH6J98%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX19GJSovqZqGDuA0zDWkTCQiEeQu53mjA6P4FSrO2Dest1YRl8%2BZ%2BowjT7l0rzQLo%2Bt9VVcrSEfkyPp5ZIp2RKFd0ULHvmf7SmtEiegQNX36wjugC7WyL3Y0sMEWJpOcJysNmz9aw4GDaw%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX19nYicRNEGbKoHAqzQHnq27ygeOeCFdmYUCBL%2Bf9k1ldgW8yDUDxnkCShyl9cGvhzvb1EEUHNLN%2B7Z6m2XYjwUXEgE1L6qaZIMkRJKq4sCJJ92b9Onpe%2F1Ja9W7mshlno%2BbqY0iKXGFkA%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22fd722995c7a39bfcb46792be839d8b4ff432214232d76d2ba3ef7e3c603132e5%2387268289-9ba5-474f-a25b-2545d071788e%22%2C%22%24sesid%22%3A%5B1753290268892%2C%2201983819-1fc4-7c3d-8a15-2a0cdfc0fafa%22%2C1753287827394%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fsetup%22%7D%7D; sidebar:state=true
[2025-07-28 19:45:45] Données reçues: {"username":"admin","password":"admin"}
[2025-07-28 19:45:45] Tentative de connexion pour l'utilisateur: admin
[2025-07-28 19:45:45] Connexion à la base de données réussie
[2025-07-28 19:45:45] Recherche de l'utilisateur: trouvé
[2025-07-28 19:45:45] ID utilisateur: 1
[2025-07-28 19:45:45] Rôle: admin
[2025-07-28 19:45:45] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-28 19:45:45] Mot de passe fourni: admin
[2025-07-28 19:45:45] Vérification du mot de passe: réussie
[2025-07-28 19:45:45] Rôle défini dans la base de données: admin
[2025-07-28 19:45:45] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-28 19:45:45] Connexion réussie! Token JWT généré:
[2025-07-28 19:45:45] User ID: 1
[2025-07-28 19:45:45] Username: admin
[2025-07-28 19:45:45] Rôle: admin
[2025-07-28 19:45:45] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 19:45:45] Préparation de l'envoi de la réponse JSON:
[2025-07-28 19:45:45] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyNDc0NSwiZXhwIjoxNzUzODExMTQ1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.gaRyoOqdJZD7Wl2jbh7S6N9cdCQQvYsSRous3LObK38"}
[2025-07-28 21:04:02] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 21:04:02] Méthode HTTP: POST
[2025-07-28 21:04:02] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:04:02] Origin: http://localhost:8080
[2025-07-28 21:04:02] Referer: http://localhost:8080/login
[2025-07-28 21:04:02] En-têtes de la requête:
[2025-07-28 21:04:02]   host: localhost
[2025-07-28 21:04:02]   connection: close
[2025-07-28 21:04:02]   content-length: 39
[2025-07-28 21:04:02]   sec-ch-ua-platform: "Windows"
[2025-07-28 21:04:02]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:04:02]   accept: application/json
[2025-07-28 21:04:02]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-28 21:04:02]   content-type: application/json
[2025-07-28 21:04:02]   sec-ch-ua-mobile: ?0
[2025-07-28 21:04:02]   origin: http://localhost:8080
[2025-07-28 21:04:02]   sec-fetch-site: same-origin
[2025-07-28 21:04:02]   sec-fetch-mode: cors
[2025-07-28 21:04:02]   sec-fetch-dest: empty
[2025-07-28 21:04:02]   referer: http://localhost:8080/login
[2025-07-28 21:04:02]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 21:04:02]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-28 21:04:03] Données reçues: {"username":"admin","password":"admin"}
[2025-07-28 21:04:03] Tentative de connexion pour l'utilisateur: admin
[2025-07-28 21:04:03] Connexion à la base de données réussie
[2025-07-28 21:04:03] Recherche de l'utilisateur: trouvé
[2025-07-28 21:04:03] ID utilisateur: 1
[2025-07-28 21:04:03] Rôle: admin
[2025-07-28 21:04:03] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-28 21:04:03] Mot de passe fourni: admin
[2025-07-28 21:04:03] Vérification du mot de passe: réussie
[2025-07-28 21:04:03] Rôle défini dans la base de données: admin
[2025-07-28 21:04:03] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-28 21:04:03] Connexion réussie! Token JWT généré:
[2025-07-28 21:04:03] User ID: 1
[2025-07-28 21:04:03] Username: admin
[2025-07-28 21:04:03] Rôle: admin
[2025-07-28 21:04:03] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 21:04:03] Préparation de l'envoi de la réponse JSON:
[2025-07-28 21:04:03] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyOTQ0MywiZXhwIjoxNzUzODE1ODQzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.gsyyS_H8Ah580ZAn4FdqCLmbpj7ZjlFTpsJUdI8KOcU"}
[2025-07-28 21:05:31] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 21:05:31] Méthode HTTP: POST
[2025-07-28 21:05:31] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-28 21:05:31] Origin: http://localhost:8080
[2025-07-28 21:05:31] Referer: http://localhost:8080/login
[2025-07-28 21:05:31] En-têtes de la requête:
[2025-07-28 21:05:31]   host: localhost
[2025-07-28 21:05:31]   connection: close
[2025-07-28 21:05:31]   content-length: 43
[2025-07-28 21:05:31]   sec-ch-ua-platform: "Windows"
[2025-07-28 21:05:31]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-28 21:05:31]   accept: application/json
[2025-07-28 21:05:31]   content-type: application/json
[2025-07-28 21:05:31]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-28 21:05:31]   sec-ch-ua-mobile: ?0
[2025-07-28 21:05:31]   origin: http://localhost:8080
[2025-07-28 21:05:31]   sec-fetch-site: same-origin
[2025-07-28 21:05:31]   sec-fetch-mode: cors
[2025-07-28 21:05:31]   sec-fetch-dest: empty
[2025-07-28 21:05:31]   referer: http://localhost:8080/login
[2025-07-28 21:05:31]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 21:05:31]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-28 21:05:31] Données reçues: {"username":"manager","password":"manager"}
[2025-07-28 21:05:31] Tentative de connexion pour l'utilisateur: manager
[2025-07-28 21:05:31] Connexion à la base de données réussie
[2025-07-28 21:05:31] Recherche de l'utilisateur: trouvé
[2025-07-28 21:05:31] ID utilisateur: 2
[2025-07-28 21:05:31] Rôle: manager
[2025-07-28 21:05:31] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-28 21:05:31] Mot de passe fourni: manager
[2025-07-28 21:05:31] Vérification du mot de passe: réussie
[2025-07-28 21:05:31] Rôle défini dans la base de données: manager
[2025-07-28 21:05:31] Connexion réussie! Token JWT généré:
[2025-07-28 21:05:31] User ID: 2
[2025-07-28 21:05:31] Username: manager
[2025-07-28 21:05:31] Rôle: manager
[2025-07-28 21:05:31] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 21:05:31] Préparation de l'envoi de la réponse JSON:
[2025-07-28 21:05:31] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyOTUzMSwiZXhwIjoxNzUzODE1OTMxLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.5Mi2fUjS_Q_nVVNLtY5mEdNbyeHH6XtBdInmqGUpOGw"}
[2025-07-28 21:31:33] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 21:31:33] Méthode HTTP: POST
[2025-07-28 21:31:33] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:31:33] Origin: http://localhost:8080
[2025-07-28 21:31:33] Referer: http://localhost:8080/login
[2025-07-28 21:31:33] En-têtes de la requête:
[2025-07-28 21:31:33]   host: localhost
[2025-07-28 21:31:33]   connection: close
[2025-07-28 21:31:33]   content-length: 39
[2025-07-28 21:31:33]   sec-ch-ua-platform: "Windows"
[2025-07-28 21:31:33]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:31:33]   accept: application/json
[2025-07-28 21:31:33]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-28 21:31:33]   content-type: application/json
[2025-07-28 21:31:33]   sec-ch-ua-mobile: ?0
[2025-07-28 21:31:33]   origin: http://localhost:8080
[2025-07-28 21:31:33]   sec-fetch-site: same-origin
[2025-07-28 21:31:33]   sec-fetch-mode: cors
[2025-07-28 21:31:33]   sec-fetch-dest: empty
[2025-07-28 21:31:33]   referer: http://localhost:8080/login
[2025-07-28 21:31:33]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 21:31:33]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-28 21:31:33] Données reçues: {"username":"admin","password":"admin"}
[2025-07-28 21:31:33] Tentative de connexion pour l'utilisateur: admin
[2025-07-28 21:31:33] Connexion à la base de données réussie
[2025-07-28 21:31:33] Recherche de l'utilisateur: trouvé
[2025-07-28 21:31:33] ID utilisateur: 1
[2025-07-28 21:31:33] Rôle: admin
[2025-07-28 21:31:33] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-28 21:31:33] Mot de passe fourni: admin
[2025-07-28 21:31:33] Vérification du mot de passe: réussie
[2025-07-28 21:31:33] Rôle défini dans la base de données: admin
[2025-07-28 21:31:33] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-28 21:31:33] Connexion réussie! Token JWT généré:
[2025-07-28 21:31:33] User ID: 1
[2025-07-28 21:31:33] Username: admin
[2025-07-28 21:31:33] Rôle: admin
[2025-07-28 21:31:33] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 21:31:33] Préparation de l'envoi de la réponse JSON:
[2025-07-28 21:31:33] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTA5MywiZXhwIjoxNzUzODE3NDkzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.ZdN2K7FXzxH9ZdXb7fvYmp1bBwgyqWesVDo6APTlLzw"}
[2025-07-28 21:32:11] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 21:32:11] Méthode HTTP: POST
[2025-07-28 21:32:11] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:32:11] Origin: http://localhost:8080
[2025-07-28 21:32:11] Referer: http://localhost:8080/login
[2025-07-28 21:32:11] En-têtes de la requête:
[2025-07-28 21:32:11]   host: localhost:80
[2025-07-28 21:32:11]   connection: close
[2025-07-28 21:32:11]   content-length: 43
[2025-07-28 21:32:11]   sec-ch-ua-platform: "Windows"
[2025-07-28 21:32:11]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:32:11]   accept: application/json
[2025-07-28 21:32:11]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-28 21:32:11]   content-type: application/json
[2025-07-28 21:32:11]   sec-ch-ua-mobile: ?0
[2025-07-28 21:32:11]   origin: http://localhost:8080
[2025-07-28 21:32:11]   sec-fetch-site: same-origin
[2025-07-28 21:32:11]   sec-fetch-mode: cors
[2025-07-28 21:32:11]   sec-fetch-dest: empty
[2025-07-28 21:32:11]   referer: http://localhost:8080/login
[2025-07-28 21:32:11]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 21:32:11]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-28 21:32:11] Données reçues: {"username":"manager","password":"manager"}
[2025-07-28 21:32:11] Tentative de connexion pour l'utilisateur: manager
[2025-07-28 21:32:11] Connexion à la base de données réussie
[2025-07-28 21:32:11] Recherche de l'utilisateur: trouvé
[2025-07-28 21:32:11] ID utilisateur: 2
[2025-07-28 21:32:11] Rôle: manager
[2025-07-28 21:32:11] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-28 21:32:11] Mot de passe fourni: manager
[2025-07-28 21:32:11] Vérification du mot de passe: réussie
[2025-07-28 21:32:11] Rôle défini dans la base de données: manager
[2025-07-28 21:32:11] Connexion réussie! Token JWT généré:
[2025-07-28 21:32:11] User ID: 2
[2025-07-28 21:32:11] Username: manager
[2025-07-28 21:32:11] Rôle: manager
[2025-07-28 21:32:11] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 21:32:11] Préparation de l'envoi de la réponse JSON:
[2025-07-28 21:32:11] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTEzMSwiZXhwIjoxNzUzODE3NTMxLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.2TfUNK3t3QlplUIIMxGjOBcMOAhjwhFQrHQwvCxv7tk"}
[2025-07-28 21:36:52] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-28 21:36:52] Méthode HTTP: POST
[2025-07-28 21:36:52] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:36:52] Origin: http://localhost:8080
[2025-07-28 21:36:52] Referer: http://localhost:8080/login
[2025-07-28 21:36:52] En-têtes de la requête:
[2025-07-28 21:36:52]   host: localhost:80
[2025-07-28 21:36:52]   connection: close
[2025-07-28 21:36:52]   content-length: 43
[2025-07-28 21:36:52]   sec-ch-ua-platform: "Windows"
[2025-07-28 21:36:52]   authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTEzMSwiZXhwIjoxNzUzODE3NTMxLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.2TfUNK3t3QlplUIIMxGjOBcMOAhjwhFQrHQwvCxv7tk
[2025-07-28 21:36:52]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-28 21:36:52]   accept: application/json
[2025-07-28 21:36:52]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-28 21:36:52]   content-type: application/json
[2025-07-28 21:36:52]   sec-ch-ua-mobile: ?0
[2025-07-28 21:36:52]   origin: http://localhost:8080
[2025-07-28 21:36:52]   sec-fetch-site: same-origin
[2025-07-28 21:36:52]   sec-fetch-mode: cors
[2025-07-28 21:36:52]   sec-fetch-dest: empty
[2025-07-28 21:36:52]   referer: http://localhost:8080/login
[2025-07-28 21:36:52]   accept-encoding: gzip, deflate, br, zstd
[2025-07-28 21:36:52]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-28 21:36:52] Données reçues: {"username":"manager","password":"manager"}
[2025-07-28 21:36:52] Tentative de connexion pour l'utilisateur: manager
[2025-07-28 21:36:52] Connexion à la base de données réussie
[2025-07-28 21:36:52] Recherche de l'utilisateur: trouvé
[2025-07-28 21:36:52] ID utilisateur: 2
[2025-07-28 21:36:52] Rôle: manager
[2025-07-28 21:36:52] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-28 21:36:52] Mot de passe fourni: manager
[2025-07-28 21:36:52] Vérification du mot de passe: réussie
[2025-07-28 21:36:52] Rôle défini dans la base de données: manager
[2025-07-28 21:36:52] Connexion réussie! Token JWT généré:
[2025-07-28 21:36:52] User ID: 2
[2025-07-28 21:36:52] Username: manager
[2025-07-28 21:36:52] Rôle: manager
[2025-07-28 21:36:52] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-28 21:36:52] Préparation de l'envoi de la réponse JSON:
[2025-07-28 21:36:52] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTQxMiwiZXhwIjoxNzUzODE3ODEyLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.9zAEJBL95kQhsOroVqEyXvYlnF2V-Rq4Rrzooc6eHIc"}
[2025-07-30 10:57:59] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 10:57:59] Méthode HTTP: POST
[2025-07-30 10:57:59] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-30 10:57:59] Origin: http://localhost:8080
[2025-07-30 10:57:59] Referer: http://localhost:8080/login
[2025-07-30 10:57:59] En-têtes de la requête:
[2025-07-30 10:57:59]   host: localhost
[2025-07-30 10:57:59]   connection: close
[2025-07-30 10:57:59]   content-length: 43
[2025-07-30 10:57:59]   sec-ch-ua-platform: "Windows"
[2025-07-30 10:57:59]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-30 10:57:59]   accept: application/json
[2025-07-30 10:57:59]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-30 10:57:59]   content-type: application/json
[2025-07-30 10:57:59]   sec-ch-ua-mobile: ?0
[2025-07-30 10:57:59]   origin: http://localhost:8080
[2025-07-30 10:57:59]   sec-fetch-site: same-origin
[2025-07-30 10:57:59]   sec-fetch-mode: cors
[2025-07-30 10:57:59]   sec-fetch-dest: empty
[2025-07-30 10:57:59]   referer: http://localhost:8080/login
[2025-07-30 10:57:59]   accept-encoding: gzip, deflate, br, zstd
[2025-07-30 10:57:59]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-30 10:57:59] Données reçues: {"username":"manager","password":"manager"}
[2025-07-30 10:57:59] Tentative de connexion pour l'utilisateur: manager
[2025-07-30 10:57:59] Connexion à la base de données réussie
[2025-07-30 10:57:59] Recherche de l'utilisateur: trouvé
[2025-07-30 10:57:59] ID utilisateur: 2
[2025-07-30 10:57:59] Rôle: manager
[2025-07-30 10:57:59] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-30 10:57:59] Mot de passe fourni: manager
[2025-07-30 10:57:59] Vérification du mot de passe: réussie
[2025-07-30 10:57:59] Rôle défini dans la base de données: manager
[2025-07-30 10:57:59] Connexion réussie! Token JWT généré:
[2025-07-30 10:57:59] User ID: 2
[2025-07-30 10:57:59] Username: manager
[2025-07-30 10:57:59] Rôle: manager
[2025-07-30 10:57:59] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 10:57:59] Préparation de l'envoi de la réponse JSON:
[2025-07-30 10:57:59] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg2NTg3OSwiZXhwIjoxNzUzOTUyMjc5LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.hGQ-AIW772CchHiYow-f3DeCD_3ZEE854UEF1HkPh1A"}
[2025-07-30 14:02:29] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 14:02:29] Méthode HTTP: POST
[2025-07-30 14:02:29] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-30 14:02:29] Origin: http://localhost:8080
[2025-07-30 14:02:29] Referer: http://localhost:8080/login
[2025-07-30 14:02:29] En-têtes de la requête:
[2025-07-30 14:02:29]   host: localhost
[2025-07-30 14:02:29]   connection: close
[2025-07-30 14:02:29]   content-length: 39
[2025-07-30 14:02:29]   sec-ch-ua-platform: "Windows"
[2025-07-30 14:02:29]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-30 14:02:29]   accept: application/json
[2025-07-30 14:02:29]   x-kl-ajax-request: Ajax_Request
[2025-07-30 14:02:29]   content-type: application/json
[2025-07-30 14:02:29]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-30 14:02:29]   sec-ch-ua-mobile: ?0
[2025-07-30 14:02:29]   origin: http://localhost:8080
[2025-07-30 14:02:29]   sec-fetch-site: same-origin
[2025-07-30 14:02:29]   sec-fetch-mode: cors
[2025-07-30 14:02:29]   sec-fetch-dest: empty
[2025-07-30 14:02:29]   referer: http://localhost:8080/login
[2025-07-30 14:02:29]   accept-encoding: gzip, deflate, br, zstd
[2025-07-30 14:02:29]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-30 14:02:29]   cookie: rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vWSfsrwr4%2FRKIWh2d7G3khF2weJaydWU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX1%2FIDSf2LTOiTspDuJ77sAo%2BTqMzqARt0vk%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX19nbGsmsRMcHKfLljGdrDn80e%2BpuFwiJCVRDipNPoV2d5g3C%2FIjDw9EuC3HULj%2Bqx7qRDHSTsK2OA%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2Bho9jkNdJxZHUCrAGaW05IdqD%2FLEFwiTGa2c6BhImN9WV7u3hxtVjWA965DNIfcGkwYdZq3HXZohhsg8I8gqKP9UwaqhX4K4tJTjLc%2FT6zdPsugTMxv4Dc2MjZ9O7tf%2FoxEqr%2Bw4Hm5RJnViLHI0DHu7bC6kH6J98%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX19GJSovqZqGDuA0zDWkTCQiEeQu53mjA6P4FSrO2Dest1YRl8%2BZ%2BowjT7l0rzQLo%2Bt9VVcrSEfkyPp5ZIp2RKFd0ULHvmf7SmtEiegQNX36wjugC7WyL3Y0sMEWJpOcJysNmz9aw4GDaw%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX19nYicRNEGbKoHAqzQHnq27ygeOeCFdmYUCBL%2Bf9k1ldgW8yDUDxnkCShyl9cGvhzvb1EEUHNLN%2B7Z6m2XYjwUXEgE1L6qaZIMkRJKq4sCJJ92b9Onpe%2F1Ja9W7mshlno%2BbqY0iKXGFkA%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22fd722995c7a39bfcb46792be839d8b4ff432214232d76d2ba3ef7e3c603132e5%2387268289-9ba5-474f-a25b-2545d071788e%22%2C%22%24sesid%22%3A%5B1753290268892%2C%2201983819-1fc4-7c3d-8a15-2a0cdfc0fafa%22%2C1753287827394%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fsetup%22%7D%7D; sidebar:state=true
[2025-07-30 14:02:29] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 14:02:29] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 14:02:29] Connexion à la base de données réussie
[2025-07-30 14:02:29] Recherche de l'utilisateur: trouvé
[2025-07-30 14:02:29] ID utilisateur: 1
[2025-07-30 14:02:29] Rôle: admin
[2025-07-30 14:02:29] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 14:02:29] Mot de passe fourni: admin
[2025-07-30 14:02:29] Vérification du mot de passe: réussie
[2025-07-30 14:02:29] Rôle défini dans la base de données: admin
[2025-07-30 14:02:29] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 14:02:29] Connexion réussie! Token JWT généré:
[2025-07-30 14:02:29] User ID: 1
[2025-07-30 14:02:29] Username: admin
[2025-07-30 14:02:29] Rôle: admin
[2025-07-30 14:02:29] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 14:02:29] Préparation de l'envoi de la réponse JSON:
[2025-07-30 14:02:29] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg3Njk0OSwiZXhwIjoxNzUzOTYzMzQ5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.b13WgFy3ZCE2zgS6jSoAcdzvo3JnAeyQJHhWSolBXP8"}
[2025-07-30 15:10:00] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 15:10:00] Méthode HTTP: POST
[2025-07-30 15:10:00] User-Agent: Non défini
[2025-07-30 15:10:00] Origin: Non défini
[2025-07-30 15:10:00] Referer: Non défini
[2025-07-30 15:10:00] En-têtes de la requête:
[2025-07-30 15:10:00]   Host: localhost
[2025-07-30 15:10:00]   Accept: */*
[2025-07-30 15:10:00]   Content-Type: application/json
[2025-07-30 15:10:00]   Content-Length: 39
[2025-07-30 15:10:00] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 15:10:00] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 15:10:00] Connexion à la base de données réussie
[2025-07-30 15:10:00] Recherche de l'utilisateur: trouvé
[2025-07-30 15:10:00] ID utilisateur: 1
[2025-07-30 15:10:00] Rôle: admin
[2025-07-30 15:10:00] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 15:10:00] Mot de passe fourni: admin
[2025-07-30 15:10:00] Vérification du mot de passe: réussie
[2025-07-30 15:10:00] Rôle défini dans la base de données: admin
[2025-07-30 15:10:00] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 15:10:00] Connexion réussie! Token JWT généré:
[2025-07-30 15:10:00] User ID: 1
[2025-07-30 15:10:00] Username: admin
[2025-07-30 15:10:00] Rôle: admin
[2025-07-30 15:10:00] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 15:10:00] Préparation de l'envoi de la réponse JSON:
[2025-07-30 15:10:00] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MTAwMCwiZXhwIjoxNzUzOTY3NDAwLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.fDItBRvq8IT9BH8Ga3YwQTr1oYBBbu_WddHD-TdBaec"}
[2025-07-30 15:25:57] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 15:25:57] Méthode HTTP: POST
[2025-07-30 15:25:57] User-Agent: Non défini
[2025-07-30 15:25:57] Origin: Non défini
[2025-07-30 15:25:57] Referer: Non défini
[2025-07-30 15:25:57] En-têtes de la requête:
[2025-07-30 15:25:57]   host: localhost
[2025-07-30 15:25:57]   content-type: application/json
[2025-07-30 15:25:57]   accept: application/json
[2025-07-30 15:25:57]   content-length: 42
[2025-07-30 15:25:57]   connection: close
[2025-07-30 15:25:57] Données reçues: {"username":"admin","password":"admin123"}
[2025-07-30 15:25:57] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 15:25:57] Connexion à la base de données réussie
[2025-07-30 15:25:57] Recherche de l'utilisateur: trouvé
[2025-07-30 15:25:57] ID utilisateur: 1
[2025-07-30 15:25:57] Rôle: admin
[2025-07-30 15:25:57] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 15:25:58] Mot de passe fourni: admin123
[2025-07-30 15:25:58] Vérification du mot de passe: échouée
[2025-07-30 15:25:58] Échec: Mot de passe incorrect
[2025-07-30 15:25:58] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 15:25:58] Méthode HTTP: POST
[2025-07-30 15:25:58] User-Agent: Non défini
[2025-07-30 15:25:58] Origin: Non défini
[2025-07-30 15:25:58] Referer: Non défini
[2025-07-30 15:25:58] En-têtes de la requête:
[2025-07-30 15:25:58]   host: localhost
[2025-07-30 15:25:58]   accept: */*
[2025-07-30 15:25:58]   content-type: application/json
[2025-07-30 15:25:58]   content-length: 39
[2025-07-30 15:25:58]   connection: close
[2025-07-30 15:25:58] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 15:25:58] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 15:25:58] Connexion à la base de données réussie
[2025-07-30 15:25:58] Recherche de l'utilisateur: trouvé
[2025-07-30 15:25:58] ID utilisateur: 1
[2025-07-30 15:25:58] Rôle: admin
[2025-07-30 15:25:58] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 15:25:58] Mot de passe fourni: admin
[2025-07-30 15:25:58] Vérification du mot de passe: réussie
[2025-07-30 15:25:58] Rôle défini dans la base de données: admin
[2025-07-30 15:25:58] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 15:25:58] Connexion réussie! Token JWT généré:
[2025-07-30 15:25:58] User ID: 1
[2025-07-30 15:25:58] Username: admin
[2025-07-30 15:25:58] Rôle: admin
[2025-07-30 15:25:58] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 15:25:58] Préparation de l'envoi de la réponse JSON:
[2025-07-30 15:25:58] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MTk1OCwiZXhwIjoxNzUzOTY4MzU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.b-9egYEiZ0zJStvOZJ5NgC98ATopOsYDNBejE_SK46A"}
[2025-07-30 15:26:37] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 15:26:37] Méthode HTTP: POST
[2025-07-30 15:26:37] User-Agent: Non défini
[2025-07-30 15:26:37] Origin: Non défini
[2025-07-30 15:26:37] Referer: Non défini
[2025-07-30 15:26:37] En-têtes de la requête:
[2025-07-30 15:26:37]   host: localhost
[2025-07-30 15:26:37]   accept: */*
[2025-07-30 15:26:37]   content-type: application/json
[2025-07-30 15:26:37]   content-length: 39
[2025-07-30 15:26:37]   connection: close
[2025-07-30 15:26:37] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 15:26:37] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 15:26:37] Connexion à la base de données réussie
[2025-07-30 15:26:37] Recherche de l'utilisateur: trouvé
[2025-07-30 15:26:37] ID utilisateur: 1
[2025-07-30 15:26:37] Rôle: admin
[2025-07-30 15:26:37] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 15:26:37] Mot de passe fourni: admin
[2025-07-30 15:26:37] Vérification du mot de passe: réussie
[2025-07-30 15:26:37] Rôle défini dans la base de données: admin
[2025-07-30 15:26:37] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 15:26:37] Connexion réussie! Token JWT généré:
[2025-07-30 15:26:37] User ID: 1
[2025-07-30 15:26:37] Username: admin
[2025-07-30 15:26:37] Rôle: admin
[2025-07-30 15:26:37] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 15:26:37] Préparation de l'envoi de la réponse JSON:
[2025-07-30 15:26:37] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MTk5NywiZXhwIjoxNzUzOTY4Mzk3LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.K3uQteVYarxQbyrnXxeQQV0YMh2aoXaATrb71C29BlE"}
[2025-07-30 15:28:31] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 15:28:31] Méthode HTTP: POST
[2025-07-30 15:28:31] User-Agent: Non défini
[2025-07-30 15:28:31] Origin: Non défini
[2025-07-30 15:28:31] Referer: Non défini
[2025-07-30 15:28:31] En-têtes de la requête:
[2025-07-30 15:28:31]   host: localhost
[2025-07-30 15:28:31]   accept: */*
[2025-07-30 15:28:31]   content-type: application/json
[2025-07-30 15:28:31]   content-length: 39
[2025-07-30 15:28:31]   connection: close
[2025-07-30 15:28:31] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 15:28:31] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 15:28:31] Connexion à la base de données réussie
[2025-07-30 15:28:31] Recherche de l'utilisateur: trouvé
[2025-07-30 15:28:31] ID utilisateur: 1
[2025-07-30 15:28:31] Rôle: admin
[2025-07-30 15:28:31] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 15:28:31] Mot de passe fourni: admin
[2025-07-30 15:28:31] Vérification du mot de passe: réussie
[2025-07-30 15:28:31] Rôle défini dans la base de données: admin
[2025-07-30 15:28:31] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 15:28:31] Connexion réussie! Token JWT généré:
[2025-07-30 15:28:31] User ID: 1
[2025-07-30 15:28:31] Username: admin
[2025-07-30 15:28:31] Rôle: admin
[2025-07-30 15:28:31] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 15:28:31] Préparation de l'envoi de la réponse JSON:
[2025-07-30 15:28:31] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MjExMSwiZXhwIjoxNzUzOTY4NTExLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.9WjkuRKwq5FwhFDp7ce4TA703XvpqHanH7DYS6NnK1s"}
[2025-07-30 15:30:23] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 15:30:23] Méthode HTTP: POST
[2025-07-30 15:30:23] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-30 15:30:23] Origin: http://localhost:8080
[2025-07-30 15:30:23] Referer: http://localhost:8080/test_admin_interface.html
[2025-07-30 15:30:23] En-têtes de la requête:
[2025-07-30 15:30:23]   host: localhost
[2025-07-30 15:30:23]   connection: close
[2025-07-30 15:30:23]   content-length: 39
[2025-07-30 15:30:23]   sec-ch-ua-platform: "Windows"
[2025-07-30 15:30:23]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-30 15:30:23]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-30 15:30:23]   content-type: application/json
[2025-07-30 15:30:23]   sec-ch-ua-mobile: ?0
[2025-07-30 15:30:23]   accept: */*
[2025-07-30 15:30:23]   origin: http://localhost:8080
[2025-07-30 15:30:23]   sec-fetch-site: same-origin
[2025-07-30 15:30:23]   sec-fetch-mode: cors
[2025-07-30 15:30:23]   sec-fetch-dest: empty
[2025-07-30 15:30:23]   referer: http://localhost:8080/test_admin_interface.html
[2025-07-30 15:30:23]   accept-encoding: gzip, deflate, br, zstd
[2025-07-30 15:30:23]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-30 15:30:23] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 15:30:23] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 15:30:23] Connexion à la base de données réussie
[2025-07-30 15:30:23] Recherche de l'utilisateur: trouvé
[2025-07-30 15:30:23] ID utilisateur: 1
[2025-07-30 15:30:23] Rôle: admin
[2025-07-30 15:30:23] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 15:30:23] Mot de passe fourni: admin
[2025-07-30 15:30:23] Vérification du mot de passe: réussie
[2025-07-30 15:30:23] Rôle défini dans la base de données: admin
[2025-07-30 15:30:23] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 15:30:23] Connexion réussie! Token JWT généré:
[2025-07-30 15:30:23] User ID: 1
[2025-07-30 15:30:23] Username: admin
[2025-07-30 15:30:23] Rôle: admin
[2025-07-30 15:30:23] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 15:30:23] Préparation de l'envoi de la réponse JSON:
[2025-07-30 15:30:23] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MjIyMywiZXhwIjoxNzUzOTY4NjIzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.yXmnciHyKwX8hFNUWWow0KQO8t79Hw997gIZykvnMQ8"}
[2025-07-30 22:26:37] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 22:26:37] Méthode HTTP: POST
[2025-07-30 22:26:37] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-30 22:26:37] Origin: http://localhost:8080
[2025-07-30 22:26:37] Referer: http://localhost:8080/login
[2025-07-30 22:26:37] En-têtes de la requête:
[2025-07-30 22:26:37]   host: localhost
[2025-07-30 22:26:37]   connection: close
[2025-07-30 22:26:37]   content-length: 43
[2025-07-30 22:26:37]   sec-ch-ua-platform: "Windows"
[2025-07-30 22:26:37]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-30 22:26:37]   accept: application/json
[2025-07-30 22:26:37]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-07-30 22:26:37]   content-type: application/json
[2025-07-30 22:26:37]   sec-ch-ua-mobile: ?0
[2025-07-30 22:26:37]   origin: http://localhost:8080
[2025-07-30 22:26:37]   sec-fetch-site: same-origin
[2025-07-30 22:26:37]   sec-fetch-mode: cors
[2025-07-30 22:26:37]   sec-fetch-dest: empty
[2025-07-30 22:26:37]   referer: http://localhost:8080/login
[2025-07-30 22:26:37]   accept-encoding: gzip, deflate, br, zstd
[2025-07-30 22:26:37]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-07-30 22:26:37] Données reçues: {"username":"manager","password":"manager"}
[2025-07-30 22:26:37] Tentative de connexion pour l'utilisateur: manager
[2025-07-30 22:26:37] Connexion à la base de données réussie
[2025-07-30 22:26:37] Recherche de l'utilisateur: trouvé
[2025-07-30 22:26:37] ID utilisateur: 2
[2025-07-30 22:26:37] Rôle: manager
[2025-07-30 22:26:37] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[2025-07-30 22:26:37] Mot de passe fourni: manager
[2025-07-30 22:26:37] Vérification du mot de passe: réussie
[2025-07-30 22:26:37] Rôle défini dans la base de données: manager
[2025-07-30 22:26:37] Connexion réussie! Token JWT généré:
[2025-07-30 22:26:37] User ID: 2
[2025-07-30 22:26:37] Username: manager
[2025-07-30 22:26:37] Rôle: manager
[2025-07-30 22:26:37] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 22:26:37] Préparation de l'envoi de la réponse JSON:
[2025-07-30 22:26:37] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzkwNzE5NywiZXhwIjoxNzUzOTkzNTk3LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.nu81_iseD4but7cVoG-i-wQTjt9Q7lupfECS5vDDf0c"}
[2025-07-30 22:38:21] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-07-30 22:38:21] Méthode HTTP: POST
[2025-07-30 22:38:21] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-30 22:38:21] Origin: http://localhost:8080
[2025-07-30 22:38:21] Referer: http://localhost:8080/login
[2025-07-30 22:38:21] En-têtes de la requête:
[2025-07-30 22:38:21]   host: localhost
[2025-07-30 22:38:21]   connection: close
[2025-07-30 22:38:21]   content-length: 39
[2025-07-30 22:38:21]   sec-ch-ua-platform: "Windows"
[2025-07-30 22:38:21]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-30 22:38:21]   accept: application/json
[2025-07-30 22:38:21]   x-kl-ajax-request: Ajax_Request
[2025-07-30 22:38:21]   content-type: application/json
[2025-07-30 22:38:21]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
[2025-07-30 22:38:21]   sec-ch-ua-mobile: ?0
[2025-07-30 22:38:21]   origin: http://localhost:8080
[2025-07-30 22:38:21]   sec-fetch-site: same-origin
[2025-07-30 22:38:21]   sec-fetch-mode: cors
[2025-07-30 22:38:21]   sec-fetch-dest: empty
[2025-07-30 22:38:21]   referer: http://localhost:8080/login
[2025-07-30 22:38:21]   accept-encoding: gzip, deflate, br, zstd
[2025-07-30 22:38:21]   accept-language: fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7
[2025-07-30 22:38:21]   cookie: rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vWSfsrwr4%2FRKIWh2d7G3khF2weJaydWU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX1%2FIDSf2LTOiTspDuJ77sAo%2BTqMzqARt0vk%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX19nbGsmsRMcHKfLljGdrDn80e%2BpuFwiJCVRDipNPoV2d5g3C%2FIjDw9EuC3HULj%2Bqx7qRDHSTsK2OA%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2Bho9jkNdJxZHUCrAGaW05IdqD%2FLEFwiTGa2c6BhImN9WV7u3hxtVjWA965DNIfcGkwYdZq3HXZohhsg8I8gqKP9UwaqhX4K4tJTjLc%2FT6zdPsugTMxv4Dc2MjZ9O7tf%2FoxEqr%2Bw4Hm5RJnViLHI0DHu7bC6kH6J98%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX19GJSovqZqGDuA0zDWkTCQiEeQu53mjA6P4FSrO2Dest1YRl8%2BZ%2BowjT7l0rzQLo%2Bt9VVcrSEfkyPp5ZIp2RKFd0ULHvmf7SmtEiegQNX36wjugC7WyL3Y0sMEWJpOcJysNmz9aw4GDaw%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX19nYicRNEGbKoHAqzQHnq27ygeOeCFdmYUCBL%2Bf9k1ldgW8yDUDxnkCShyl9cGvhzvb1EEUHNLN%2B7Z6m2XYjwUXEgE1L6qaZIMkRJKq4sCJJ92b9Onpe%2F1Ja9W7mshlno%2BbqY0iKXGFkA%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22fd722995c7a39bfcb46792be839d8b4ff432214232d76d2ba3ef7e3c603132e5%2387268289-9ba5-474f-a25b-2545d071788e%22%2C%22%24sesid%22%3A%5B1753290268892%2C%2201983819-1fc4-7c3d-8a15-2a0cdfc0fafa%22%2C1753287827394%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fsetup%22%7D%7D; sidebar:state=true
[2025-07-30 22:38:21] Données reçues: {"username":"admin","password":"admin"}
[2025-07-30 22:38:21] Tentative de connexion pour l'utilisateur: admin
[2025-07-30 22:38:21] Connexion à la base de données réussie
[2025-07-30 22:38:21] Recherche de l'utilisateur: trouvé
[2025-07-30 22:38:21] ID utilisateur: 1
[2025-07-30 22:38:21] Rôle: admin
[2025-07-30 22:38:21] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-07-30 22:38:21] Mot de passe fourni: admin
[2025-07-30 22:38:21] Vérification du mot de passe: réussie
[2025-07-30 22:38:21] Rôle défini dans la base de données: admin
[2025-07-30 22:38:21] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-07-30 22:38:21] Connexion réussie! Token JWT généré:
[2025-07-30 22:38:21] User ID: 1
[2025-07-30 22:38:21] Username: admin
[2025-07-30 22:38:21] Rôle: admin
[2025-07-30 22:38:21] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-07-30 22:38:21] Préparation de l'envoi de la réponse JSON:
[2025-07-30 22:38:21] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzkwNzkwMSwiZXhwIjoxNzUzOTk0MzAxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.tPzzsyb1LNakuMhf5VJhhaT0UG_Vg4NlXMWgkolC2_o"}
[2025-08-02 12:23:08] === NOUVELLE TENTATIVE DE CONNEXION ===
[2025-08-02 12:23:08] Méthode HTTP: POST
[2025-08-02 12:23:08] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-02 12:23:08] Origin: http://localhost:8080
[2025-08-02 12:23:08] Referer: http://localhost:8080/login
[2025-08-02 12:23:08] En-têtes de la requête:
[2025-08-02 12:23:08]   host: localhost
[2025-08-02 12:23:08]   connection: close
[2025-08-02 12:23:08]   content-length: 39
[2025-08-02 12:23:08]   sec-ch-ua-platform: "Windows"
[2025-08-02 12:23:08]   user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-02 12:23:08]   accept: application/json
[2025-08-02 12:23:08]   sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
[2025-08-02 12:23:08]   content-type: application/json
[2025-08-02 12:23:08]   sec-ch-ua-mobile: ?0
[2025-08-02 12:23:08]   origin: http://localhost:8080
[2025-08-02 12:23:08]   sec-fetch-site: same-origin
[2025-08-02 12:23:08]   sec-fetch-mode: cors
[2025-08-02 12:23:08]   sec-fetch-dest: empty
[2025-08-02 12:23:08]   referer: http://localhost:8080/login
[2025-08-02 12:23:08]   accept-encoding: gzip, deflate, br, zstd
[2025-08-02 12:23:08]   accept-language: fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
[2025-08-02 12:23:08] Données reçues: {"username":"admin","password":"admin"}
[2025-08-02 12:23:08] Tentative de connexion pour l'utilisateur: admin
[2025-08-02 12:23:08] Connexion à la base de données réussie
[2025-08-02 12:23:09] Recherche de l'utilisateur: trouvé
[2025-08-02 12:23:09] ID utilisateur: 1
[2025-08-02 12:23:09] Rôle: admin
[2025-08-02 12:23:09] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[2025-08-02 12:23:09] Mot de passe fourni: admin
[2025-08-02 12:23:09] Vérification du mot de passe: réussie
[2025-08-02 12:23:09] Rôle défini dans la base de données: admin
[2025-08-02 12:23:09] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[2025-08-02 12:23:09] Connexion réussie! Token JWT généré:
[2025-08-02 12:23:09] User ID: 1
[2025-08-02 12:23:09] Username: admin
[2025-08-02 12:23:09] Rôle: admin
[2025-08-02 12:23:09] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[2025-08-02 12:23:09] Préparation de l'envoi de la réponse JSON:
[2025-08-02 12:23:09] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1NDEzMDE4OSwiZXhwIjoxNzU0MjE2NTg5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.tYVuG-tZrZw1OMfzGp31w8zmqJTeonxTq3ykI4Lf7J0"}
