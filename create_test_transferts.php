<?php
/**
 * Script pour créer des transferts de test
 */

require_once 'api/config.php';

echo "🔧 CRÉATION DE TRANSFERTS DE TEST\n";
echo "=================================\n\n";

try {
    // Vérifier si des transferts existent déjà
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM transferts");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count > 0) {
        echo "⚠️  $count transferts existent déjà. Voulez-vous continuer ? (y/N): ";
        // Pour l'automatisation, on continue
        echo "Oui (automatique)\n";
    }
    
    // Créer des transferts de test
    $testTransferts = [
        [
            'reference' => 'TR' . date('YmdHis') . '001',
            'montant' => 50000.00,
            'type' => 'national',
            'destination' => 'Abidjan',
            'service' => 'Orange Money',
            'date_transfert' => date('Y-m-d'),
            'motif' => 'Transfert familial',
            'statut' => 'en_attente'
        ],
        [
            'reference' => 'TR' . date('YmdHis') . '002',
            'montant' => 25000.00,
            'type' => 'national',
            'destination' => 'Bouaké',
            'service' => 'MTN Mobile Money',
            'date_transfert' => date('Y-m-d'),
            'motif' => 'Paiement facture',
            'statut' => 'valide'
        ],
        [
            'reference' => 'TR' . date('YmdHis') . '003',
            'montant' => 100000.00,
            'type' => 'international',
            'destination' => 'Paris, France',
            'service' => 'Western Union',
            'date_transfert' => date('Y-m-d'),
            'motif' => 'Envoi étudiant',
            'statut' => 'en_attente'
        ],
        [
            'reference' => 'TR' . date('YmdHis') . '004',
            'montant' => 75000.00,
            'type' => 'national',
            'destination' => 'Yamoussoukro',
            'service' => 'Wave',
            'date_transfert' => date('Y-m-d', strtotime('-1 day')),
            'motif' => 'Achat matériel',
            'statut' => 'termine'
        ],
        [
            'reference' => 'TR' . date('YmdHis') . '005',
            'montant' => 200000.00,
            'type' => 'international',
            'destination' => 'Dakar, Sénégal',
            'service' => 'Wari',
            'date_transfert' => date('Y-m-d', strtotime('-2 days')),
            'motif' => 'Investissement',
            'statut' => 'valide'
        ]
    ];
    
    echo "Insertion des transferts de test...\n";
    
    $insertQuery = "INSERT INTO transferts (reference, montant, type, destination, service, date_transfert, motif, statut, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $stmt = $pdo->prepare($insertQuery);
    
    foreach ($testTransferts as $index => $transfert) {
        try {
            $stmt->execute([
                $transfert['reference'],
                $transfert['montant'],
                $transfert['type'],
                $transfert['destination'],
                $transfert['service'],
                $transfert['date_transfert'],
                $transfert['motif'],
                $transfert['statut']
            ]);
            echo "✅ Transfert " . ($index + 1) . " créé: {$transfert['reference']}\n";
        } catch (Exception $e) {
            echo "❌ Erreur transfert " . ($index + 1) . ": " . $e->getMessage() . "\n";
        }
    }
    
    // Vérification finale
    echo "\n📊 VÉRIFICATION DES DONNÉES:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM transferts");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "Total des transferts: $total\n";
    
    $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM transferts GROUP BY type");
    $typeCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Répartition par type:\n";
    foreach ($typeCounts as $typeCount) {
        echo "- {$typeCount['type']}: {$typeCount['count']}\n";
    }
    
    $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM transferts GROUP BY statut");
    $statutCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Répartition par statut:\n";
    foreach ($statutCounts as $statutCount) {
        echo "- {$statutCount['statut']}: {$statutCount['count']}\n";
    }
    
    // Afficher quelques exemples
    echo "\n📋 EXEMPLES DE TRANSFERTS:\n";
    $stmt = $pdo->query("SELECT reference, montant, type, destination, service, statut FROM transferts ORDER BY created_at DESC LIMIT 3");
    $exemples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($exemples as $exemple) {
        echo "- {$exemple['reference']}: {$exemple['montant']} FCFA ({$exemple['type']}) vers {$exemple['destination']} via {$exemple['service']} - {$exemple['statut']}\n";
    }
    
    echo "\n✅ TRANSFERTS DE TEST CRÉÉS AVEC SUCCÈS!\n";
    echo "\n🎯 PROCHAINES ÉTAPES:\n";
    echo "1. Redémarrer le serveur de développement\n";
    echo "2. Tester l'interface manager: http://localhost:8080/manager/dashboard/transferts\n";
    echo "3. Vérifier qu'il n'y a plus d'erreur JavaScript\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Script terminé\n";
?>
