<?php
require_once 'api/config.php';

echo "STRUCTURE DE LA TABLE services_transfert\n";
echo "========================================\n\n";

try {
    $stmt = $pdo->query('DESCRIBE services_transfert');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Colonnes existantes:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']}\n";
    }
    
    echo "\nDonnées existantes:\n";
    $stmt = $pdo->query('SELECT * FROM services_transfert LIMIT 5');
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    print_r($data);
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
?>
