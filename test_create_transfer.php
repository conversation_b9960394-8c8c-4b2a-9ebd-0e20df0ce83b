<?php
/**
 * Test de création de transfert externe
 */

echo "TEST CRÉATION TRANSFERT EXTERNE\n";
echo "===============================\n\n";

// Test de création d'un transfert externe complet
echo "1. Test de création d'un transfert externe...\n";
$url = "http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php";
$data = [
    'type' => 'externe',
    'montant' => 30000,
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF',
    'service' => '1', // ID du service MIXX
    'motif' => 'Envoi familial',
    'expediteur' => [
        'nom' => 'MOUSSA',
        'prenom' => 'OUMANDE',
        'telephone' => '22890387401'
    ],
    'destinataire' => [
        'nom' => 'MEMEM',
        'prenom' => 'Issifou',
        'telephone' => '71454212'
    ]
];

echo "URL: $url\n";
echo "Données: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data),
        'timeout' => 15
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response !== false) {
    $result = json_decode($response, true);
    
    if ($result && $result['success']) {
        echo "✅ Transfert externe créé avec succès !\n";
        echo "   ID: " . $result['data']['id'] . "\n";
        echo "   Référence: " . $result['data']['reference'] . "\n";
        echo "   Montant: " . $result['data']['montant'] . " XOF\n";
        echo "   Frais: " . $result['data']['frais'] . " XOF\n";
        echo "   Taux de change: " . $result['data']['taux_change'] . "\n";
        echo "   Montant à recevoir: " . $result['data']['montant_a_recevoir'] . " XOF\n";
    } else {
        echo "❌ Erreur création: " . ($result['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse complète: " . substr($response, 0, 1000) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API\n";
    $error = error_get_last();
    echo "   Erreur: " . ($error['message'] ?? 'Erreur inconnue') . "\n";
}

echo "\n";

// Test 2: Test avec données manquantes
echo "2. Test avec données manquantes (expéditeur manquant)...\n";
$data2 = [
    'type' => 'externe',
    'montant' => 15000,
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF',
    'service' => '1',
    'motif' => 'Test validation',
    // expediteur manquant
    'destinataire' => [
        'nom' => 'TEST',
        'prenom' => 'User',
        'telephone' => '12345678'
    ]
];

echo "Données (expéditeur manquant): " . json_encode($data2, JSON_PRETTY_PRINT) . "\n";

$context2 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data2),
        'timeout' => 10
    ]
]);

$response2 = file_get_contents($url, false, $context2);

if ($response2 !== false) {
    $result2 = json_decode($response2, true);
    
    if (!$result2['success']) {
        echo "✅ Validation OK - Erreur attendue: " . $result2['message'] . "\n";
    } else {
        echo "❌ Validation échouée - Devrait retourner une erreur\n";
    }
} else {
    echo "❌ Impossible de contacter l'API pour le test de validation\n";
}

echo "\n";

// Test 3: Test avec service international
echo "3. Test avec service international (Western Union)...\n";
$data3 = [
    'type' => 'externe',
    'montant' => 50000,
    'devise_source' => 'XOF',
    'devise_destination' => 'EUR',
    'service' => '5', // ID du service Western Union
    'motif' => 'Transfert international',
    'expediteur' => [
        'nom' => 'DIALLO',
        'prenom' => 'Amadou',
        'telephone' => '22891234567'
    ],
    'destinataire' => [
        'nom' => 'MARTIN',
        'prenom' => 'Jean',
        'telephone' => '+33123456789'
    ]
];

echo "Données: " . json_encode($data3, JSON_PRETTY_PRINT) . "\n";

$context3 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data3),
        'timeout' => 15
    ]
]);

$response3 = file_get_contents($url, false, $context3);

if ($response3 !== false) {
    $result3 = json_decode($response3, true);
    
    if ($result3 && $result3['success']) {
        echo "✅ Transfert international créé avec succès !\n";
        echo "   ID: " . $result3['data']['id'] . "\n";
        echo "   Référence: " . $result3['data']['reference'] . "\n";
        echo "   Montant: " . $result3['data']['montant'] . " " . $data3['devise_source'] . "\n";
        echo "   Frais: " . $result3['data']['frais'] . " " . $data3['devise_source'] . "\n";
        echo "   Taux de change: " . $result3['data']['taux_change'] . "\n";
        echo "   Montant à recevoir: " . $result3['data']['montant_a_recevoir'] . " " . $data3['devise_destination'] . "\n";
    } else {
        echo "❌ Erreur création internationale: " . ($result3['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response3, 0, 1000) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API pour le test international\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
