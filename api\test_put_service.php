<?php
/**
 * Test spécifique pour la requête PUT de modification de service
 */

echo "=== TEST PUT SERVICE ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Simuler une requête PUT
$service_id = 2; // ID du service à modifier
$test_data = [
    'nom' => 'FLOOZ Updated',
    'type' => 'Premium',
    'description' => 'Service FLOOZ mis à jour',
    'commission_fixe' => 250,
    'commission_pourcentage' => 2.0,
    'commission_envoi_fixe' => 350,
    'commission_envoi_pourcentage' => 2.5,
    'commission_retrait_fixe' => 280,
    'commission_retrait_pourcentage' => 2.0,
    'actif' => 1
];

echo "1. Données de test pour PUT:\n";
echo "   Service ID: $service_id\n";
echo "   Données: " . json_encode($test_data, JSON_PRETTY_PRINT) . "\n\n";

// Test avec cURL pour simuler exactement la requête du frontend
echo "2. Test avec cURL:\n";

$url = "http://localhost:8080/api/admin_transferts.php?id=$service_id";
$json_data = json_encode($test_data);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($json_data),
    'Authorization: Bearer test_token' // Vous devrez peut-être ajuster ceci
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   Code HTTP: $http_code\n";

// Séparer les en-têtes du corps de la réponse
$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $header_size);
$body = substr($response, $header_size);

echo "   Réponse: $body\n\n";

// Test direct avec la base de données pour vérifier l'état actuel
echo "3. Vérification de l'état actuel du service ID $service_id:\n";

require_once 'config.php';

try {
    $stmt = $pdo->prepare("SELECT * FROM services_transfert WHERE id = ?");
    $stmt->execute([$service_id]);
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($service) {
        echo "   ✅ Service trouvé:\n";
        echo "   Nom: {$service['nom']}\n";
        echo "   Type: {$service['type']}\n";
        echo "   Commission envoi fixe: {$service['commission_envoi_fixe']}\n";
        echo "   Commission retrait fixe: {$service['commission_retrait_fixe']}\n";
        echo "   Actif: {$service['actif']}\n";
    } else {
        echo "   ❌ Service ID $service_id non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// Test de validation des champs
echo "4. Test de validation des champs autorisés:\n";

$allowedFields = [
    'nom', 'type', 'description', 'commission_fixe',
    'commission_pourcentage', 'commission_envoi_fixe', 'commission_envoi_pourcentage',
    'commission_retrait_fixe', 'commission_retrait_pourcentage',
    'devise_principale', 'devises_supportees', 'actif'
];

echo "   Champs autorisés: " . implode(', ', $allowedFields) . "\n";

$updateFields = [];
foreach ($allowedFields as $field) {
    if (isset($test_data[$field])) {
        $updateFields[] = $field;
    }
}

echo "   Champs présents dans les données: " . implode(', ', $updateFields) . "\n";
echo "   Nombre de champs à mettre à jour: " . count($updateFields) . "\n";

if (count($updateFields) > 0) {
    echo "   ✅ Au moins un champ à mettre à jour\n";
} else {
    echo "   ❌ Aucun champ à mettre à jour - ceci causerait l'erreur 400\n";
}

echo "\n=== FIN DU TEST ===\n";
?>
