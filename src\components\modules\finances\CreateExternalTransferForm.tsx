import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from '@/components/ui/select';
import { Card, CardHeader, CardTitle, CardDescription, CardFooter, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertCircle,
  Send,
  User,
  Phone,
  Loader2,
  Banknote,
  FileCheck,
  UserPlus,
  UserRound,
  Home,
  Globe,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  X,
  CheckCircle,
  ReceiptText,
  Info,
  CircleDollarSign,
  RefreshCw,
  Calculator
} from 'lucide-react';
import { API_BASE_URL } from '@/config';

// Importer les styles personnalisés
import "../../../styles/fadeIn.css";
import "../../../styles/slideIn.css";

interface CreateExternalTransferFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface Quote {
  montant_envoye: number;
  frais: number;
  taux_change: number;
  devise_source: string;
  devise_destination: string;
  montant_a_recevoir: number;
}

interface TransferService {
  id: number;
  nom: string;
  description: string;
  type: string;
  commission_fixe: string;
  commission_pourcentage: string;
  montant_min: string;
  montant_max: string;
  actif: number;
}

interface Currency {
  code: string;
  name: string;
  symbol: string;
}

export const CreateExternalTransferForm: React.FC<CreateExternalTransferFormProps> = ({ onClose, onSuccess }) => {
  const { toast } = useToast();
  
  // État pour suivre l'étape actuelle du processus
  const [activeStep, setActiveStep] = useState<number>(1);
  
  const [formData, setFormData] = useState({
    expediteur: { nom: '', prenom: '', telephone: '' },
    destinataire: { nom: '', prenom: '', telephone: '' },
    montant: '',
    devise_source: 'XOF',
    devise_destination: 'XOF',
    service: '',
    type: 'externe', // Changé de 'national' à 'externe' pour les transferts externes
    operation_type: 'envoi', // Nouveau champ pour différencier envoi/retrait
    notes: '',
  });

  const [quote, setQuote] = useState<Quote | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isQuoteLoading, setIsQuoteLoading] = useState(false);
  const [services, setServices] = useState<TransferService[]>([]);
  const [isServicesLoading, setIsServicesLoading] = useState(false);
  const [servicesError, setServicesError] = useState<string | null>(null);
  
  // États de validation pour chaque étape
  const [stepErrors, setStepErrors] = useState<{[key: number]: string | null}>({1: null, 2: null, 3: null});

  // Liste des devises disponibles
  const currencies: Currency[] = [
    { code: 'XOF', name: 'Franc CFA (BCEAO)', symbol: 'CFA' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'USD', name: 'Dollar US', symbol: '$' },
    { code: 'GBP', name: 'Livre Sterling', symbol: '£' },
  ];
  
  // Types de transfert
  const transferTypes = ['national', 'international'];

  // Fonction pour trouver le token dans localStorage
  const findToken = () => {
    const keys = ['token', 'authToken', 'jwt', 'access_token'];
    for (const key of keys) {
      const value = localStorage.getItem(key);
      if (value && value.startsWith('eyJ')) {
        return value;
      }
    }
    return null;
  };

  // Fonction pour récupérer le nom du service à partir de son ID
  const getServiceName = (serviceId: string) => {
    const service = services.find(s => s.id.toString() === serviceId);
    return service ? service.nom : serviceId;
  };

  // Charger les services de transfert depuis l'API
  // Récupérer les services de transfert depuis l'API
  useEffect(() => {
    console.log("Tentative de récupération des services de transfert...");
    const fetchServices = async () => {
      setIsServicesLoading(true);
      setServicesError(null);

      try {
        // Récupérer le token d'authentification
        const token = findToken();
        console.log('Token pour services:', token ? 'OUI' : 'NON');

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        };

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(`${API_BASE_URL}/transferts.php?action=services`, {
          method: 'GET',
          headers,
        });

        if (!response.ok) {
          throw new Error(`Erreur HTTP ${response.status}`);
        }

        const result = await response.json();

        if (result.success && result.data) {
          console.log("Services récupérés:", result.data);
          console.log("Services récupérés:", JSON.stringify(result.data, null, 2)); // Ajout de code de débogage
          setServices(result.data);
        } else {
          throw new Error(result.message || 'Erreur lors de la récupération des services');
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des services de transfert:', error);
        setServicesError(error instanceof Error ? error.message : 'Erreur inconnue');
      } finally {
        setIsServicesLoading(false);
      }
    };
    
    fetchServices();
  }, []);
  
  // Déclencher automatiquement le calcul du devis à l'étape 3
  useEffect(() => {
    if (activeStep === 3 && formData.service && formData.montant) {
      console.log("Étape 3 activée, calcul automatique du devis...");
      calculateQuote();
    }
  }, [activeStep, formData.service, formData.montant]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Réinitialiser l'erreur spécifique à cette étape si nécessaire
    if (activeStep === 2) {
      setStepErrors(prev => ({ ...prev, 2: null }));
    }
  };

  const handleClientChange = (type: 'expediteur' | 'destinataire', e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [type]: { ...prev[type], [name]: value },
    }));
    
    // Réinitialiser l'erreur spécifique à cette étape si nécessaire
    if (activeStep === 1) {
      setStepErrors(prev => ({ ...prev, 1: null }));
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    // Réinitialiser l'erreur spécifique à cette étape si nécessaire
    if (activeStep === 2) {
      setStepErrors(prev => ({ ...prev, 2: null }));
    }
  };

  // Validation pour l'étape 1: Expéditeur et destinataire
  const validateStep1 = () => {
    const { expediteur, destinataire } = formData;
    
    if (!expediteur.nom || !expediteur.prenom || !expediteur.telephone) {
      setStepErrors(prev => ({ ...prev, 1: "Les informations de l'expéditeur sont incomplètes" }));
      return false;
    }
    
    if (!destinataire.nom || !destinataire.prenom || !destinataire.telephone) {
      setStepErrors(prev => ({ ...prev, 1: "Les informations du destinataire sont incomplètes" }));
      return false;
    }
    
    setStepErrors(prev => ({ ...prev, 1: null }));
    return true;
  };

  // Validation pour l'étape 2: Détails du transfert
  const validateStep2 = () => {
    let error = null;
    
    if (!formData.service) {
      error = 'Veuillez sélectionner un service de transfert';
    } else if (!formData.montant) {
      error = 'Veuillez saisir un montant';
    } else if (isNaN(Number(formData.montant)) || Number(formData.montant) <= 0) {
      error = 'Veuillez saisir un montant valide';
    } else if (!formData.devise_source) {
      error = 'Veuillez sélectionner une devise source';
    } else if (!formData.devise_destination) {
      error = 'Veuillez sélectionner une devise destination';
    } else if (!formData.type) {
      error = 'Veuillez sélectionner un type de transfert';
    }
    
    setStepErrors({ ...stepErrors, 2: error });
    return !error;
  };

  // Navigation entre les étapes
  const goToNextStep = () => {
    if (activeStep === 1) {
      if (validateStep1()) {
        setActiveStep(2);
      }
    } else if (activeStep === 2) {
      if (validateStep2()) {
        calculateQuote();
        setActiveStep(3);
      }
    }
  };

  const goToPreviousStep = () => {
    if (activeStep > 1) {
      setActiveStep(activeStep - 1);
    }
  };

  // Calcul du devis pour le transfert
  const calculateQuote = async () => {
    const { montant, devise_source, devise_destination, type, service, operation_type } = formData;
    setIsQuoteLoading(true);
    setError(null);
    
    console.log('Tentative de calcul du devis avec les données suivantes:', formData);
    
    // Vérification des données requises avec plus de détails
    if (!service) {
      console.error('Service manquant pour le calcul du devis');
      setError('Données manquantes pour le devis: service');
      setIsQuoteLoading(false);
      return;
    }
    
    if (!montant || isNaN(parseFloat(montant)) || parseFloat(montant) <= 0) {
      console.error('Montant invalide pour le calcul du devis:', montant);
      setError('Montant invalide pour le calcul du devis');
      setIsQuoteLoading(false);
      return;
    }
    
    try {
      // Préparation des données à envoyer
      const quoteData = {
        montant: parseFloat(montant),
        devise_source,
        devise_destination,
        type,
        service,
        operation_type, // Inclure le type d'opération
      };
      
      console.log('Données envoyées pour le calcul du devis:', quoteData);

      // Construire l'URL avec les paramètres
      const params = new URLSearchParams({
        action: 'quote',
        service: service,
        montant: montant,
        devise_source: devise_source,
        devise_destination: devise_destination,
        operation_type: operation_type
      });

      // Récupérer le token d'authentification
      const token = findToken();
      console.log('Token pour devis:', token ? 'OUI' : 'NON');

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/transferts.php?${params.toString()}`, {
        method: 'GET',
        headers,
      });
      
      const data = await response.json();
      console.log('Réponse du serveur pour le devis:', data);
      
      if (!response.ok) {
        throw new Error(data.message || 'Erreur lors du calcul du devis');
      }
      
      if (data.success && data.data) {
        console.log('Données du devis reçues:', data.data);
        // Vérification des valeurs numériques
        if (data.data.montant_destination === undefined || data.data.montant_destination === null) {
          console.error('Le montant à recevoir est manquant dans la réponse');
          data.data.montant_destination = '0'; // Valeur par défaut
        }
        
        // Conversion explicite en nombre puis en chaîne pour garantir le bon format
        data.data.montant_destination = parseFloat(data.data.montant_destination).toString();
        data.data.montant_source = parseFloat(data.data.montant_source).toString();
        data.data.frais = parseFloat(data.data.frais).toString();
        
        console.log('Données du devis après traitement:', data.data);
        setQuote(data.data);
      } else {
        console.error('Format de réponse incorrect pour le devis:', data);
        throw new Error('Format de réponse incorrect pour le devis');
      }
    } catch (err) {
      console.error('Error calculating quote:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors du calcul du devis');
    } finally {
      setIsQuoteLoading(false);
    }
  };

  // Soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (activeStep !== 3) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Récupérer le token d'authentification
      const token = findToken();
      console.log('Token trouvé:', token ? 'OUI' : 'NON', token ? token.substring(0, 20) + '...' : 'Aucun');

      if (!token) {
        throw new Error('Token d\'authentification non trouvé. Veuillez vous reconnecter.');
      }

      const response = await fetch(`${API_BASE_URL}/transferts.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...formData,
          montant: parseFloat(formData.montant),
          frais: quote?.frais || 0,
          taux_change: quote?.taux_change || 1,
          montant_a_recevoir: quote?.montant_a_recevoir || 0,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Erreur lors de la création du transfert');
      }

      toast({
        title: "Transfert externe créé",
        description: `Le transfert a été créé avec succès avec la référence: ${data.data.reference}`,
        duration: 5000,
      });

      onSuccess();
      onClose();
    } catch (err) {
      console.error('Error submitting transfer:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de la création du transfert');
    } finally {
      setIsLoading(false);
    }
  };

  // Rendu des indicateurs d'étapes
  const renderStepIndicators = () => {
    return (
      <div className="flex items-center justify-center mb-6">
        <div className={`flex items-center justify-center w-12 h-12 rounded-full ${activeStep >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
          <UserPlus className="w-6 h-6" />
        </div>
        <div className={`w-16 h-1 ${activeStep >= 2 ? 'bg-blue-500' : 'bg-gray-200'}`} />
        <div className={`flex items-center justify-center w-12 h-12 rounded-full ${activeStep >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
          <DollarSign className="w-6 h-6" />
        </div>
        <div className={`w-16 h-1 ${activeStep >= 3 ? 'bg-blue-500' : 'bg-gray-200'}`} />
        <div className={`flex items-center justify-center w-12 h-12 rounded-full ${activeStep >= 3 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
          <ReceiptText className="w-6 h-6" />
        </div>
      </div>
    );
  };

  // Rendu des boutons de navigation
  const renderNavigationButtons = () => {
    return (
      <div className="flex justify-between mt-6">
        {activeStep > 1 ? (
          <Button 
            variant="outline" 
            onClick={goToPreviousStep} 
            className="flex items-center gap-2 border-blue-300 hover:bg-blue-50"
            disabled={isLoading}
          >
            <ChevronLeft className="w-4 h-4" /> Précédent
          </Button>
        ) : (
          <Button 
            variant="outline" 
            onClick={onClose} 
            className="flex items-center gap-2 border-blue-300 hover:bg-blue-50"
            disabled={isLoading}
          >
            <X className="w-4 h-4" /> Annuler
          </Button>
        )}
        
        {activeStep < 3 ? (
          <Button 
            onClick={goToNextStep} 
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
          >
            Suivant <ChevronRight className="w-4 h-4" />
          </Button>
        ) : (
          <Button 
            type="submit"
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700" 
            disabled={isLoading || (activeStep === 3 && error !== null)}
            onClick={handleSubmit}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" /> Traitement...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4" /> Confirmer
              </>
            )}
          </Button>
        )}
      </div>
    );
  };

  // Rendu de l'étape 1: Informations expéditeur et destinataire
  const renderStep1 = () => {
    if (activeStep !== 1) return null;
    
    return (
      <div className="space-y-6 fade-in slide-in">
        <div>
          <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
            <UserRound className="w-5 h-5" /> Informations de l'expéditeur
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label className="text-blue-700" htmlFor="expediteur-nom">Nom</Label>
              <Input
                id="expediteur-nom" 
                name="nom" 
                value={formData.expediteur.nom} 
                onChange={(e) => handleClientChange('expediteur', e)} 
                placeholder="Nom"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
            <div>
              <Label className="text-blue-700" htmlFor="expediteur-prenom">Prénom</Label>
              <Input
                id="expediteur-prenom" 
                name="prenom" 
                value={formData.expediteur.prenom} 
                onChange={(e) => handleClientChange('expediteur', e)}
                placeholder="Prénom"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
            <div>
              <Label className="text-blue-700" htmlFor="expediteur-telephone">Téléphone</Label>
              <Input
                id="expediteur-telephone" 
                name="telephone" 
                value={formData.expediteur.telephone} 
                onChange={(e) => handleClientChange('expediteur', e)}
                placeholder="Téléphone"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
            <UserRound className="w-5 h-5" /> Informations du destinataire
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label className="text-blue-700" htmlFor="destinataire-nom">Nom</Label>
              <Input
                id="destinataire-nom" 
                name="nom" 
                value={formData.destinataire.nom} 
                onChange={(e) => handleClientChange('destinataire', e)} 
                placeholder="Nom"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
            <div>
              <Label className="text-blue-700" htmlFor="destinataire-prenom">Prénom</Label>
              <Input
                id="destinataire-prenom" 
                name="prenom" 
                value={formData.destinataire.prenom} 
                onChange={(e) => handleClientChange('destinataire', e)}
                placeholder="Prénom"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
            <div>
              <Label className="text-blue-700" htmlFor="destinataire-telephone">Téléphone</Label>
              <Input
                id="destinataire-telephone" 
                name="telephone" 
                value={formData.destinataire.telephone} 
                onChange={(e) => handleClientChange('destinataire', e)}
                placeholder="Téléphone"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
          </div>
        </div>
        
        {stepErrors[1] && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{stepErrors[1]}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  // Rendu de l'étape 2: Détails du transfert
  const renderStep2 = () => {
    if (activeStep !== 2) return null;
    
    return (
      <div className="space-y-6 fade-in slide-in">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="montant" className="flex items-center gap-2 text-blue-700">
                <Banknote className="w-4 h-4" /> Montant
              </Label>
              <Input
                id="montant"
                name="montant"
                value={formData.montant}
                onChange={handleInputChange}
                placeholder="0.00"
                type="number"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>

            <div>
              <Label htmlFor="service" className="flex items-center gap-2 text-blue-700">
                <FileCheck className="w-4 h-4" /> Service
              </Label>
              <Select
                value={formData.service}
                onValueChange={(value) => handleSelectChange('service', value)}
              >
                <SelectTrigger className="w-full border-blue-200 focus-visible:ring-blue-500 focus-visible:border-blue-500">
                  <SelectValue placeholder="Sélectionner un service" />
                </SelectTrigger>
                <SelectContent>
                  {isServicesLoading ? (
                    <SelectItem value="loading" disabled>
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Chargement des services...
                      </div>
                    </SelectItem>
                  ) : servicesError ? (
                    <SelectItem value="error" disabled>
                      <div className="flex items-center gap-2 text-red-500">
                        <AlertCircle className="h-4 w-4" />
                        Erreur de chargement: {servicesError}
                      </div>
                    </SelectItem>
                  ) : services.length === 0 ? (
                    <SelectItem value="empty" disabled>
                      Aucun service disponible
                    </SelectItem>
                  ) : (
                    services.map((service) => (
                      <SelectItem key={service.id} value={service.id.toString()}>
                        {service.nom}
                        {service.description && (
                          <span className="ml-2 text-xs text-muted-foreground">
                            ({service.description})
                          </span>
                        )}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="type" className="flex items-center gap-2 text-blue-700">
                <Send className="w-4 h-4" /> Type de transfert
              </Label>
              <div className="flex gap-4 mt-1">
                {transferTypes.map((type) => (
                  <div key={type} className="flex items-center">
                    <input
                      type="radio"
                      id={`type-${type}`}
                      name="type"
                      value={type}
                      checked={formData.type === type}
                      onChange={(e) => handleSelectChange('type', e.target.value)}
                      className="mr-2"
                    />
                    <label htmlFor={`type-${type}`}>
                      {type === 'national' ? 'National' : 'International'}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="operation_type" className="flex items-center gap-2 text-blue-700">
                <CircleDollarSign className="w-4 h-4" /> Type d'opération
              </Label>
              <Select
                value={formData.operation_type}
                onValueChange={(value) => handleSelectChange('operation_type', value)}
              >
                <SelectTrigger className="w-full border-blue-200 focus-visible:ring-blue-500 focus-visible:border-blue-500">
                  <SelectValue placeholder="Sélectionner le type d'opération" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="envoi">
                    <div className="flex items-center gap-2">
                      <Send className="h-4 w-4 text-blue-500" />
                      <span>Envoi</span>
                      <span className="text-xs text-muted-foreground ml-2">
                        (Envoyer de l'argent)
                      </span>
                    </div>
                  </SelectItem>
                  <SelectItem value="retrait">
                    <div className="flex items-center gap-2">
                      <Banknote className="h-4 w-4 text-green-500" />
                      <span>Retrait</span>
                      <span className="text-xs text-muted-foreground ml-2">
                        (Retirer de l'argent)
                      </span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label htmlFor="devise_source" className="flex items-center gap-2 text-blue-700">
                <Home className="w-4 h-4" /> Devise source
              </Label>
              <Select
                value={formData.devise_source}
                onValueChange={(value) => handleSelectChange('devise_source', value)}
              >
                <SelectTrigger id="devise_source" className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300">
                  <SelectValue placeholder="Devise source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Devises disponibles</SelectLabel>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code}>
                        {currency.name} ({currency.symbol})
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="devise_destination" className="flex items-center gap-2 text-blue-700">
                <Globe className="w-4 h-4" /> Devise destination
              </Label>
              <Select
                value={formData.devise_destination}
                onValueChange={(value) => handleSelectChange('devise_destination', value)}
              >
                <SelectTrigger id="devise_destination" className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300">
                  <SelectValue placeholder="Devise destination" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Devises disponibles</SelectLabel>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code}>
                        {currency.name} ({currency.symbol})
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes" className="flex items-center gap-2 text-blue-700">
                <Info className="w-4 h-4" /> Notes (optionnel)
              </Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="Notes ou instructions spéciales pour ce transfert"
                className="mt-1 border-blue-200 focus:border-blue-400 focus:ring-blue-300"
              />
            </div>
          </div>
        </div>
        
        {stepErrors[2] && (
          <Alert variant="destructive" className="mt-4 animate-pulse">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur de validation</AlertTitle>
            <AlertDescription>{stepErrors[2]}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  // Rendu de l'étape 3: Confirmation et devis
  const renderStep3 = () => {
    if (activeStep !== 3) return null;
    
    return (
      <div className="space-y-6 fade-in slide-in">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <CheckCircle className="w-5 h-5" /> Confirmation du transfert
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="border-blue-200 hover:border-blue-400 transition-colors duration-300">
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2 text-blue-700">
                <UserRound className="w-5 h-5" /> Expéditeur
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><span className="font-medium text-blue-700">Nom:</span> {formData.expediteur.nom}</p>
                <p><span className="font-medium text-blue-700">Prénom:</span> {formData.expediteur.prenom}</p>
                <p><span className="font-medium text-blue-700">Téléphone:</span> {formData.expediteur.telephone}</p>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-blue-200 hover:border-blue-400 transition-colors duration-300">
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2 text-blue-700">
                <UserRound className="w-5 h-5" /> Destinataire
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><span className="font-medium text-blue-700">Nom:</span> {formData.destinataire.nom}</p>
                <p><span className="font-medium text-blue-700">Prénom:</span> {formData.destinataire.prenom}</p>
                <p><span className="font-medium text-blue-700">Téléphone:</span> {formData.destinataire.telephone}</p>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Card className="border-blue-200 hover:border-blue-400 transition-colors duration-300">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2 text-blue-700">
              <ReceiptText className="w-5 h-5" /> Détails du transfert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <p><span className="font-medium text-blue-700">Service:</span> {getServiceName(formData.service)}</p>
                <p><span className="font-medium text-blue-700">Type:</span> {formData.type === 'national' ? 'National' : 'International'}</p>
                {formData.notes && (
                  <p><span className="font-medium text-blue-700">Notes:</span> {formData.notes}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-blue-700">Montant:</span>
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300 ml-1">
                    {formData.montant} {formData.devise_source}
                  </Badge>
                </div>
                
                {isQuoteLoading ? (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Calcul du devis en cours...
                  </div>
                ) : error ? (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Erreur de calcul du devis</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                    <div className="mt-3">
                      <Button onClick={() => calculateQuote()} size="sm" variant="outline">
                        <RefreshCw className="h-4 w-4 mr-2" /> Réessayer
                      </Button>
                    </div>
                  </Alert>
                ) : quote ? (
                  <div className="space-y-2 border rounded-md p-3 bg-muted/50">
                    <div className="flex items-center gap-1 text-sm mb-2">
                      <span className="font-medium text-blue-700">Type d'opération:</span>
                      <Badge variant={formData.operation_type === 'envoi' ? 'default' : 'secondary'} className="ml-1">
                        {formData.operation_type === 'envoi' ? (
                          <>
                            <Send className="h-3 w-3 mr-1" />
                            Envoi
                          </>
                        ) : (
                          <>
                            <Banknote className="h-3 w-3 mr-1" />
                            Retrait
                          </>
                        )}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <span className="font-medium text-blue-700">Montant {formData.operation_type === 'envoi' ? 'envoyé' : 'retiré'}:</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">{quote.montant_envoye} {quote.devise_source}</Badge>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <span className="font-medium text-blue-700">Frais:</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">{quote.frais} {quote.devise_source}</Badge>
                    </div>
                    {quote.taux_change !== 1 && (
                      <div className="flex items-center gap-1 text-sm">
                        <span className="font-medium text-blue-700">Taux de change:</span>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">1 {quote.devise_source} = {quote.taux_change} {quote.devise_destination}</Badge>
                      </div>
                    )}
                    <div className="flex items-center gap-1 text-sm font-semibold mt-3 pt-2 border-t border-blue-300">
                      <span className="font-bold text-blue-800">Montant à recevoir:</span>
                      <Badge className="bg-green-600 text-white hover:bg-green-700 ml-1 px-2 py-1">
                        {quote.montant_a_recevoir || '0'} {quote.devise_destination}
                      </Badge>
                    </div>
                    <div className="mt-4 pt-3 border-t border-blue-200">
                      <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700">
                        <CheckCircle className="h-4 w-4 mr-2" /> Confirmer le transfert
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Devis non calculé</AlertTitle>
                    <AlertDescription>Cliquez sur le bouton ci-dessous pour calculer le devis</AlertDescription>
                    <div className="mt-3">
                      <Button onClick={() => calculateQuote()} size="sm">
                        <Calculator className="h-4 w-4 mr-2" /> Calculer le devis
                      </Button>
                    </div>
                  </Alert>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <Card className="w-full border-blue-400 shadow-md">
      <CardHeader>
        <CardTitle className="text-blue-700">Nouveau Transfert Externe</CardTitle>
        <CardDescription className="text-blue-500">Créez un nouveau transfert vers un service externe.</CardDescription>
      </CardHeader>
      
      <CardContent>
        {renderStepIndicators()}
        <form onSubmit={handleSubmit}>
          {renderStep1()}
          {renderStep2()}
          {renderStep3()}
        </form>
      </CardContent>
      
      <CardFooter>
        {renderNavigationButtons()}
      </CardFooter>
    </Card>
  );
}