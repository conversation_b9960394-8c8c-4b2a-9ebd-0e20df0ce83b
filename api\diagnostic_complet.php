<?php
/**
 * Diagnostic complet des transferts
 */

// Headers CORS
header("Access-Control-Allow-Origin: http://localhost:8080");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/services/transfer_service.php';

$diagnostic = [
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => []
];

try {
    // Test 1: Connexion base de données
    $diagnostic['tests']['database'] = [
        'name' => 'Connexion base de données',
        'status' => 'success',
        'details' => []
    ];
    
    if ($pdo) {
        $diagnostic['tests']['database']['details']['connection'] = 'OK';
        
        // Vérifier la table services_transfert
        $stmt = $pdo->query("SHOW TABLES LIKE 'services_transfert'");
        if ($stmt->rowCount() > 0) {
            $diagnostic['tests']['database']['details']['table_services'] = 'EXISTS';
            
            // Compter les services
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM services_transfert WHERE actif = 1");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            $diagnostic['tests']['database']['details']['active_services'] = $count['count'];
            
            // Lister les services
            $stmt = $pdo->query("SELECT id, nom, type FROM services_transfert WHERE actif = 1");
            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $diagnostic['tests']['database']['details']['services_list'] = $services;
        } else {
            $diagnostic['tests']['database']['status'] = 'error';
            $diagnostic['tests']['database']['details']['table_services'] = 'NOT_EXISTS';
        }
    } else {
        $diagnostic['tests']['database']['status'] = 'error';
        $diagnostic['tests']['database']['details']['connection'] = 'FAILED';
    }
    
    // Test 2: Classe TransferService
    $diagnostic['tests']['transfer_service'] = [
        'name' => 'Classe TransferService',
        'status' => 'success',
        'details' => []
    ];
    
    try {
        $transferService = new TransferService($pdo);
        $diagnostic['tests']['transfer_service']['details']['instantiation'] = 'OK';
        
        // Test calcul de frais
        $frais = $transferService->calculateFees('MIXX', 50000, 'envoi');
        $diagnostic['tests']['transfer_service']['details']['calculate_fees'] = [
            'service' => 'MIXX',
            'amount' => 50000,
            'operation' => 'envoi',
            'fees' => $frais
        ];
        
        // Test taux de change
        $taux = $transferService->getExchangeRate('XOF', 'XOF');
        $diagnostic['tests']['transfer_service']['details']['exchange_rate'] = [
            'from' => 'XOF',
            'to' => 'XOF',
            'rate' => $taux
        ];
        
    } catch (Exception $e) {
        $diagnostic['tests']['transfer_service']['status'] = 'error';
        $diagnostic['tests']['transfer_service']['details']['error'] = $e->getMessage();
    }
    
    // Test 3: API Services
    $diagnostic['tests']['api_services'] = [
        'name' => 'API Services',
        'status' => 'success',
        'details' => []
    ];
    
    try {
        $stmt = $pdo->query("SELECT id, nom, type, actif FROM services_transfert WHERE actif = 1");
        $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $diagnostic['tests']['api_services']['details']['query_result'] = $services;
        $diagnostic['tests']['api_services']['details']['count'] = count($services);
        
        // Simuler la réponse API
        $api_response = [
            'success' => true,
            'data' => $services
        ];
        $diagnostic['tests']['api_services']['details']['api_response'] = $api_response;
        
    } catch (Exception $e) {
        $diagnostic['tests']['api_services']['status'] = 'error';
        $diagnostic['tests']['api_services']['details']['error'] = $e->getMessage();
    }
    
    // Test 4: API Quote
    $diagnostic['tests']['api_quote'] = [
        'name' => 'API Quote',
        'status' => 'success',
        'details' => []
    ];
    
    try {
        $test_data = [
            'montant' => 50000,
            'service' => '1',
            'devise_source' => 'XOF',
            'devise_destination' => 'XOF',
            'operation_type' => 'envoi'
        ];
        
        $diagnostic['tests']['api_quote']['details']['input_data'] = $test_data;
        
        // Vérifier que le service existe
        $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE id = ? AND actif = 1");
        $stmt->execute([$test_data['service']]);
        $service_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($service_info) {
            $diagnostic['tests']['api_quote']['details']['service_found'] = $service_info;
            
            $transferService = new TransferService($pdo);
            $montant = (float)$test_data['montant'];
            $service_nom = $service_info['nom'];
            $operation_type = $test_data['operation_type'];
            
            $frais = $transferService->calculateFees($service_nom, $montant, $operation_type);
            $taux_change = $transferService->getExchangeRate($test_data['devise_source'], $test_data['devise_destination']);
            $montant_a_recevoir = ($montant - $frais) * $taux_change;
            
            $quote_result = [
                'montant_source' => $montant,
                'montant_envoye' => $montant,
                'frais' => $frais,
                'taux_change' => $taux_change,
                'devise_source' => $test_data['devise_source'],
                'devise_destination' => $test_data['devise_destination'],
                'montant_a_recevoir' => round($montant_a_recevoir, 2),
                'montant_destination' => round($montant_a_recevoir, 2)
            ];
            
            $diagnostic['tests']['api_quote']['details']['calculation_result'] = $quote_result;
            
            // Simuler la réponse API
            $api_response = [
                'success' => true,
                'data' => $quote_result
            ];
            $diagnostic['tests']['api_quote']['details']['api_response'] = $api_response;
            
        } else {
            $diagnostic['tests']['api_quote']['status'] = 'error';
            $diagnostic['tests']['api_quote']['details']['error'] = 'Service non trouvé avec ID: ' . $test_data['service'];
        }
        
    } catch (Exception $e) {
        $diagnostic['tests']['api_quote']['status'] = 'error';
        $diagnostic['tests']['api_quote']['details']['error'] = $e->getMessage();
    }
    
    // Test 5: Variables d'environnement et configuration
    $diagnostic['tests']['environment'] = [
        'name' => 'Environnement et configuration',
        'status' => 'success',
        'details' => [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
            'pdo_available' => class_exists('PDO'),
            'json_available' => function_exists('json_encode'),
            'curl_available' => function_exists('curl_init'),
        ]
    ];
    
} catch (Exception $e) {
    $diagnostic['global_error'] = [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];
}

// Calculer le statut global
$global_status = 'success';
foreach ($diagnostic['tests'] as $test) {
    if ($test['status'] === 'error') {
        $global_status = 'error';
        break;
    }
}

$diagnostic['global_status'] = $global_status;

// Retourner le diagnostic
echo json_encode($diagnostic, JSON_PRETTY_PRINT);
?>
