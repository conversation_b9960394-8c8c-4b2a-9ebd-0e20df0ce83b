import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, TrendingUp, Users, FileSpreadsheet, Banknote, Calendar, Download } from "lucide-react";
import { API_BASE_URL } from "@/config";
import { Button } from "@/components/ui/button";

// Types
interface StatCategory {
  label: string;
  count: number;
  montant_total: number;
}

interface StatsData {
  par_statut: StatCategory[];
  par_service: StatCategory[];
  par_type: StatCategory[];
  commissions: {
    total_commission: number;
  };
}

export function TransfertStats() {
  // États
  const [statsData, setStatsData] = useState<StatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fonction pour trouver le token d'authentification
  const findToken = () => {
    const keys = ['token', 'authToken', 'jwt', 'access_token'];
    for (const key of keys) {
      const value = localStorage.getItem(key);
      if (value && value.startsWith('eyJ')) {
        return value;
      }
    }
    return null;
  };

  // Chargement initial des données
  useEffect(() => {
    fetchStats();
  }, []);

  // Fonction pour récupérer les statistiques
  const fetchStats = async () => {
    setIsLoading(true);
    try {
      const token = findToken();

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/admin_transferts.php?action=stats`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setStatsData(result.data);
      } else {
        toast({
          title: "Erreur",
          description: result.message || "Une erreur est survenue",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);

      toast({
        title: "Erreur",
        description: `Impossible de charger les statistiques: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour télécharger le rapport (à implémenter plus tard)
  const downloadReport = () => {
    toast({
      title: "Information",
      description: "La fonctionnalité de téléchargement de rapport sera disponible prochainement."
    });
  };

  // Formatage du montant
  const formatMontant = (montant: number) => {
    return montant.toLocaleString('fr-FR') + " XOF";
  };

  // Formatage du statut pour affichage
  const formatStatut = (statut: string) => {
    switch (statut.toLowerCase()) {
      case 'en_attente':
        return 'En attente';
      case 'valide':
        return 'Validé';
      case 'complete':
        return 'Complété';
      case 'annule':
        return 'Annulé';
      default:
        return statut;
    }
  };

  // Rendu conditionnel pour le chargement
  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total des transferts */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total des transferts
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsData?.par_statut.reduce((acc, stat) => acc + stat.count, 0) || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Tous statuts confondus
            </p>
          </CardContent>
        </Card>

        {/* Montant total */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Montant total
            </CardTitle>
            <Banknote className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatMontant(statsData?.par_statut.reduce((acc, stat) => acc + Number(stat.montant_total || 0), 0) || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Tous transferts confondus
            </p>
          </CardContent>
        </Card>

        {/* Revenus des commissions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Revenus des commissions
            </CardTitle>
            <FileSpreadsheet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatMontant(Number(statsData?.commissions?.total_commission || 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Sur les transferts validés/complétés
            </p>
          </CardContent>
        </Card>

        {/* Transferts en attente */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Transferts en attente
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsData?.par_statut.find(s => s.label === 'en_attente')?.count || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Nécessitant une validation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Détails par statut */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Transferts par statut</CardTitle>
            <CardDescription>Répartition des transferts selon leur statut</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statsData?.par_statut.map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{formatStatut(stat.label)}</p>
                    <p className="text-xs text-muted-foreground">{stat.count} transferts</p>
                  </div>
                  <p className="text-sm font-semibold">{formatMontant(Number(stat.montant_total || 0))}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Détails par service */}
        <Card>
          <CardHeader>
            <CardTitle>Transferts par service</CardTitle>
            <CardDescription>Répartition par service de transfert</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statsData?.par_service.map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{stat.label || "Service inconnu"}</p>
                    <p className="text-xs text-muted-foreground">{stat.count} transferts</p>
                  </div>
                  <p className="text-sm font-semibold">{formatMontant(Number(stat.montant_total || 0))}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Détails par type */}
      <Card>
        <CardHeader>
          <CardTitle>Transferts par type</CardTitle>
          <CardDescription>Répartition entre envois et réceptions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {statsData?.par_type.map((stat, index) => (
              <div key={index} className="bg-muted/30 p-4 rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold">{stat.label === 'envoi' ? 'Envois' : 'Réceptions'}</h3>
                  <span className="text-sm font-medium">{stat.count} transferts</span>
                </div>
                <p className="text-lg font-bold">{formatMontant(Number(stat.montant_total || 0))}</p>
              </div>
            ))}
          </div>
          <div className="mt-6 flex justify-end">
            <Button onClick={downloadReport} className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Télécharger le rapport complet
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
