import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, TrendingUp, Users, FileSpreadsheet, Banknote, Calendar, Download } from "lucide-react";
import { API_BASE_URL } from "@/config";
import { Button } from "@/components/ui/button";

// Types
interface StatCategory {
  label: string;
  count: number;
  montant_total: number;
}

interface StatsData {
  par_statut: StatCategory[];
  par_service: StatCategory[];
  par_type: StatCategory[];
  commissions: {
    total_commission: number;
  };
}

export function TransfertStats() {
  // États
  const [statsData, setStatsData] = useState<StatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fonction pour trouver le token d'authentification
  const findToken = () => {
    const keys = ['token', 'authToken', 'jwt', 'access_token'];
    for (const key of keys) {
      const value = localStorage.getItem(key);
      if (value && value.startsWith('eyJ')) {
        return value;
      }
    }
    return null;
  };

  // Chargement initial des données
  useEffect(() => {
    fetchStats();
  }, []);

  // Fonction pour récupérer les statistiques
  const fetchStats = async () => {
    setIsLoading(true);
    try {
      const token = findToken();

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/admin_transferts.php?action=stats`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setStatsData(result.data);
      } else {
        toast({
          title: "Erreur",
          description: result.message || "Une erreur est survenue",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);

      toast({
        title: "Erreur",
        description: `Impossible de charger les statistiques: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour télécharger le rapport (à implémenter plus tard)
  const downloadReport = () => {
    toast({
      title: "Information",
      description: "La fonctionnalité de téléchargement de rapport sera disponible prochainement."
    });
  };

  // Formatage du montant
  const formatMontant = (montant: number) => {
    return montant.toLocaleString('fr-FR') + " XOF";
  };

  // Formatage du statut pour affichage
  const formatStatut = (statut: string | undefined) => {
    if (!statut) return 'Inconnu';

    switch (statut.toLowerCase()) {
      case 'en_attente':
        return 'En attente';
      case 'valide':
        return 'Validé';
      case 'complete':
        return 'Complété';
      case 'annule':
        return 'Annulé';
      case 'rejete':
        return 'Rejeté';
      default:
        return statut;
    }
  };

  // Rendu conditionnel pour le chargement
  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total des transferts */}
        <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Total des transferts
            </CardTitle>
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900 dark:text-blue-100">
              {statsData?.par_statut.reduce((acc, stat) => acc + stat.count, 0) || 0}
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              Tous statuts confondus
            </p>
          </CardContent>
        </Card>

        {/* Montant total */}
        <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">
              Montant total
            </CardTitle>
            <div className="p-2 bg-green-500/20 rounded-lg">
              <Banknote className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">
              {formatMontant(statsData?.par_statut.reduce((acc, stat) => acc + Number(stat.montant_total || 0), 0) || 0)}
            </div>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              Tous transferts confondus
            </p>
          </CardContent>
        </Card>

        {/* Revenus des commissions */}
        <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">
              Revenus des commissions
            </CardTitle>
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <FileSpreadsheet className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {formatMontant(Number(statsData?.commissions?.total_commission || 0))}
            </div>
            <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
              Sur les transferts validés/complétés
            </p>
          </CardContent>
        </Card>

        {/* Transferts en attente */}
        <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -mr-10 -mt-10"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">
              Transferts en attente
            </CardTitle>
            <div className="p-2 bg-orange-500/20 rounded-lg">
              <Calendar className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-900 dark:text-orange-100">
              {statsData?.par_statut.find(s => s.statut === 'en_attente')?.count || 0}
            </div>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              Nécessitant une validation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Détails par statut */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-slate-800 dark:text-slate-200">
              <div className="p-2 bg-slate-200 dark:bg-slate-700 rounded-lg">
                <TrendingUp className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              </div>
              Transferts par statut
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400">
              Répartition des transferts selon leur statut
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statsData?.par_statut.map((stat, index) => {
                const getStatutColor = (statut: string) => {
                  switch (statut) {
                    case 'en_attente': return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900 dark:text-orange-200';
                    case 'valide': return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200';
                    case 'complete': return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200';
                    case 'annule': return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200';
                    default: return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-200';
                  }
                };

                return (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatutColor(stat.statut)}`}>
                        {formatStatut(stat.statut)}
                      </span>
                      <div>
                        <p className="text-xs text-slate-600 dark:text-slate-400">{stat.count} transferts</p>
                      </div>
                    </div>
                    <p className="text-sm font-bold text-slate-900 dark:text-slate-100">{formatMontant(Number(stat.montant_total || 0))}</p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Détails par service */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900 dark:to-indigo-800">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-indigo-800 dark:text-indigo-200">
              <div className="p-2 bg-indigo-200 dark:bg-indigo-700 rounded-lg">
                <FileSpreadsheet className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              Transferts par service
            </CardTitle>
            <CardDescription className="text-indigo-600 dark:text-indigo-400">
              Répartition par service de transfert
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statsData?.par_service.map((stat, index) => {
                const serviceColors = [
                  'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900 dark:text-emerald-200',
                  'bg-cyan-100 text-cyan-800 border-cyan-200 dark:bg-cyan-900 dark:text-cyan-200',
                  'bg-violet-100 text-violet-800 border-violet-200 dark:bg-violet-900 dark:text-violet-200',
                  'bg-rose-100 text-rose-800 border-rose-200 dark:bg-rose-900 dark:text-rose-200',
                  'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900 dark:text-amber-200'
                ];

                return (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white dark:bg-indigo-800 border border-indigo-200 dark:border-indigo-700 hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium border ${serviceColors[index % serviceColors.length]}`}>
                        {stat.service || "Service inconnu"}
                      </span>
                      <div>
                        <p className="text-xs text-indigo-600 dark:text-indigo-400">{stat.count} transferts</p>
                      </div>
                    </div>
                    <p className="text-sm font-bold text-indigo-900 dark:text-indigo-100">{formatMontant(Number(stat.montant_total || 0))}</p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Détails par type */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900 dark:to-teal-800">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-teal-800 dark:text-teal-200">
            <div className="p-2 bg-teal-200 dark:bg-teal-700 rounded-lg">
              <Banknote className="h-4 w-4 text-teal-600 dark:text-teal-400" />
            </div>
            Transferts par type
          </CardTitle>
          <CardDescription className="text-teal-600 dark:text-teal-400">
            Répartition entre envois et réceptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {statsData?.par_type.map((stat, index) => {
              const isEnvoi = stat.type === 'envoi';
              return (
                <div key={index} className={`relative overflow-hidden p-6 rounded-xl border-2 transition-all hover:shadow-lg ${
                  isEnvoi
                    ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 dark:from-blue-900 dark:to-blue-800 dark:border-blue-700'
                    : 'bg-gradient-to-br from-green-50 to-green-100 border-green-200 dark:from-green-900 dark:to-green-800 dark:border-green-700'
                }`}>
                  <div className={`absolute top-0 right-0 w-16 h-16 rounded-full -mr-8 -mt-8 ${
                    isEnvoi ? 'bg-blue-500/20' : 'bg-green-500/20'
                  }`}></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${
                          isEnvoi ? 'bg-blue-500/20' : 'bg-green-500/20'
                        }`}>
                          {isEnvoi ? (
                            <TrendingUp className={`h-5 w-5 ${isEnvoi ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400'}`} />
                          ) : (
                            <Calendar className={`h-5 w-5 ${isEnvoi ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400'}`} />
                          )}
                        </div>
                        <h3 className={`text-lg font-bold ${
                          isEnvoi ? 'text-blue-800 dark:text-blue-200' : 'text-green-800 dark:text-green-200'
                        }`}>
                          {stat.type === 'envoi' ? 'Envois' : 'Réceptions'}
                        </h3>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium border ${
                        isEnvoi
                          ? 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200'
                          : 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200'
                      }`}>
                        {stat.count} transferts
                      </span>
                    </div>
                    <p className={`text-2xl font-bold ${
                      isEnvoi ? 'text-blue-900 dark:text-blue-100' : 'text-green-900 dark:text-green-100'
                    }`}>
                      {formatMontant(Number(stat.montant_total || 0))}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="mt-8 flex justify-end">
            <Button
              onClick={downloadReport}
              className="flex items-center gap-2 bg-teal-600 hover:bg-teal-700 text-white shadow-lg hover:shadow-xl transition-all"
            >
              <Download className="h-4 w-4" />
              Télécharger le rapport complet
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
