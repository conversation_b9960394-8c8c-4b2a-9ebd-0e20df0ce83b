<?php
/**
 * Corriger les dates invalides dans la table transferts
 */

echo "CORRECTION DES DATES INVALIDES\n";
echo "==============================\n\n";

try {
    require_once 'api/config.php';
    
    // Corriger les date_transfert invalides
    echo "1. Correction des date_transfert invalides...\n";
    $stmt = $pdo->prepare("
        UPDATE transferts 
        SET date_transfert = DATE(date_operation)
        WHERE date_transfert IS NULL 
           OR date_transfert = '' 
           OR date_transfert = '0000-00-00'
    ");
    
    $result = $stmt->execute();
    $affected = $stmt->rowCount();
    
    if ($result) {
        echo "✅ {$affected} transferts corrigés pour date_transfert\n";
    } else {
        echo "❌ Erreur lors de la correction des date_transfert\n";
    }
    
    // Corriger les date_operation invalides si nécessaire
    echo "\n2. Correction des date_operation invalides...\n";
    $stmt = $pdo->prepare("
        UPDATE transferts 
        SET date_operation = created_at
        WHERE date_operation IS NULL 
           OR date_operation = '' 
           OR date_operation = '0000-00-00 00:00:00'
    ");
    
    $result = $stmt->execute();
    $affected = $stmt->rowCount();
    
    if ($result) {
        echo "✅ {$affected} transferts corrigés pour date_operation\n";
    } else {
        echo "❌ Erreur lors de la correction des date_operation\n";
    }
    
    // Vérifier le résultat
    echo "\n3. Vérification après correction...\n";
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM transferts 
        WHERE date_transfert IS NULL OR date_transfert = '' OR date_transfert = '0000-00-00'
    ");
    $null_dates = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Transferts avec date_transfert encore invalide: {$null_dates}\n";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM transferts 
        WHERE date_operation IS NULL OR date_operation = '' OR date_operation = '0000-00-00 00:00:00'
    ");
    $null_op_dates = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Transferts avec date_operation encore invalide: {$null_op_dates}\n";
    
    // Afficher quelques exemples corrigés
    echo "\n4. Exemples de transferts corrigés:\n";
    $stmt = $pdo->query("
        SELECT id, reference, date_transfert, date_operation 
        FROM transferts 
        WHERE id IN (23, 24)
        ORDER BY id DESC
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "ID {$row['id']}: date_transfert = {$row['date_transfert']}, date_operation = {$row['date_operation']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Correction terminée\n";
?>
