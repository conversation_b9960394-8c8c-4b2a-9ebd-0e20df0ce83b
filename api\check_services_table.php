<?php
require_once 'config.php';

echo "=== STRUCTURE DE LA TABLE services_transfert ===\n";

try {
    $stmt = $pdo->query('SHOW COLUMNS FROM services_transfert');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach($columns as $column) {
        echo $column['Field'] . ' - ' . $column['Type'] . "\n";
    }
    
    echo "\n=== DONNÉES EXEMPLE ===\n";
    $stmt = $pdo->query('SELECT * FROM services_transfert LIMIT 2');
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach($services as $service) {
        echo "Service: " . ($service['nom'] ?? 'N/A') . "\n";
        echo "Code: " . ($service['code'] ?? 'N/A') . "\n";
        echo "Commission envoi fixe: " . ($service['commission_envoi_fixe'] ?? 'N/A') . "\n";
        echo "Commission envoi %: " . ($service['commission_envoi_pourcentage'] ?? 'N/A') . "\n";
        echo "Commission retrait fixe: " . ($service['commission_retrait_fixe'] ?? 'N/A') . "\n";
        echo "Commission retrait %: " . ($service['commission_retrait_pourcentage'] ?? 'N/A') . "\n";
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
?>
