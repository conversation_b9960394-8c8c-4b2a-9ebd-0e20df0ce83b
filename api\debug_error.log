[GERANT] Début création gérant[GERANT] Données nettoyées: {"nom":"<PERSON><PERSON><PERSON><PERSON>","telephone":"+22893526352","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2024-07-16"}[GERANT] Création utilisateur avec email: <EMAIL>[GERANT] Utilisateur créé avec ID: 18[GERANT] Création gérant avec user_id: 18[GERANT] Gérant créé avec ID: 4[GERANT] Gérant créé avec succès: <PERSON><PERSON><PERSON><PERSON>[18-Jul-2025 16:32:14 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Début création gérant[GERANT] Données nettoyées: {"nom":"MOUSSA Oumand\u00e9","telephone":"+22890887401","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2024-07-12"}[GERANT] Création utilisateur avec email: <EMAIL>[GERANT] Utilisateur créé avec ID: 19[GERANT] Création gérant avec user_id: 19[GERANT] Gérant créé avec ID: 5[GERANT] Gérant créé avec succès: MOUSSA Oumandé[18-Jul-2025 16:32:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 16:36:41 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 16:39:00 Europe/Berlin] PHP Fatal error:  Uncaught Error: Undefined constant "DB_PASS" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_gerants_response.php:6
Stack trace:
#0 {main}
  thrown in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_gerants_response.php on line 6
[18-Jul-2025 16:43:13 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 16:44:47 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 16:51:59 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MDMxOSwiZXhwIjoxNzUyOTM2NzE5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.hgxryI8v37Df0xVP-6YV2Qm4-zwEVpMwsaoF8FCN_jM"}
[18-Jul-2025 16:52:06 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752850326884[GERANT] Method: GET, Gerant ID: [18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 16:54:01 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MDQ0MSwiZXhwIjoxNzUyOTM2ODQxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.iBTt5BXcpZOMJrF12shZWu6P6JfUsVcWno_WIPrkmVg"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752850449533[GERANT] Method: GET, Gerant ID: [18-Jul-2025 16:54:09 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752850458931[GERANT] Method: POST, Gerant ID: [GERANT] Début création gérant[GERANT] Données nettoyées: {"nom":"Rita Maman","telephone":"+22885741247","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2025-07-26"}[GERANT] Email déjà utilisé dans users: <EMAIL>[18-Jul-2025 16:54:27 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752850467570[GERANT] Method: GET, Gerant ID: [GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752850498463[GERANT] Method: POST, Gerant ID: [GERANT] Début création gérant[GERANT] Données nettoyées: {"nom":"Maman Rita","telephone":"+22891425632","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2025-07-10"}[GERANT] Création utilisateur avec email: <EMAIL>[GERANT] Utilisateur créé avec ID: 20[GERANT] Création gérant avec user_id: 20[GERANT] Gérant créé avec ID: 6[GERANT] Gérant créé avec succès: Maman Rita[18-Jul-2025 16:54:58 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752850498762[GERANT] Method: GET, Gerant ID: [18-Jul-2025 17:02:31 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752850950994[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 5[GERANT] Gérant 0 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 1 - ID: 6, Nom: Maman Rita[GERANT] Gérant 2 - ID: 5, Nom: MOUSSA Oumandé[GERANT] Gérant 3 - ID: 2, Nom: Test Gérant 16:19:07[GERANT] Gérant 4 - ID: 3, Nom: Test Gérant Final 16:30:20[18-Jul-2025 17:02:37 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 17:02:42 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 17:02:45 Europe/Berlin] [JWT_AUTH] Aucun en-tête Authorization trouvé
[18-Jul-2025 17:02:45 Europe/Berlin] [JWT_AUTH] En-têtes disponibles: ["host","connection","sec-ch-ua-platform","user-agent","sec-ch-ua","sec-ch-ua-mobile","accept","sec-fetch-site","sec-fetch-mode","sec-fetch-dest","referer","accept-encoding","accept-language","cookie"]
[18-Jul-2025 17:02:45 Europe/Berlin] [JWT_AUTH] Aucun token trouvé dans les en-têtes
[18-Jul-2025 17:02:45 Europe/Berlin] [JWT_AUTH] Aucun en-tête Authorization trouvé
[18-Jul-2025 17:02:45 Europe/Berlin] [JWT_AUTH] En-têtes disponibles: ["host","connection","sec-ch-ua-platform","user-agent","sec-ch-ua","sec-ch-ua-mobile","accept","sec-fetch-site","sec-fetch-mode","sec-fetch-dest","referer","accept-encoding","accept-language","cookie"]
[18-Jul-2025 17:02:45 Europe/Berlin] [JWT_AUTH] Aucun token pour vérification admin
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 17:02:47 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:02:51 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752850980366[18-Jul-2025 17:03:00 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 5[GERANT] Gérant 0 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 1 - ID: 6, Nom: Maman Rita[GERANT] Gérant 2 - ID: 5, Nom: MOUSSA Oumandé[GERANT] Gérant 3 - ID: 2, Nom: Test Gérant 16:19:07[GERANT] Gérant 4 - ID: 3, Nom: Test Gérant Final 16:30:20[GERANT] ID extrait de l'URL: 6[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php/6?_t=1752850993437[GERANT] Method: PUT, Gerant ID: 6[GERANT] Début mise à jour gérant ID: 6[GERANT] Données reçues pour mise à jour: {"id":6,"user_id":20,"nom":"Maman Rita","telephone":"+22890425632","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2025-07-10","moulinsAssignes":[],"created_at":"2025-07-18 14:54:58","updated_at":"2025-07-18 14:54:58","manager_id":null,"moulins":[]}[GERANT] ID extrait de l'URL: 2[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php/2?_t=1752851029685[GERANT] Method: PUT, Gerant ID: 2[GERANT] Début mise à jour gérant ID: 2[GERANT] Données reçues pour mise à jour: {"id":2,"user_id":14,"nom":"MALIK Magnim","telephone":"+22893525652","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2025-07-18","moulinsAssignes":[],"created_at":"2025-07-18 14:19:08","updated_at":"2025-07-18 14:19:08","manager_id":null,"moulins":[]}[GERANT] ID extrait de l'URL: 3[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php/3?_t=1752851066592[GERANT] Method: PUT, Gerant ID: 3[GERANT] Début mise à jour gérant ID: 3[GERANT] Données reçues pour mise à jour: {"id":3,"user_id":17,"nom":"MALOUM Azir","telephone":"+22890415263","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2025-07-18","moulinsAssignes":[],"created_at":"2025-07-18 14:30:20","updated_at":"2025-07-18 14:30:20","manager_id":null,"moulins":[]}[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752851080009[GERANT] Method: POST, Gerant ID: [GERANT] Début création gérant[GERANT] Données nettoyées: {"nom":"DMA Groupe","telephone":"+22896874521","email":"<EMAIL>","statut":"Actif","dateEmbauche":"2025-07-11"}[GERANT] Création utilisateur avec email: <EMAIL>[GERANT] Utilisateur créé avec ID: 21[GERANT] Création gérant avec user_id: 21[GERANT] Gérant créé avec ID: 7[GERANT] Gérant créé avec succès: DMA Groupe[18-Jul-2025 17:04:40 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752851080408[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 7[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=7&_t=1752851093968[GERANT] Method: PUT, Gerant ID: 7[GERANT] Début mise à jour gérant ID: 7[GERANT] Données reçues pour mise à jour: {"moulins_assignes":["8"]}[18-Jul-2025 17:05:00 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752851100750[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:07:22 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 17:07:22 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:07:22 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 17:07:22 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 17:07:22 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 17:07:22 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 17:07:25 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 17:07:25 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:07:25 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 17:07:25 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 17:07:25 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 17:07:25 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 17:07:27 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:07:27 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[GERANT] ID extrait des paramètres GET: 7[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=7&_t=1752851466489[GERANT] Method: PUT, Gerant ID: 7[GERANT] Début mise à jour gérant ID: 7[GERANT] Données reçues pour mise à jour: {"moulins_assignes":["7"]}[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752851615203[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:13:35 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:16:39 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MTc5OSwiZXhwIjoxNzUyOTM4MTk5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.H05_PPPHMfZdd50e5ft1l-cFpIx3yF18JiQZx5AkDzA"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 7[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=7[GERANT] Method: PUT, Gerant ID: 7[GERANT] Début mise à jour gérant ID: 7[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[1,2]}[GERANT] Moulins à assigner: [1,2][GERANT] Moulins désassignés pour le gérant 7[GERANT] Erreur lors de l'assignation: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`gestion_moulin_db`.`moulins`, CONSTRAINT `fk_moulins_gerant` FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE SET NULL ON UPDATE CASCADE)[18-Jul-2025 17:17:47 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 29
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:21:25 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjA4NSwiZXhwIjoxNzUyOTM4NDg1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.uDoXAiULDoEyAecsuPU26i3JWw7q8YSKbR7zkjKMFXs"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:21:32 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] ID extrait des paramètres GET: 1[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=1[GERANT] Method: PUT, Gerant ID: 1[GERANT] Début mise à jour gérant ID: 1[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[1,2]}[GERANT] ID extrait des paramètres GET: 1[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=1[GERANT] Method: PUT, Gerant ID: 1[GERANT] Début mise à jour gérant ID: 1[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[1,3]}[GERANT] ID extrait des paramètres GET: 2[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=2[GERANT] Method: PUT, Gerant ID: 2[GERANT] Début mise à jour gérant ID: 2[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[2,3]}[GERANT] Moulins à assigner: [2,3][GERANT] Moulins désassignés pour le gérant 2[GERANT] Erreur lors de l'assignation: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`gestion_moulin_db`.`moulins`, CONSTRAINT `fk_moulins_gerant` FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE SET NULL ON UPDATE CASCADE)[18-Jul-2025 17:22:57 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752852177194[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 5[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=5&_t=1752852193518[GERANT] Method: PUT, Gerant ID: 5[GERANT] Début mise à jour gérant ID: 5[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":["8"]}[GERANT] Moulins à assigner: ["8"][GERANT] Moulins désassignés pour le gérant 5[GERANT] Erreur lors de l'assignation: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`gestion_moulin_db`.`moulins`, CONSTRAINT `fk_moulins_gerant` FOREIGN KEY (`gerant_id`) REFERENCES `gerants` (`id`) ON DELETE SET NULL ON UPDATE CASCADE)[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:27:38 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjQ1OCwiZXhwIjoxNzUyOTM4ODU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.rutuA4hTcl14oTh6WjkgUbIpA2sjISwBf7rBnvQQnQE"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 7[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=7[GERANT] Method: PUT, Gerant ID: 7[GERANT] Début mise à jour gérant ID: 7[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[1,2]}[GERANT] Moulins à assigner: [1,2][GERANT] Moulins désassignés pour le gérant 7[GERANT] Moulins assignés: 1,2[GERANT] Transaction d'assignation réussie[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:28:45 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjUyNSwiZXhwIjoxNzUyOTM4OTI1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.-E0r_Ff4ytEZ7olPQLP6HVUIp78RBSb2vHcn8QWAO4A"}
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:29:22 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjU2MiwiZXhwIjoxNzUyOTM4OTYyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.UNTA8Zf1Yo7J2cywGk3h59P0S4f_uhBxO8NLEvmW-V8"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:29:28 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] ID extrait des paramètres GET: 7[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=7[GERANT] Method: PUT, Gerant ID: 7[GERANT] Début mise à jour gérant ID: 7[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[8]}[GERANT] Moulins à assigner: [8][GERANT] Moulins désassignés pour le gérant 7[GERANT] Moulins assignés: 8[GERANT] Transaction d'assignation réussie[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:29:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752852595560[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:29:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] ID extrait des paramètres GET: 4[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=4&_t=1752852630179[GERANT] Method: PUT, Gerant ID: 4[GERANT] Début mise à jour gérant ID: 4[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":["3"]}[GERANT] Moulins à assigner: ["3"][GERANT] Moulins désassignés pour le gérant 4[GERANT] Moulins assignés: 3[GERANT] Transaction d'assignation réussie[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752852630262[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:30:30 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:33:51 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MjgzMSwiZXhwIjoxNzUyOTM5MjMxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.blB4kCRTdBH2anHYB_RwNyyW_o9cvctK6gzuLWxOux8"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752852887218[GERANT] Method: GET, Gerant ID: [18-Jul-2025 17:34:47 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:37:26 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752853046299[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:39:07 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752853147137[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:40:22 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752853222226[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752853244327[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[18-Jul-2025 17:40:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:40:50 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 17:40:51 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752853254235[18-Jul-2025 17:40:54 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzcwNiwiZXhwIjoxNzUyOTQwMTA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.nISLty_IOdtLv5LWamB9FukVdD7E6OieY5a2mhb4LAk"}
[GERANT] ID extrait des paramètres GET: 2[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=2[GERANT] Method: PUT, Gerant ID: 2[GERANT] Début mise à jour gérant ID: 2[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[1]}[GERANT] Moulins à assigner: [1][GERANT] Moulins désassignés pour le gérant 2[GERANT] Moulins assignés: 1[GERANT] Transaction d'assignation réussie[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzcwNiwiZXhwIjoxNzUyOTQwMTA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.nISLty_IOdtLv5LWamB9FukVdD7E6OieY5a2mhb4LAk"}
[GERANT] ID extrait des paramètres GET: 3[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=3[GERANT] Method: PUT, Gerant ID: 3[GERANT] Début mise à jour gérant ID: 3[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[3,4]}[GERANT] Moulins à assigner: [3,4][18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:48:26 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzcwNiwiZXhwIjoxNzUyOTQwMTA2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.nISLty_IOdtLv5LWamB9FukVdD7E6OieY5a2mhb4LAk"}
[GERANT] ID extrait des paramètres GET: 4[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=4[GERANT] Method: PUT, Gerant ID: 4[GERANT] Début mise à jour gérant ID: 4[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":[1]}[GERANT] Moulins à assigner: [1][GERANT] Moulins désassignés pour le gérant 4[GERANT] Moulins assignés: 1[GERANT] Transaction d'assignation réussie[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 17:50:34 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1MzgzNCwiZXhwIjoxNzUyOTQwMjM0LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.6pEIJ3os2bF3oCjS9alme3vXFrFaJE_85teH9slkZZ0"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752853964385[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[18-Jul-2025 17:52:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:53:08 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752853988885[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 2[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=2&_t=1752854003717[GERANT] Method: PUT, Gerant ID: 2[GERANT] Début mise à jour gérant ID: 2[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":["7"]}[GERANT] Moulins à assigner: ["7"][GERANT] Moulins désassignés pour le gérant 2[GERANT] Moulins assignés: 7[GERANT] Transaction d'assignation réussie[18-Jul-2025 17:53:23 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752854003846[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait de l'URL: 6[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php/6?_t=1752854027161[GERANT] Method: PUT, Gerant ID: 6[GERANT] Début mise à jour gérant ID: 6[GERANT] Données reçues pour mise à jour: {"id":6,"user_id":20,"nom":"Maman Rita","telephone":"+22890425632","email":"<EMAIL>","statut":"Conge","dateEmbauche":"2025-07-10","moulinsAssignes":[],"created_at":"2025-07-18 14:54:58","updated_at":"2025-07-18 15:03:13","manager_id":null,"moulins":[]}[GERANT] Moulins à assigner: [][GERANT] Moulins désassignés pour le gérant 6[GERANT] Transaction d'assignation réussie[18-Jul-2025 17:53:57 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752854037075[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:54:01 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752854041180[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:57:42 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:58:18 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 17:58:26 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752854306064[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 17:58:39 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752854319677[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:00:41 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:00:54 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752854483224[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:01:23 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:01:28 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752854488850[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 5[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=5&_t=1752854500322[GERANT] Method: PUT, Gerant ID: 5[GERANT] Début mise à jour gérant ID: 5[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":["3"]}[GERANT] Moulins à assigner: ["3"][GERANT] Moulins désassignés pour le gérant 5[GERANT] Moulins assignés: 3[GERANT] Transaction d'assignation réussie[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752854500424[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:01:40 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:01:48 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752854508934[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:02:53 Europe/Berlin] [moulins.php] Exception: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '' for key 'code'
[18-Jul-2025 18:03:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752854635457[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:03:56 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752854636658[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:04:02 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 18:04:05 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 18:04:09 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752854649699[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:04:28 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752854668272[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:05:38 Europe/Berlin] [moulins.php] Exception: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '' for key 'code'
[18-Jul-2025 18:09:05 Europe/Berlin] PHP Fatal error:  Uncaught Error: Undefined constant "DB_PASS" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_moulin_creation.php:10
Stack trace:
#0 {main}
  thrown in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_moulin_creation.php on line 10
[18-Jul-2025 18:13:46 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 18:14:26 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NTI2NiwiZXhwIjoxNzUyOTQxNjY2LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.21GHZSg0Xi4xRzfIFXKEzIbyQM1BhGiQxNw2-vPhOGY"}
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 18:15:58 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NTM1OCwiZXhwIjoxNzUyOTQxNzU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.zT7txtbRSjB4T93LgCUbtDD00MGlLDz-BRbLf08HR_M"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752855390479[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:16:30 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:16:59 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752855419854[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:17:33 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752855453720[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:17:39 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752855459913[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:17:46 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:17:56 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 18:17:56 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 18:17:57 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NTQ3NywiZXhwIjoxNzUyOTQxODc3LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.NbmYCiv2QJHtE645jS968QEAquBeqDBrmWHM9U948lk"}
[18-Jul-2025 18:23:03 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752855783624[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:23:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752855784935[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] ID extrait des paramètres GET: 6[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?id=6&_t=1752855805892[GERANT] Method: PUT, Gerant ID: 6[GERANT] Début mise à jour gérant ID: 6[GERANT] Données reçues pour mise à jour: {"moulinsAssignes":["5"]}[GERANT] Moulins à assigner: ["5"][GERANT] Moulins désassignés pour le gérant 6[GERANT] Moulins assignés: 5[GERANT] Transaction d'assignation réussie[18-Jul-2025 18:23:26 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752855806008[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752855810043[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[18-Jul-2025 18:23:30 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Erreur PHP: error_log(): Write of 44 bytes failed with errno=13 Permission denied dans C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\gerants.php:12[18-Jul-2025 18:23:36 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752855816416[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:23:41 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752855821116[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752855822542[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[18-Jul-2025 18:23:42 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:24:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:24:44 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 18:31:47 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 29
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] ID utilisateur: 2
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Vérification du mot de passe: échouée
[18-Jul-2025 18:31:47 Europe/Berlin] [LOGIN] Échec: Mot de passe incorrect
[2025-07-18 18:31:47] Échec d'authentification pour l'utilisateur 'manager'.
  - Mot de passe fourni: 'manager'
  - Hash stocké en base: '$2y$10$0sSc7DsikFdTwDD29.Fi4eYxTE0oKLTiC4KLwacFp8dddmNy.ckNW'
  - Résultat de password_verify(): bool(false)
[18-Jul-2025 18:32:50 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 29
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] ID utilisateur: 2
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] User ID: 2
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Username: manager
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 18:32:50 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1NjM3MCwiZXhwIjoxNzUyOTQyNzcwLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.vX2mDEovZacLImEheU64REhcwKA1sCMewjFNyJzuZQQ"}
[18-Jul-2025 18:32:51 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:33:56 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752856436407[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:33:59 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752856439524[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:34:06 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752856446562[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] ID utilisateur: 2
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] User ID: 2
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Username: manager
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 18:42:27 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg1Njk0NywiZXhwIjoxNzUyOTQzMzQ3LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.RO4bBccg4aM3AOFdCig4FmYCU1fcCyvcCXeEsFoqb_Q"}
[18-Jul-2025 18:43:31 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?_t=1752857011078[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:43:38 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752857018254[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:43:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:54:46 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 18:58:01 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752857881895[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752857883205[18-Jul-2025 18:58:03 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 18:58:05 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 18:58:12 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752857892442[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:05:56 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 19:18:48 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/gerants.php?statut=Actif&_t=1752859128461[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:18:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752859135130[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:18:59 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 19:25:15 Europe/Berlin] JWT Validation Error: Wrong number of segments
[18-Jul-2025 19:28:39 Europe/Berlin] JWT Validation Error: Wrong number of segments
[18-Jul-2025 19:28:56 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752859736571[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:28:58 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752859738005[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:33:10 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] ID utilisateur: 1
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] User ID: 1
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Username: admin
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Rôle: admin
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 19:55:42 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg2MTM0MiwiZXhwIjoxNzUyOTQ3NzQyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.2X11xQzi4iE63z0isZnEnmCYXGILycOvQvpClNC5Agg"}
[18-Jul-2025 19:55:56 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 19:55:56 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 19:55:56 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 19:55:56 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 19:55:56 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 19:55:56 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 19:56:05 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 19:56:05 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 19:56:05 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 19:56:05 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 19:56:05 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 19:56:05 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 19:56:53 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /api/gerants.php?_t=1752861413118[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:56:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /api/gerants.php?_t=1752861415518[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:57:01 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752861421302[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 19:57:05 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 19:58:25 Europe/Berlin] JWT Validation Error: Wrong number of segments
[18-Jul-2025 19:59:25 Europe/Berlin] JWT Validation Error: Wrong number of segments
[18-Jul-2025 20:01:00 Europe/Berlin] JWT Validation Error: Wrong number of segments
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] ID utilisateur: 2
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] User ID: 2
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Username: manager
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Rôle: manager
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[18-Jul-2025 20:04:05 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mjg2MTg0NSwiZXhwIjoxNzUyOTQ4MjQ1LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.7hrfg3xS8R44uW2jlniv-uBXVsPjRBKgzwV5WRnYj04"}
[18-Jul-2025 20:05:44 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:05:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:05:44 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:05:44 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 20:05:44 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 20:05:44 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 20:05:47 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:05:47 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:05:47 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:05:47 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 20:05:47 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 20:05:47 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 20:05:52 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:05:57 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752861957568[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:05:58 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752861958484[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:06:01 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:06:03 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:06:10 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /api/gerants.php?statut=Actif&_t=1752861970383[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:06:11 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:06:12 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:07:01 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:08:12 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:08:12 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:08:12 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:08:12 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 20:08:12 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 20:08:12 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 20:08:16 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:08:16 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:08:16 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:08:16 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 20:08:16 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 20:08:16 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 20:11:36 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:11:36 Europe/Berlin] Recherche des moulins pour le manager ID: 1
[18-Jul-2025 20:11:36 Europe/Berlin] Moulins trouvés pour le manager 1 (relation manager_id): 0
[18-Jul-2025 20:11:36 Europe/Berlin] Aucun moulin trouvé pour le manager 1 - vérification des gérants assignés
[18-Jul-2025 20:11:36 Europe/Berlin] Nombre de gérants assignés au manager 1: 0
[18-Jul-2025 20:11:36 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:12:05 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:12:05 Europe/Berlin] Recherche des moulins pour le manager ID: 1
[18-Jul-2025 20:12:06 Europe/Berlin] Moulins trouvés pour le manager 1 (relation manager_id): 0
[18-Jul-2025 20:12:06 Europe/Berlin] Aucun moulin trouvé pour le manager 1 - vérification des gérants assignés
[18-Jul-2025 20:12:06 Europe/Berlin] Nombre de gérants assignés au manager 1: 0
[18-Jul-2025 20:12:06 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:12:42 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:12:42 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:12:42 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 0
[18-Jul-2025 20:12:42 Europe/Berlin] Aucun moulin trouvé pour le manager 2 - vérification des gérants assignés
[18-Jul-2025 20:12:42 Europe/Berlin] Nombre de gérants assignés au manager 2: 0
[18-Jul-2025 20:13:23 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:13:23 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:13:23 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:14:40 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:14:40 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:14:40 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:18:10 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752862690845[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:20:41 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752862841160[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /api/gerants.php?_t=1752862845755[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:20:45 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:28:41 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752863321226[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:28:50 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752863330520[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:28:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:28:58 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[18-Jul-2025 20:29:03 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[18-Jul-2025 20:30:02 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:36:04 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:36:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:36:04 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:36:04 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:36:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:38:22 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 20:38:22 Europe/Berlin] [LOGIN] Méthode non autorisée: HEAD
[18-Jul-2025 20:38:22 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 20:39:34 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 20:39:34 Europe/Berlin] [LOGIN] Méthode non autorisée: HEAD
[18-Jul-2025 20:39:34 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 20:42:07 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752864127220[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752864128881[18-Jul-2025 20:42:08 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:42:11 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:11 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:11 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:11 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:16 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:16 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:16 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:16 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:21 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:21 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:21 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:21 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:25 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:25 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:25 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:25 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:35 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:35 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:35 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:35 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:37 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:37 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:37 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:37 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:39 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:39 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:39 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:39 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:43 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:43 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:43 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:43 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:45 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:42:45 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:42:45 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:42:45 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:42:54 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[18-Jul-2025 20:45:49 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:45:49 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:45:49 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:45:49 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:45:52 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:45:53 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:45:53 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:45:53 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:46:01 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752864361132[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:46:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:46:08 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1752864368387[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[18-Jul-2025 20:46:11 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:46:11 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:46:11 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:46:11 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:54:15 Europe/Berlin] PHP Warning:  Undefined variable $host in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:15 Europe/Berlin] PHP Warning:  Undefined variable $dbname in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:15 Europe/Berlin] PHP Warning:  Undefined variable $username in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:15 Europe/Berlin] PHP Warning:  Undefined variable $password in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:45 Europe/Berlin] PHP Warning:  Undefined variable $host in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:45 Europe/Berlin] PHP Warning:  Undefined variable $dbname in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:45 Europe/Berlin] PHP Warning:  Undefined variable $username in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:54:45 Europe/Berlin] PHP Warning:  Undefined variable $password in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\debug_manager_moulins.php on line 11
[18-Jul-2025 20:55:50 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:55:50 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:55:50 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:56:03 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:56:03 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:56:03 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:56:03 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:57:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:57:04 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:57:04 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:57:51 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:57:51 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:57:51 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:58:38 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:58:38 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:58:38 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:58:48 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:58:48 Europe/Berlin] Recherche des moulins pour le manager ID: 1
[18-Jul-2025 20:58:48 Europe/Berlin] Moulins trouvés pour le manager 1 (relation manager_id): 0
[18-Jul-2025 20:58:48 Europe/Berlin] Aucun moulin trouvé pour le manager 1 - vérification des gérants assignés
[18-Jul-2025 20:58:48 Europe/Berlin] Nombre de gérants assignés au manager 1: 0
[18-Jul-2025 20:58:48 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:59:07 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:59:07 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:59:07 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:59:07 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 20:59:09 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 20:59:09 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 20:59:09 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 20:59:09 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 21:03:07 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 21:03:07 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 21:03:07 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 21:03:07 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 21:03:11 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 21:03:11 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 21:03:11 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 21:03:11 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 21:03:14 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[18-Jul-2025 21:03:14 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[18-Jul-2025 21:03:14 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[18-Jul-2025 21:03:14 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[18-Jul-2025 21:03:49 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1752865429843[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] ID utilisateur: 1
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Rôle: admin
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] User ID: 1
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Username: admin
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Rôle: admin
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[21-Jul-2025 10:28:39 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzA4NjUxOSwiZXhwIjoxNzUzMTcyOTE5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.RJ5krXGIuAJAXnbmP1pfCnZsdYhQveNrvOMyL0XEy_s"}
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1753086524337[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[21-Jul-2025 10:28:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[21-Jul-2025 10:28:50 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1753086530579[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[21-Jul-2025 10:29:00 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[21-Jul-2025 10:29:06 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] ID utilisateur: 1
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Rôle: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] User ID: 1
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Username: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Rôle: admin
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[21-Jul-2025 10:29:55 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzA4NjU5NSwiZXhwIjoxNzUzMTcyOTk1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.XRGSeMJzL-OA-OKD7-fhw3tlxPSjHdYoLpS4_uKOocA"}
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"maneger123"}
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] ID utilisateur: 2
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Rôle: manager
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Mot de passe fourni: maneger123
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Vérification du mot de passe: échouée
[21-Jul-2025 10:30:24 Europe/Berlin] [LOGIN] Échec: Mot de passe incorrect
[2025-07-21 10:30:24] Échec d'authentification pour l'utilisateur 'manager'.
  - Mot de passe fourni: 'maneger123'
  - Hash stocké en base: '$2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie'
  - Résultat de password_verify(): bool(false)
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] ID utilisateur: 2
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Rôle: manager
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] User ID: 2
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Username: manager
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Rôle: manager
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[21-Jul-2025 10:30:34 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzA4NjYzNCwiZXhwIjoxNzUzMTczMDM0LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.p-whn_zP50867LwAH4-XN03P2KcQUuOf3arbtjePYqE"}
[21-Jul-2025 10:30:35 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[21-Jul-2025 10:30:35 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[21-Jul-2025 10:30:35 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[21-Jul-2025 10:30:35 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[21-Jul-2025 10:30:36 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[21-Jul-2025 10:30:36 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[21-Jul-2025 10:30:36 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[21-Jul-2025 10:30:36 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager123"}
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] ID utilisateur: 2
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Mot de passe fourni: manager123
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Vérification du mot de passe: échouée
[22-Jul-2025 23:02:30 Europe/Berlin] [LOGIN] Échec: Mot de passe incorrect
[2025-07-22 23:02:30] Échec d'authentification pour l'utilisateur 'manager'.
  - Mot de passe fourni: 'manager123'
  - Hash stocké en base: '$2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie'
  - Résultat de password_verify(): bool(false)
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager123"}
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] ID utilisateur: 2
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Mot de passe fourni: manager123
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Vérification du mot de passe: échouée
[22-Jul-2025 23:02:32 Europe/Berlin] [LOGIN] Échec: Mot de passe incorrect
[2025-07-22 23:02:32] Échec d'authentification pour l'utilisateur 'manager'.
  - Mot de passe fourni: 'manager123'
  - Hash stocké en base: '$2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie'
  - Résultat de password_verify(): bool(false)
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] ID utilisateur: 2
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] User ID: 2
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Username: manager
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[22-Jul-2025 23:02:43 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzIxODE2MywiZXhwIjoxNzUzMzA0NTYzLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.1Os0VCOf1PTnRmXojA-K8ZQ7xTe85t0AMVhwNijwL1E"}
[22-Jul-2025 23:02:43 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[22-Jul-2025 23:02:43 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[22-Jul-2025 23:02:43 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[22-Jul-2025 23:02:43 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[22-Jul-2025 23:02:44 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[22-Jul-2025 23:02:44 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[22-Jul-2025 23:02:44 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[22-Jul-2025 23:02:44 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[22-Jul-2025 23:14:30 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[22-Jul-2025 23:14:48 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[22-Jul-2025 23:33:48 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager123"}
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] ID utilisateur: 2
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Mot de passe fourni: manager123
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Vérification du mot de passe: échouée
[22-Jul-2025 23:43:04 Europe/Berlin] [LOGIN] Échec: Mot de passe incorrect
[2025-07-22 23:43:04] Échec d'authentification pour l'utilisateur 'manager'.
  - Mot de passe fourni: 'manager123'
  - Hash stocké en base: '$2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie'
  - Résultat de password_verify(): bool(false)
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] ID utilisateur: 2
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] User ID: 2
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Username: manager
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Rôle: manager
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[22-Jul-2025 23:43:18 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzIyMDU5OCwiZXhwIjoxNzUzMzA2OTk4LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.HqLS8pHnlA26MAC5XcSRI46v5ugewo_tvBgwqacnKpQ"}
[22-Jul-2025 23:43:18 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[22-Jul-2025 23:43:18 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[22-Jul-2025 23:43:18 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[22-Jul-2025 23:43:18 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[22-Jul-2025 23:43:20 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[22-Jul-2025 23:43:20 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[22-Jul-2025 23:43:20 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[22-Jul-2025 23:43:20 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[22-Jul-2025 23:43:23 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[22-Jul-2025 23:46:29 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[22-Jul-2025 23:46:29 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[22-Jul-2025 23:46:29 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[22-Jul-2025 23:46:29 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[22-Jul-2025 23:46:32 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[22-Jul-2025 23:46:33 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[22-Jul-2025 23:46:33 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[22-Jul-2025 23:46:33 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[23-Jul-2025 10:37:58 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[23-Jul-2025 10:37:58 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[23-Jul-2025 10:37:58 Europe/Berlin] JWT Validation Error: Expired token
[23-Jul-2025 10:37:58 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[23-Jul-2025 10:37:58 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] ID utilisateur: 1
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Rôle: admin
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] User ID: 1
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Username: admin
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Rôle: admin
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[23-Jul-2025 10:38:13 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzI1OTg5MywiZXhwIjoxNzUzMzQ2MjkzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.R8Mz77rOZ_Mu5ZTBC6pqUYeUgfkdzl4tjptVdl3_xQs"}
[23-Jul-2025 10:38:18 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1753259898705[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[23-Jul-2025 10:38:20 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1753259900328[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[23-Jul-2025 10:38:24 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[23-Jul-2025 10:38:24 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] ID utilisateur: 2
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Rôle: manager
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] User ID: 2
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Username: manager
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Rôle: manager
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[23-Jul-2025 10:38:42 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzI1OTkyMiwiZXhwIjoxNzUzMzQ2MzIyLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.4wg6sLxo2iGQgmDXFABUQl-rh9BRyvFY13pTpLdBKEQ"}
[23-Jul-2025 10:38:42 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[23-Jul-2025 10:38:42 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[23-Jul-2025 10:38:42 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[23-Jul-2025 10:38:42 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[23-Jul-2025 10:38:43 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[23-Jul-2025 10:38:43 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[23-Jul-2025 10:38:43 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[23-Jul-2025 10:38:43 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] ID utilisateur: 1
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Rôle: admin
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] User ID: 1
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Username: admin
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Rôle: admin
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[26-Jul-2025 20:51:02 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzU1NTg2MiwiZXhwIjoxNzUzNjQyMjYyLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.DBVX6qzTUZs7ueoAxqFLxdQlRzstm_RD8wnkMQqUdxU"}
[26-Jul-2025 20:51:12 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1753555872235[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[26-Jul-2025 20:51:12 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1753555872945[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[26-Jul-2025 20:51:16 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[26-Jul-2025 20:51:16 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] ID utilisateur: 1
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] Rôle: admin
[27-Jul-2025 10:03:03 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] User ID: 1
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Username: admin
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Rôle: admin
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[27-Jul-2025 10:03:04 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzYwMzM4NCwiZXhwIjoxNzUzNjg5Nzg0LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.ql-RZ1lD9G_3WN4rrmsO_qABAToqNssKU09F3HF3qN8"}
[27-Jul-2025 11:19:47 Europe/Berlin] PHP Warning:  mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\lib\mail_sender.php on line 109
[27-Jul-2025 11:19:47 Europe/Berlin] [2025-07-27 11:19:47] [error] Échec de l'envoi d'email (PHP mail) | Context: {"to":"<EMAIL>","subject":"R\u00e9initialisation de votre mot de passe","from":"<EMAIL>"}

[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] ID utilisateur: 1
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] User ID: 1
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Username: admin
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 19:31:41 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyMzkwMSwiZXhwIjoxNzUzODEwMzAxLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.dme8mWITb4-bBCLdf2a790U6cQHaH7Z4JriuLmJXOMs"}
[28-Jul-2025 19:33:00 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1753723980717[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[28-Jul-2025 19:33:12 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1753723992585[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[28-Jul-2025 19:33:18 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[28-Jul-2025 19:33:18 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 19:45:44 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] ID utilisateur: 1
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] User ID: 1
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Username: admin
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 19:45:45 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyNDc0NSwiZXhwIjoxNzUzODExMTQ1LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.gaRyoOqdJZD7Wl2jbh7S6N9cdCQQvYsSRous3LObK38"}
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] ID utilisateur: 1
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] User ID: 1
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Username: admin
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 21:04:03 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyOTQ0MywiZXhwIjoxNzUzODE1ODQzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.gsyyS_H8Ah580ZAn4FdqCLmbpj7ZjlFTpsJUdI8KOcU"}
[28-Jul-2025 21:04:10 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?statut=Actif&_t=1753729450264[GERANT] Method: GET, Gerant ID: [GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[GERANT] REQUEST_URI: /Gestion_moulin_wifiZone_ok/api/gerants.php?_t=1753729454641[GERANT] Method: GET, Gerant ID: in] [moulins.php] Bypassing authentication for GET request
[GERANT] Nombre de gérants retournés: 6[GERANT] Gérant 0 - ID: 7, Nom: DMA Groupe[GERANT] Gérant 1 - ID: 4, Nom: JOJO Gilles[GERANT] Gérant 2 - ID: 2, Nom: MALIK Magnim[GERANT] Gérant 3 - ID: 3, Nom: MALOUM Azir[GERANT] Gérant 4 - ID: 6, Nom: Maman Rita[GERANT] Gérant 5 - ID: 5, Nom: MOUSSA Oumandé[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] ID utilisateur: 2
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Rôle: manager
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] User ID: 2
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Username: manager
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Rôle: manager
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 21:05:31 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzcyOTUzMSwiZXhwIjoxNzUzODE1OTMxLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.5Mi2fUjS_Q_nVVNLtY5mEdNbyeHH6XtBdInmqGUpOGw"}
[28-Jul-2025 21:05:31 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:05:31 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:05:31 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:05:31 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:05:32 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:05:32 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:05:32 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:05:32 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:11:07 Europe/Berlin] PHP Fatal error:  Uncaught Error: Call to undefined function getPDO() in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\check_manager_relation.php:11
Stack trace:
#0 {main}
  thrown in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\check_manager_relation.php on line 11
[28-Jul-2025 21:11:07 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:11:07 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:11:07 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:11:10 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:11:10 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:11:10 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:11:40 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:11:40 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:11:40 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:18:47 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:18:47 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:18:47 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:18:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:18:55 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:18:55 Europe/Berlin] PHP Fatal error:  Uncaught Error: Call to undefined function getPDO() in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\check_manager_relation.php:11
Stack trace:
#0 {main}
  thrown in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\check_manager_relation.php on line 11
[28-Jul-2025 21:18:55 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] ID utilisateur: 1
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] User ID: 1
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Username: admin
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Rôle: admin
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 21:31:33 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTA5MywiZXhwIjoxNzUzODE3NDkzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.ZdN2K7FXzxH9ZdXb7fvYmp1bBwgyqWesVDo6APTlLzw"}
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] ID utilisateur: 2
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Rôle: manager
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] User ID: 2
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Username: manager
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Rôle: manager
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 21:32:11 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTEzMSwiZXhwIjoxNzUzODE3NTMxLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.2TfUNK3t3QlplUIIMxGjOBcMOAhjwhFQrHQwvCxv7tk"}
[28-Jul-2025 21:32:11 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:32:11 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:32:11 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:32:11 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:32:13 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:32:13 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:32:13 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:32:13 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:33:14 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:33:14 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:33:14 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:34:30 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:34:30 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:34:30 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:34:30 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:36:10 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:36:10 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:36:10 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] ID utilisateur: 2
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Rôle: manager
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] User ID: 2
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Username: manager
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Rôle: manager
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[28-Jul-2025 21:36:52 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1MzczMTQxMiwiZXhwIjoxNzUzODE3ODEyLCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.9zAEJBL95kQhsOroVqEyXvYlnF2V-Rq4Rrzooc6eHIc"}
[28-Jul-2025 21:36:52 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:36:52 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:36:52 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:36:52 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:36:54 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:36:54 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:36:54 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:36:54 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:39:42 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:39:42 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:39:42 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:39:42 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:40:23 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:40:23 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:40:23 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:40:29 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:40:29 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:40:29 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:40:34 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:40:34 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:40:34 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:40:38 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:40:39 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:40:54 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:40:54 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:40:54 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:40:54 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:42:04 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:42:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:42:04 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:42:04 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:42:07 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:42:07 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:42:07 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:44:29 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:44:31 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:44:31 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:44:31 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:44:38 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:44:38 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:44:38 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:44:38 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[28-Jul-2025 21:44:39 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[28-Jul-2025 21:44:39 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[28-Jul-2025 21:44:39 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[28-Jul-2025 21:44:39 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Données reçues: {"username":"manager","password":"manager"}
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: manager
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] ID utilisateur: 2
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Rôle: manager
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$MaQ/K7uEnJOduxhwPTmed.aCoZyjD0Jo56qWzzCT/5p3giKHTs.Ie
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Mot de passe fourni: manager
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: manager
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] User ID: 2
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Username: manager
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Rôle: manager
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 10:57:59 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":2,"username":"manager","role":"manager"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg2NTg3OSwiZXhwIjoxNzUzOTUyMjc5LCJkYXRhIjp7ImlkIjoyLCJ1c2VybmFtZSI6Im1hbmFnZXIiLCJyb2xlIjoibWFuYWdlciJ9fQ.hGQ-AIW772CchHiYow-f3DeCD_3ZEE854UEF1HkPh1A"}
[30-Jul-2025 10:58:00 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 10:58:00 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 10:58:00 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 10:58:00 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 10:58:02 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 10:58:02 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 10:58:02 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 10:58:02 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 10:58:56 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[30-Jul-2025 11:03:37 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[30-Jul-2025 11:03:51 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[30-Jul-2025 11:27:01 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 11:27:01 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 11:30:00 Europe/Berlin] PHP Warning:  Undefined variable $action in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[30-Jul-2025 11:47:18 Europe/Berlin] Erreur GET transferts: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'type' in where clause is ambiguous
[30-Jul-2025 11:49:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 3
[30-Jul-2025 11:49:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 4
[30-Jul-2025 11:49:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 5
[30-Jul-2025 11:49:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 6
[30-Jul-2025 11:49:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 7
[30-Jul-2025 11:49:46 Europe/Berlin] Erreur GET transferts: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'type' in where clause is ambiguous
[30-Jul-2025 11:49:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 11:52:17 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 3
[30-Jul-2025 11:52:17 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 4
[30-Jul-2025 11:52:17 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 5
[30-Jul-2025 11:52:17 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 6
[30-Jul-2025 11:52:17 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 7
[30-Jul-2025 11:52:17 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_transferts_filters.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 12:33:38 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 3
[30-Jul-2025 12:33:38 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 4
[30-Jul-2025 12:33:38 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 5
[30-Jul-2025 12:33:38 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 6
[30-Jul-2025 12:33:38 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 7
[30-Jul-2025 12:33:38 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 12:36:23 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 3
[30-Jul-2025 12:36:23 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 4
[30-Jul-2025 12:36:23 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 5
[30-Jul-2025 12:36:23 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 6
[30-Jul-2025 12:36:23 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 7
[30-Jul-2025 12:36:23 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\test_services_api.php:6) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 12:44:50 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:44:50 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:45:41 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:45:54 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:46:14 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:46:27 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:50:04 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:50:04 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 12:54:42 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 13:07:07 Europe/Berlin] Transfert à créer - Montant: 30000, Frais: 0, Taux: 1, Montant à recevoir: 30000
[30-Jul-2025 13:07:07 Europe/Berlin] Erreur POST transferts: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'expediteur_nom' in 'field list'
[30-Jul-2025 13:07:07 Europe/Berlin] Transfert à créer - Montant: 50000, Frais: 2500, Taux: 0.00152, Montant à recevoir: 72.2
[30-Jul-2025 13:07:07 Europe/Berlin] Erreur POST transferts: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'expediteur_nom' in 'field list'
[30-Jul-2025 13:07:55 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 34
[30-Jul-2025 13:07:55 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 43
[30-Jul-2025 13:07:55 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 13:07:55 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php:10) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 13:09:56 Europe/Berlin] Transfert à créer - Montant: 30000, Frais: 0, Taux: 1, Montant à recevoir: 30000
[30-Jul-2025 13:09:56 Europe/Berlin] Erreur POST transferts: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'expediteur_nom' in 'field list'
[30-Jul-2025 13:09:56 Europe/Berlin] Transfert à créer - Montant: 50000, Frais: 2500, Taux: 0.00152, Montant à recevoir: 72.2
[30-Jul-2025 13:09:56 Europe/Berlin] Erreur POST transferts: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'expediteur_nom' in 'field list'
[30-Jul-2025 13:29:17 Europe/Berlin] Transfert à créer - Montant: 30000, Frais: 0, Taux: 1, Montant à recevoir: 30000
[30-Jul-2025 13:29:17 Europe/Berlin] Transfert à créer - Montant: 50000, Frais: 2500, Taux: 0.00152, Montant à recevoir: 72.2
[30-Jul-2025 13:43:54 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 13:44:38 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 13:53:44 Europe/Berlin] Transfert à créer - Montant: 30000, Frais: 500, Taux: 1, Montant à recevoir: 29500
[30-Jul-2025 13:53:45 Europe/Berlin] Transfert à créer - Montant: 50000, Frais: 2500, Taux: 0.00152, Montant à recevoir: 72.2
[30-Jul-2025 13:55:42 Europe/Berlin] Authentification échouée: Pas d'en-tête d'authentification
[30-Jul-2025 13:57:59 Europe/Berlin] Transfert à créer - Montant: 25000, Frais: 250, Taux: 1, Montant à recevoir: 24750
[30-Jul-2025 14:01:36 Europe/Berlin] Transfert à créer - Montant: 30000, Frais: 500, Taux: 1, Montant à recevoir: 29500
[30-Jul-2025 14:02:04 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 14:02:04 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 14:02:04 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 14:02:04 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] User ID: 1
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Username: admin
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 14:02:29 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg3Njk0OSwiZXhwIjoxNzUzOTYzMzQ5LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.b13WgFy3ZCE2zgS6jSoAcdzvo3JnAeyQJHhWSolBXP8"}
[30-Jul-2025 14:02:33 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:03:00 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:03:11 Europe/Berlin] [JWT_AUTH] Aucun en-tête Authorization trouvé
[30-Jul-2025 14:03:11 Europe/Berlin] [JWT_AUTH] En-têtes disponibles: ["host","connection","sec-ch-ua-platform","user-agent","sec-ch-ua","sec-ch-ua-mobile","accept","sec-fetch-site","sec-fetch-mode","sec-fetch-dest","referer","accept-encoding","accept-language","cookie"]
[30-Jul-2025 14:03:11 Europe/Berlin] [JWT_AUTH] Aucun token trouvé dans les en-têtes
[30-Jul-2025 14:03:11 Europe/Berlin] [JWT_AUTH] Aucun en-tête Authorization trouvé
[30-Jul-2025 14:03:11 Europe/Berlin] [JWT_AUTH] En-têtes disponibles: ["host","connection","sec-ch-ua-platform","user-agent","sec-ch-ua","sec-ch-ua-mobile","accept","sec-fetch-site","sec-fetch-mode","sec-fetch-dest","referer","accept-encoding","accept-language","cookie"]
[30-Jul-2025 14:03:11 Europe/Berlin] [JWT_AUTH] Aucun token pour vérification admin
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:03:14 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:21:08 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:12 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:22:35 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:03 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:35 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:23:59 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:24:17 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:24:46 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'FLOOZ': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'FLOOZ': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'FLOOZ': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'FLOOZ': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'FLOOZ': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'FLOOZ': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:29:17 Europe/Berlin] Erreur lors de la récupération du service 'MIXX': SQLSTATE[42S22]: Column not found: 1054 Unknown column 'code' in 'where clause'
[30-Jul-2025 14:48:42 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 14:48:42 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 14:48:42 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 14:48:42 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 14:50:28 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 34
[30-Jul-2025 14:50:28 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 41
[30-Jul-2025 14:50:28 Europe/Berlin] PHP Warning:  Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\transferts.php on line 59
[30-Jul-2025 14:50:28 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\test_simple_quote.php:16) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 14:51:47 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\test_simple_quote.php:17) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 14:53:53 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\test_simple_quote.php:17) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 14:55:46 Europe/Berlin] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\test_simple_quote.php:17) in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\cors.php on line 53
[30-Jul-2025 14:59:27 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 14:59:30 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:00 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:02:19 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:06 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:27 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:07:43 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:08:02 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 15:08:02 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:08:02 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 15:08:02 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] User ID: 1
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Username: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 15:10:00 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MTAwMCwiZXhwIjoxNzUzOTY3NDAwLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.fDItBRvq8IT9BH8Ga3YwQTr1oYBBbu_WddHD-TdBaec"}
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Aucun en-tête Authorization trouvé
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] En-têtes disponibles: ["Host","Accept","Content-Type"]
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Aucun token trouvé dans les en-têtes
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Aucun en-tête Authorization trouvé
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] En-têtes disponibles: ["Host","Accept","Content-Type"]
[30-Jul-2025 15:10:00 Europe/Berlin] [JWT_AUTH] Aucun token pour vérification admin
[30-Jul-2025 15:15:08 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:15:09 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:16:01 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:16:01 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:16:01 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:16:01 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:18:46 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:19:53 Europe/Berlin] [JWT_AUTH] Token extrait: test_token...
[30-Jul-2025 15:19:53 Europe/Berlin] [JWT_AUTH] Erreur de décodage du token: Wrong number of segments
[30-Jul-2025 15:19:53 Europe/Berlin] [JWT_AUTH] Token extrait: test_token...
[30-Jul-2025 15:19:53 Europe/Berlin] [JWT_AUTH] Erreur lors de la vérification admin: Wrong number of segments
[30-Jul-2025 15:21:40 Europe/Berlin] PHP Fatal error:  Uncaught Error: Call to undefined function getallheaders() in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\debug_token.php:15
Stack trace:
#0 {main}
  thrown in C:\xampp\htdocs\Gestion_moulin_wifiZone_ok\api\debug_token.php on line 15
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:21:59 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:22:13 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 15:22:13 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:22:13 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 15:22:13 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin123"}
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:25:57 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Mot de passe fourni: admin123
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Vérification du mot de passe: échouée
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Échec: Mot de passe incorrect
[2025-07-30 15:25:58] Échec d'authentification pour l'utilisateur 'admin'.
  - Mot de passe fourni: 'admin123'
  - Hash stocké en base: '$2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy'
  - Résultat de password_verify(): bool(false)
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] User ID: 1
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Username: admin
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 15:25:58 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MTk1OCwiZXhwIjoxNzUzOTY4MzU4LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.b-9egYEiZ0zJStvOZJ5NgC98ATopOsYDNBejE_SK46A"}
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] User ID: 1
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Username: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 15:26:37 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MTk5NywiZXhwIjoxNzUzOTY4Mzk3LCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.K3uQteVYarxQbyrnXxeQQV0YMh2aoXaATrb71C29BlE"}
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:26:37 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] User ID: 1
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Username: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 15:28:31 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MjExMSwiZXhwIjoxNzUzOTY4NTExLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.9WjkuRKwq5FwhFDp7ce4TA703XvpqHanH7DYS6NnK1s"}
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:31 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:28:53 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Données reçues: {"username":"admin","password":"admin"}
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Tentative de connexion pour l'utilisateur: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Connexion à la base de données réussie
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Recherche de l'utilisateur: trouvé
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] ID utilisateur: 1
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Hash stocké: $2y$10$0qGg/OqKKeVFmJhABdzrGeLnog/.GPwJF9Aa/0feW.E6LtdB5PjQy
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Mot de passe fourni: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Vérification du mot de passe: réussie
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Rôle défini dans la base de données: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Utilisateur reconnu comme administrateur par son nom d'utilisateur ou email
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Connexion réussie! Token JWT généré:
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] User ID: 1
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Username: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Rôle: admin
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Token (premiers 20 caractères): eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Préparation de l'envoi de la réponse JSON:
[30-Jul-2025 15:30:23 Europe/Berlin] [LOGIN] Réponse à envoyer: {"success":true,"user":{"id":1,"username":"admin","role":"admin"},"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0IiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdCIsImlhdCI6MTc1Mzg4MjIyMywiZXhwIjoxNzUzOTY4NjIzLCJkYXRhIjp7ImlkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn19.yXmnciHyKwX8hFNUWWow0KQO8t79Hw997gIZykvnMQ8"}
[30-Jul-2025 15:30:27 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:30:27 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:30:27 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:30:27 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:30:51 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:31:44 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:32:19 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:35:55 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:36:15 Europe/Berlin] Aucun ticket trouvé pour le manager ID: 2
[30-Jul-2025 15:36:15 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:36:15 Europe/Berlin] Recherche des moulins pour le manager ID: 2
[30-Jul-2025 15:36:15 Europe/Berlin] Moulins trouvés pour le manager 2 (relation manager_id): 5
[30-Jul-2025 15:36:45 Europe/Berlin] [moulins.php] Bypassing authentication for GET request
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Token décodé avec succès - User ID: 1
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Token extrait: eyJ0eXAiOiJKV1QiLCJh...
[30-Jul-2025 15:36:46 Europe/Berlin] [JWT_AUTH] Vérification admin - User ID: 1, Role: admin
