<?php
/**
 * Debug de l'API transferts
 */

echo "DEBUG API TRANSFERTS\n";
echo "====================\n\n";

// Test de syntaxe PHP
echo "1. Test de syntaxe PHP...\n";
$syntax_check = shell_exec('php -l api/transferts.php 2>&1');
echo "Résultat: " . $syntax_check . "\n";

// Test de connexion à la base de données
echo "2. Test de connexion à la base de données...\n";
try {
    require_once 'api/config.php';
    echo "✅ Connexion à la base de données OK\n";
    
    // Vérifier la table transferts
    $stmt = $pdo->query("DESCRIBE transferts");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "   Colonnes de la table transferts: " . implode(', ', $columns) . "\n";
    
} catch (Exception $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n";
}

// Test de la classe TransferService
echo "\n3. Test de la classe TransferService...\n";
try {
    require_once 'api/services/transfer_service.php';
    $transferService = new TransferService();
    echo "✅ Classe TransferService chargée OK\n";
    
    // Test de calcul de frais
    $frais = $transferService->calculateFees('MIXX by Yass', 30000);
    echo "   Frais calculés pour 30000 XOF via MIXX: " . $frais . " XOF\n";
    
} catch (Exception $e) {
    echo "❌ Erreur TransferService: " . $e->getMessage() . "\n";
}

// Test de la table services_transfert
echo "\n4. Test de la table services_transfert...\n";
try {
    $stmt = $pdo->prepare("SELECT id, nom FROM services_transfert WHERE actif = 1 LIMIT 3");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($services) {
        echo "✅ Services disponibles:\n";
        foreach ($services as $service) {
            echo "   - ID: " . $service['id'] . ", Nom: " . $service['nom'] . "\n";
        }
    } else {
        echo "❌ Aucun service trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur services: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Debug terminé\n";
?>
