<?php
/**
 * Test de création de transfert avec type 'externe'
 */

echo "TEST CRÉATION TRANSFERT TYPE 'EXTERNE'\n";
echo "=====================================\n\n";

// Test avec type 'externe'
echo "1. Test de création avec type 'externe'...\n";
$url = "http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php";
$data = [
    'type' => 'externe',
    'montant' => 25000,
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF',
    'service' => '1', // ID du service MIXX
    'notes' => 'Test transfert externe',
    'expediteur' => [
        'nom' => 'TRAORE',
        'prenom' => 'Mamadou',
        'telephone' => '22890123456'
    ],
    'destinataire' => [
        'nom' => 'KONE',
        'prenom' => 'Fatou',
        'telephone' => '71234567'
    ],
    'frais' => 250,
    'taux_change' => 1,
    'montant_a_recevoir' => 24750
];

echo "URL: $url\n";
echo "Données: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data),
        'timeout' => 15
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response !== false) {
    $result = json_decode($response, true);
    
    if ($result && $result['success']) {
        echo "✅ Transfert externe créé avec succès !\n";
        echo "   ID: " . $result['data']['id'] . "\n";
        echo "   Référence: " . $result['data']['reference'] . "\n";
        echo "   Montant: " . $result['data']['montant'] . " XOF\n";
        echo "   Frais: " . $result['data']['frais'] . " XOF\n";
        echo "   Taux de change: " . $result['data']['taux_change'] . "\n";
        echo "   Montant à recevoir: " . $result['data']['montant_a_recevoir'] . " XOF\n";
    } else {
        echo "❌ Erreur création: " . ($result['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse complète: " . substr($response, 0, 1000) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API\n";
    $error = error_get_last();
    echo "   Erreur: " . ($error['message'] ?? 'Erreur inconnue') . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
