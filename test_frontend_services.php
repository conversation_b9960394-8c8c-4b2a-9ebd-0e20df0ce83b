<?php
/**
 * Test de l'appel services comme le fait le frontend
 */

echo "TEST APPEL SERVICES FRONTEND\n";
echo "============================\n\n";

// Test 1: Appel direct comme le frontend
echo "1. Test appel direct (comme CreateExternalTransferForm)...\n";
$url = "http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?action=services";
echo "URL: $url\n";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'timeout' => 10
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response !== false) {
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "✅ Appel services OK - " . count($data['data']) . " services\n";
        echo "   Services disponibles:\n";
        foreach ($data['data'] as $service) {
            echo "   - ID: {$service['id']}, Nom: {$service['nom']}, Type: {$service['type']}\n";
        }
        
        echo "\n   Structure complète du premier service:\n";
        if (!empty($data['data'])) {
            print_r($data['data'][0]);
        }
    } else {
        echo "❌ Erreur services: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response, 0, 500) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API\n";
    $error = error_get_last();
    echo "   Erreur: " . ($error['message'] ?? 'Erreur inconnue') . "\n";
}

echo "\n";

// Test 2: Test avec proxy simulation (port 8080)
echo "2. Test avec simulation proxy (port 8080)...\n";
$url = "http://localhost:8080/api/transferts.php?action=services";
echo "URL: $url\n";

$response = file_get_contents($url, false, $context);

if ($response !== false) {
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "✅ Proxy OK - " . count($data['data']) . " services\n";
    } else {
        echo "❌ Erreur proxy: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response, 0, 300) . "\n";
    }
} else {
    echo "❌ Proxy non accessible\n";
    $error = error_get_last();
    echo "   Erreur: " . ($error['message'] ?? 'Erreur inconnue') . "\n";
}

echo "\n";

// Test 3: Test avec cURL (plus proche du comportement fetch)
echo "3. Test avec cURL (simulation fetch)...\n";
$url = "http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?action=services";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "Code HTTP: $httpCode\n";

if ($response !== false && empty($error)) {
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "✅ cURL OK - " . count($data['data']) . " services\n";
        
        // Vérifier la structure attendue par le frontend
        $firstService = $data['data'][0] ?? null;
        if ($firstService) {
            $requiredFields = ['id', 'nom', 'type', 'description'];
            $missingFields = [];
            
            foreach ($requiredFields as $field) {
                if (!isset($firstService[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (empty($missingFields)) {
                echo "   ✅ Structure OK - tous les champs requis présents\n";
            } else {
                echo "   ⚠️  Champs manquants: " . implode(', ', $missingFields) . "\n";
            }
        }
    } else {
        echo "❌ Erreur cURL: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response, 0, 300) . "\n";
    }
} else {
    echo "❌ Erreur cURL: $error\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
