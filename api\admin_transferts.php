<?php

// Utiliser le système CORS centralisé
require_once __DIR__ . '/cors.php';

require_once 'config.php';
require_once 'services/transfer_service.php';
require_once 'middleware/jwt_auth_middleware.php';

// Logger les requêtes pour le débogage
$log_file = __DIR__ . '/debug_admin_transferts.log';
$log_message = date('Y-m-d H:i:s') . ' - IP: ' . $_SERVER['REMOTE_ADDR'] . 
               ' - Method: ' . $_SERVER['REQUEST_METHOD'] . 
               ' - URI: ' . $_SERVER['REQUEST_URI'];

// Récupérer le token d'authentification pour le débogage
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : 'Non fourni';
$token_debug = substr(str_replace('Bearer ', '', $authHeader), 0, 15) . '...';
$log_message .= ' - Token: ' . $token_debug . PHP_EOL;

file_put_contents($log_file, $log_message, FILE_APPEND);

// Headers pour l'API avec CORS complet
// Retiré: // Retiré: // Commenté pour éviter duplication: // En-tête CORS géré par cors.php
// Retiré: // Retiré: // Commenté pour éviter duplication: header('Access-Control-Allow-Credentials: true');
// Commenté pour éviter duplication: // Commenté pour éviter duplication: header('Content-Type: application/json; charset=UTF-8');
// Retiré: // Retiré: // Commenté pour éviter duplication: header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS');
// Retiré: // Retiré: // Commenté pour éviter duplication: header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

// Gestion des requêtes OPTIONS (CORS pre-flight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Vérifier si l'utilisateur est un administrateur
$authMiddleware = new JWTAuthMiddleware();
$is_authenticated = $authMiddleware->isAuthenticated();
$is_admin = $authMiddleware->isAdmin();

// Journaliser le résultat de l'authentification
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Auth: " . ($is_authenticated ? 'Oui' : 'Non') . 
                  " - Admin: " . ($is_admin ? 'Oui' : 'Non') . PHP_EOL, FILE_APPEND);

// Vérifier les droits d'admin
if (!$is_authenticated) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Authentification requise.',
        'status' => 'auth_required'
    ]);
    exit;
} else if (!$is_admin) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Accès refusé. Droits administrateur requis.',
        'status' => 'admin_required'
    ]);
    exit;
}

// Récupérer la méthode HTTP
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : null;

// Gestionnaire de transfert
$transferService = new TransferService();

// Logger les requêtes pour le débogage
$log_file = __DIR__ . '/debug_admin_transferts.log';
file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Method: ' . $method . ' - Action: ' . $action . PHP_EOL, FILE_APPEND);

// ============== GESTION DES SERVICES DE TRANSFERT =============
if ($action === 'services') {
    $service_id = isset($_GET['id']) ? $_GET['id'] : null;
    
    switch ($method) {
        case 'GET':
            // Récupérer tous les services ou un service spécifique
            try {
                if ($service_id) {
                    $stmt = $pdo->prepare("
                        SELECT * FROM services_transfert WHERE id = ?
                    ");
                    $stmt->execute([$service_id]);
                    $service = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$service) {
                        http_response_code(404);
                        echo json_encode(['success' => false, 'message' => 'Service non trouvé']);
                        exit;
                    }
                    
                    echo json_encode(['success' => true, 'data' => $service]);
                } else {
                    $stmt = $pdo->prepare("
                        SELECT * FROM services_transfert ORDER BY nom ASC
                    ");
                    $stmt->execute();
                    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo json_encode(['success' => true, 'data' => $services]);
                }
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de récupération des services: ' . $e->getMessage()]);
            }
            break;
            
        case 'POST':
            // Créer un nouveau service
            $data = json_decode(file_get_contents('php://input'), true);
            
            // Vérifier les champs requis
            $requiredFields = ['code', 'nom', 'type'];
            $missingFields = [];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false, 
                    'message' => 'Champs obligatoires manquants: ' . implode(', ', $missingFields)
                ]);
                exit;
            }
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO services_transfert (
                        nom, type, description, commission_fixe, commission_pourcentage,
                        commission_envoi_fixe, commission_envoi_pourcentage,
                        commission_retrait_fixe, commission_retrait_pourcentage,
                        actif
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $data['nom'],
                    $data['type'] ?? 'Standard',
                    $data['description'] ?? null,
                    $data['commission_fixe'] ?? 0,
                    $data['commission_pourcentage'] ?? 0,
                    $data['commission_envoi_fixe'] ?? ($data['commission_envoi_fixe'] ?? $data['commission_fixe'] ?? 0),
                    $data['commission_envoi_pourcentage'] ?? ($data['commission_envoi_pourcentage'] ?? $data['commission_pourcentage'] ?? 0),
                    $data['commission_retrait_fixe'] ?? ($data['commission_retrait_fixe'] ?? ($data['commission_fixe'] ?? 0) * 0.8),
                    $data['commission_retrait_pourcentage'] ?? ($data['commission_retrait_pourcentage'] ?? ($data['commission_pourcentage'] ?? 0) * 0.8),
                    $data['actif'] ?? 1
                ]);
                
                $newId = $pdo->lastInsertId();
                
                // Récupérer le service créé
                $stmt = $pdo->prepare("SELECT * FROM services_transfert WHERE id = ?");
                $stmt->execute([$newId]);
                $newService = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Service de transfert créé avec succès',
                    'data' => $newService
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de création du service: ' . $e->getMessage()]);
            }
            break;
            
        case 'PUT':
            // Mettre à jour un service existant
            if (!$service_id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID du service requis']);
                exit;
            }
            
            $data = json_decode(file_get_contents('php://input'), true);

            // Debug: Log des données reçues
            file_put_contents($log_file, date('Y-m-d H:i:s') . ' - PUT Service ID: ' . $service_id . ' - Data: ' . json_encode($data) . PHP_EOL, FILE_APPEND);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Données JSON invalides']);
                exit;
            }

            try {
                // Vérifier si le service existe
                $stmt = $pdo->prepare("SELECT id FROM services_transfert WHERE id = ?");
                $stmt->execute([$service_id]);
                if (!$stmt->fetch()) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Service non trouvé']);
                    exit;
                }
                
                // Construire la requête de mise à jour dynamiquement
                $updateFields = [];
                $params = [];
                
                $allowedFields = [
                    'nom', 'type', 'description', 'commission_fixe',
                    'commission_pourcentage', 'commission_envoi_fixe', 'commission_envoi_pourcentage',
                    'commission_retrait_fixe', 'commission_retrait_pourcentage',
                    'devise_principale', 'devises_supportees', 'actif'
                ];
                
                foreach ($allowedFields as $field) {
                    if (isset($data[$field])) {
                        $updateFields[] = "$field = ?";
                        $params[] = $data[$field];
                    }
                }
                
                if (empty($updateFields)) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Aucun champ à mettre à jour']);
                    exit;
                }
                
                // Ajouter l'ID à la fin des paramètres
                $params[] = $service_id;
                
                $sql = "UPDATE services_transfert SET " . implode(', ', $updateFields) . " WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                
                // Récupérer le service mis à jour
                $stmt = $pdo->prepare("SELECT * FROM services_transfert WHERE id = ?");
                $stmt->execute([$service_id]);
                $updatedService = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Service mis à jour avec succès',
                    'data' => $updatedService
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de mise à jour du service: ' . $e->getMessage()]);
            }
            break;
            
        case 'DELETE':
            // Supprimer un service
            if (!$service_id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID du service requis']);
                exit;
            }
            
            try {
                // Vérifier si le service existe
                $stmt = $pdo->prepare("SELECT id FROM services_transfert WHERE id = ?");
                $stmt->execute([$service_id]);
                if (!$stmt->fetch()) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Service non trouvé']);
                    exit;
                }
                
                // Vérifier s'il y a des transferts liés à ce service
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM transferts WHERE service_id = ?");
                $stmt->execute([$service_id]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($result['count'] > 0) {
                    // Au lieu de supprimer, désactiver le service
                    $stmt = $pdo->prepare("UPDATE services_transfert SET actif = 0 WHERE id = ?");
                    $stmt->execute([$service_id]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Le service a été désactivé car il est lié à des transferts existants'
                    ]);
                } else {
                    // Supprimer complètement le service
                    $stmt = $pdo->prepare("DELETE FROM services_transfert WHERE id = ?");
                    $stmt->execute([$service_id]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Service supprimé avec succès'
                    ]);
                }
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de suppression du service: ' . $e->getMessage()]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
            break;
    }
    exit;
}

// ============== GESTION DES TAUX DE CHANGE =============
if ($action === 'taux') {
    $taux_id = isset($_GET['id']) ? $_GET['id'] : null;
    
    switch ($method) {
        case 'GET':
            // Récupérer tous les taux ou un taux spécifique
            try {
                if ($taux_id) {
                    $stmt = $pdo->prepare("
                        SELECT * FROM taux_change WHERE id = ?
                    ");
                    $stmt->execute([$taux_id]);
                    $taux = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$taux) {
                        http_response_code(404);
                        echo json_encode(['success' => false, 'message' => 'Taux de change non trouvé']);
                        exit;
                    }
                    
                    echo json_encode(['success' => true, 'data' => $taux]);
                } else {
                    $stmt = $pdo->prepare("
                        SELECT * FROM taux_change 
                        ORDER BY updated_at DESC, devise_source ASC, devise_cible ASC
                    ");
                    $stmt->execute();
                    $taux = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo json_encode(['success' => true, 'data' => $taux]);
                }
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de récupération des taux: ' . $e->getMessage()]);
            }
            break;
            
        case 'POST':
            // Créer un nouveau taux
            $data = json_decode(file_get_contents('php://input'), true);
            
            // Vérifier les champs requis
            $requiredFields = ['devise_source', 'devise_cible', 'taux', 'date_effet'];
            $missingFields = [];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false, 
                    'message' => 'Champs obligatoires manquants: ' . implode(', ', $missingFields)
                ]);
                exit;
            }
            
            try {
                // Vérifier si un taux existe déjà pour cette paire de devises à cette date
                $stmt = $pdo->prepare("
                    SELECT id FROM taux_change 
                    WHERE devise_source = ? AND devise_cible = ? AND date_effet = ?
                ");
                $stmt->execute([
                    $data['devise_source'],
                    $data['devise_cible'],
                    $data['date_effet']
                ]);
                
                $existingTaux = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($existingTaux) {
                    // Mettre à jour le taux existant
                    $stmt = $pdo->prepare("
                        UPDATE taux_change 
                        SET taux = ?, source = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $data['taux'],
                        $data['source'] ?? null,
                        $existingTaux['id']
                    ]);
                    
                    $newId = $existingTaux['id'];
                    $message = 'Taux de change mis à jour avec succès';
                } else {
                    // Créer un nouveau taux
                    $stmt = $pdo->prepare("
                        INSERT INTO taux_change (
                            devise_source, devise_cible, taux, date_effet, source
                        ) VALUES (?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $data['devise_source'],
                        $data['devise_cible'],
                        $data['taux'],
                        $data['date_effet'],
                        $data['source'] ?? null
                    ]);
                    
                    $newId = $pdo->lastInsertId();
                    $message = 'Taux de change créé avec succès';
                }
                
                // Récupérer le taux créé ou mis à jour
                $stmt = $pdo->prepare("SELECT * FROM taux_change WHERE id = ?");
                $stmt->execute([$newId]);
                $newTaux = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'data' => $newTaux
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de création du taux: ' . $e->getMessage()]);
            }
            break;
            
        case 'PUT':
            // Mettre à jour un taux existant
            if (!$taux_id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID du taux requis']);
                exit;
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            
            try {
                // Vérifier si le taux existe
                $stmt = $pdo->prepare("SELECT id FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                if (!$stmt->fetch()) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Taux de change non trouvé']);
                    exit;
                }
                
                $updateFields = [];
                $params = [];
                
                $allowedFields = ['taux', 'date_effet', 'source'];
                
                foreach ($allowedFields as $field) {
                    if (isset($data[$field])) {
                        $updateFields[] = "$field = ?";
                        $params[] = $data[$field];
                    }
                }
                
                if (empty($updateFields)) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Aucun champ à mettre à jour']);
                    exit;
                }
                
                $params[] = $taux_id;
                
                $sql = "UPDATE taux_change SET " . implode(', ', $updateFields) . " WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                
                // Récupérer le taux mis à jour
                $stmt = $pdo->prepare("SELECT * FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                $updatedTaux = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Taux de change mis à jour avec succès',
                    'data' => $updatedTaux
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de mise à jour du taux: ' . $e->getMessage()]);
            }
            break;
            
        case 'DELETE':
            // Supprimer un taux
            if (!$taux_id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID du taux requis']);
                exit;
            }
            
            try {
                // Vérifier si le taux existe
                $stmt = $pdo->prepare("SELECT id FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                if (!$stmt->fetch()) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Taux de change non trouvé']);
                    exit;
                }
                
                $stmt = $pdo->prepare("DELETE FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Taux de change supprimé avec succès'
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de suppression du taux: ' . $e->getMessage()]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
            break;
    }
    exit;
}

// ============== VALIDATION DES TRANSFERTS =============
if ($action === 'validation') {
    $transfert_id = isset($_GET['id']) ? $_GET['id'] : null;
    
    if (!$transfert_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID du transfert requis']);
        exit;
    }
    
    switch ($method) {
        case 'PUT':
            // Valider ou rejeter un transfert
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($data['statut']) || !in_array($data['statut'], ['valide', 'annule'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Statut invalide. Valeurs acceptées: valide, annule']);
                exit;
            }
            
            try {
                // Vérifier si le transfert existe et son statut actuel
                $stmt = $pdo->prepare("SELECT id, statut FROM transferts WHERE id = ?");
                $stmt->execute([$transfert_id]);
                $transfert = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$transfert) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Transfert non trouvé']);
                    exit;
                }
                
                if ($transfert['statut'] != 'en_attente') {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false, 
                        'message' => 'Seuls les transferts en attente peuvent être validés ou annulés'
                    ]);
                    exit;
                }
                
                // Démarrer une transaction
                $pdo->beginTransaction();
                
                // Mettre à jour le statut du transfert
                $stmt = $pdo->prepare("
                    UPDATE transferts 
                    SET statut = ?, date_validation = NOW(), valide_par = ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $data['statut'],
                    $_SESSION['user_id'],
                    $transfert_id
                ]);
                
                // Enregistrer dans l'historique des statuts
                $stmt = $pdo->prepare("
                    INSERT INTO historique_statut_transfert (
                        transfert_id, statut_precedent, statut_nouveau, 
                        user_id, commentaire
                    ) VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $transfert_id,
                    'en_attente',
                    $data['statut'],
                    $_SESSION['user_id'],
                    $data['commentaire'] ?? null
                ]);
                
                $pdo->commit();
                
                // Récupérer le transfert mis à jour
                $stmt = $pdo->prepare("
                    SELECT t.*, u.nom as agent_nom, u.prenom as agent_prenom,
                           v.nom as validateur_nom, v.prenom as validateur_prenom
                    FROM transferts t
                    LEFT JOIN users u ON t.agent_id = u.id
                    LEFT JOIN users v ON t.valide_par = v.id
                    WHERE t.id = ?
                ");
                $stmt->execute([$transfert_id]);
                $updatedTransfert = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => $data['statut'] == 'valide' ? 'Transfert validé avec succès' : 'Transfert refusé',
                    'data' => $updatedTransfert
                ]);
            } catch (PDOException $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la validation: ' . $e->getMessage()]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
            break;
    }
    exit;
}

// ============== GESTION DES TAUX DE CHANGE =============
if ($action === 'taux') {
    $taux_id = isset($_GET['id']) ? $_GET['id'] : null;
    
    switch ($method) {
        case 'GET':
            // Récupérer tous les taux ou un taux spécifique
            try {
                if ($taux_id) {
                    $stmt = $pdo->prepare("
                        SELECT * FROM taux_change WHERE id = ?
                    ");
                    $stmt->execute([$taux_id]);
                    $taux = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$taux) {
                        http_response_code(404);
                        echo json_encode(['success' => false, 'message' => 'Taux non trouvé']);
                        exit;
                    }
                    
                    echo json_encode(['success' => true, 'data' => $taux]);
                } else {
                    $stmt = $pdo->prepare("
                        SELECT * FROM taux_change 
                        ORDER BY date_effet DESC, devise_source ASC
                    ");
                    $stmt->execute();
                    $taux = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo json_encode(['success' => true, 'data' => $taux]);
                }
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de récupération des taux: ' . $e->getMessage()]);
            }
            break;
            
        case 'POST':
            // Créer un nouveau taux de change
            $data = json_decode(file_get_contents('php://input'), true);
            
            // Vérifier les champs requis
            $requiredFields = ['devise_source', 'devise_cible', 'taux', 'date_effet'];
            $missingFields = [];
            
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false, 
                    'message' => 'Champs obligatoires manquants: ' . implode(', ', $missingFields)
                ]);
                exit;
            }
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO taux_change (
                        devise_source, devise_cible, taux, date_effet, source, actif
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $data['devise_source'],
                    $data['devise_cible'],
                    $data['taux'],
                    $data['date_effet'],
                    $data['source'] ?? 'Manuel',
                    $data['actif'] ?? true
                ]);
                
                $newTauxId = $pdo->lastInsertId();
                
                // Récupérer le nouveau taux créé
                $stmt = $pdo->prepare("SELECT * FROM taux_change WHERE id = ?");
                $stmt->execute([$newTauxId]);
                $newTaux = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Taux de change créé avec succès',
                    'data' => $newTaux
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de création du taux: ' . $e->getMessage()]);
            }
            break;
            
        case 'PUT':
            // Mettre à jour un taux existant
            if (!$taux_id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID du taux requis']);
                exit;
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            
            try {
                // Vérifier si le taux existe
                $stmt = $pdo->prepare("SELECT id FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                if (!$stmt->fetch()) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Taux non trouvé']);
                    exit;
                }
                
                // Construire la requête de mise à jour dynamiquement
                $updateFields = [];
                $params = [];
                
                $allowedFields = ['devise_source', 'devise_cible', 'taux', 'date_effet', 'source', 'actif'];
                
                foreach ($allowedFields as $field) {
                    if (isset($data[$field])) {
                        $updateFields[] = "$field = ?";
                        $params[] = $data[$field];
                    }
                }
                
                if (empty($updateFields)) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Aucun champ à mettre à jour']);
                    exit;
                }
                
                // Ajouter l'ID à la fin des paramètres
                $params[] = $taux_id;
                
                $sql = "UPDATE taux_change SET " . implode(', ', $updateFields) . " WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                
                // Récupérer le taux mis à jour
                $stmt = $pdo->prepare("SELECT * FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                $updatedTaux = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Taux mis à jour avec succès',
                    'data' => $updatedTaux
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de mise à jour du taux: ' . $e->getMessage()]);
            }
            break;
            
        case 'DELETE':
            // Supprimer un taux
            if (!$taux_id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID du taux requis']);
                exit;
            }
            
            try {
                // Vérifier si le taux existe
                $stmt = $pdo->prepare("SELECT id FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                if (!$stmt->fetch()) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Taux non trouvé']);
                    exit;
                }
                
                // Supprimer le taux
                $stmt = $pdo->prepare("DELETE FROM taux_change WHERE id = ?");
                $stmt->execute([$taux_id]);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Taux supprimé avec succès'
                ]);
            } catch (PDOException $e) {
                file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur de suppression du taux: ' . $e->getMessage()]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
            break;
    }
    exit;
}

// ============== STATISTIQUES DES TRANSFERTS =============
if ($action === 'stats') {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
        exit;
    }
    
    try {
        // Statistiques des transferts par statut
        $stmt = $pdo->prepare("
            SELECT statut, COUNT(*) as count, SUM(montant) as montant_total
            FROM transferts
            GROUP BY statut
        ");
        $stmt->execute();
        $stats_status = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Statistiques des transferts par service
        $stmt = $pdo->prepare("
            SELECT s.nom as service, COUNT(*) as count, SUM(t.montant) as montant_total
            FROM transferts t
            JOIN services_transfert s ON t.service_id = s.id
            GROUP BY t.service_id
            ORDER BY count DESC
        ");
        $stmt->execute();
        $stats_service = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Statistiques des transferts par type
        $stmt = $pdo->prepare("
            SELECT type, COUNT(*) as count, SUM(montant) as montant_total
            FROM transferts
            GROUP BY type
        ");
        $stmt->execute();
        $stats_type = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Revenus des commissions
        $stmt = $pdo->prepare("
            SELECT SUM(commission) as total_commission
            FROM transferts
            WHERE statut = 'valide' OR statut = 'complete'
        ");
        $stmt->execute();
        $commissions = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'par_statut' => $stats_status,
                'par_service' => $stats_service,
                'par_type' => $stats_type,
                'commissions' => $commissions
            ]
        ]);
    } catch (PDOException $e) {
        file_put_contents($log_file, date('Y-m-d H:i:s') . ' - Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()]);
    }
    exit;
}

// Si aucune action n'est spécifiée, renvoyer une erreur
http_response_code(400);
echo json_encode(['success' => false, 'message' => 'Action non spécifiée']);
exit;
