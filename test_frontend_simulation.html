<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulation Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; white-space: pre-wrap; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 150px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simulation exacte du Frontend</h1>
        
        <div class="test-section">
            <h3>📋 Formulaire de test</h3>
            <div class="form-group">
                <label>Montant:</label>
                <input type="number" id="montant" value="50000" />
            </div>
            <div class="form-group">
                <label>Service:</label>
                <select id="service">
                    <option value="">-- Chargement --</option>
                </select>
            </div>
            <div class="form-group">
                <label>Devise source:</label>
                <select id="devise_source">
                    <option value="XOF">XOF</option>
                    <option value="EUR">EUR</option>
                    <option value="USD">USD</option>
                </select>
            </div>
            <div class="form-group">
                <label>Devise destination:</label>
                <select id="devise_destination">
                    <option value="XOF">XOF</option>
                    <option value="EUR">EUR</option>
                    <option value="USD">USD</option>
                </select>
            </div>
            <div class="form-group">
                <label>Type opération:</label>
                <select id="operation_type">
                    <option value="envoi">Envoi</option>
                    <option value="retrait">Retrait</option>
                </select>
            </div>
            
            <button onclick="loadServices()">📋 Charger Services</button>
            <button onclick="calculateQuote()">💰 Calculer Devis</button>
            <button onclick="clearResults()">🗑️ Effacer</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Configuration exacte comme dans le frontend
        const getApiBaseUrl = () => {
            if (typeof window === 'undefined') {
                return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            }
            
            if (window.location.port === '8080') {
                return '/api';
            }
            
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            }
            
            const { protocol, hostname } = window.location;
            return `${protocol}//${hostname}/Gestion_moulin_wifiZone_ok/api`;
        };

        const API_BASE_URL = getApiBaseUrl();

        // Fonction findToken exacte comme dans le frontend
        function findToken() {
            const keys = ['token', 'authToken', 'jwt', 'access_token'];
            for (const key of keys) {
                const value = localStorage.getItem(key);
                if (value && value.startsWith('eyJ')) {
                    return value;
                }
            }
            return null;
        }

        function addResult(title, content, type = 'success') {
            const resultsDiv = document.getElementById('results');
            const section = document.createElement('div');
            section.className = `test-section ${type}`;
            section.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>Timestamp: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(section);
        }

        // Simulation exacte de fetchServices du frontend
        async function loadServices() {
            try {
                addResult('🔄 Chargement des services...', 'Début du chargement des services');
                
                const token = findToken();
                console.log('Token pour services:', token ? 'OUI' : 'NON');

                const headers = {
                    'Content-Type': 'application/json',
                };

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const url = `${API_BASE_URL}/transferts.php?action=services`;
                console.log('URL services:', url);
                console.log('Headers services:', headers);

                const response = await fetch(url, {
                    method: 'GET',
                    headers,
                });

                console.log('Status services:', response.status);
                const data = await response.json();
                console.log('Réponse services:', data);

                if (!response.ok) {
                    throw new Error(data.message || 'Erreur lors du chargement des services');
                }

                if (data.success && Array.isArray(data.data)) {
                    // Remplir le select
                    const serviceSelect = document.getElementById('service');
                    serviceSelect.innerHTML = '<option value="">-- Sélectionner un service --</option>';
                    
                    data.data.forEach(service => {
                        const option = document.createElement('option');
                        option.value = service.id;
                        option.textContent = `${service.nom} (${service.type})`;
                        serviceSelect.appendChild(option);
                    });

                    addResult(
                        '✅ Services chargés',
                        `${data.data.length} services trouvés:\n${JSON.stringify(data.data, null, 2)}`
                    );
                } else {
                    throw new Error('Format de réponse incorrect pour les services');
                }
            } catch (error) {
                console.error('Error loading services:', error);
                addResult(
                    '❌ Erreur services',
                    `Erreur: ${error.message}`,
                    'error'
                );
            }
        }

        // Simulation exacte de calculateQuote du frontend
        async function calculateQuote() {
            try {
                const montant = document.getElementById('montant').value;
                const service = document.getElementById('service').value;
                const devise_source = document.getElementById('devise_source').value;
                const devise_destination = document.getElementById('devise_destination').value;
                const operation_type = document.getElementById('operation_type').value;

                if (!service) {
                    throw new Error('Veuillez sélectionner un service');
                }

                if (!montant || isNaN(parseFloat(montant)) || parseFloat(montant) <= 0) {
                    throw new Error('Montant invalide');
                }

                addResult('🔄 Calcul du devis...', 'Début du calcul du devis');

                // Construire l'URL avec les paramètres (exactement comme le frontend)
                const params = new URLSearchParams({
                    action: 'quote',
                    service: service,
                    montant: montant,
                    devise_source: devise_source,
                    devise_destination: devise_destination,
                    operation_type: operation_type
                });

                const token = findToken();
                console.log('Token pour devis:', token ? 'OUI' : 'NON');

                const headers = {
                    'Content-Type': 'application/json',
                };

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const url = `${API_BASE_URL}/transferts.php?${params.toString()}`;
                console.log('URL complète pour le devis:', url);
                console.log('Headers envoyés:', headers);

                const response = await fetch(url, {
                    method: 'GET',
                    headers,
                });

                console.log('Status de la réponse:', response.status);
                console.log('Headers de la réponse:', Object.fromEntries(response.headers.entries()));

                const data = await response.json();
                console.log('Réponse du serveur pour le devis:', data);

                if (!response.ok) {
                    throw new Error(data.message || 'Erreur lors du calcul du devis');
                }

                if (data.success && data.data) {
                    addResult(
                        '✅ Devis calculé',
                        `Devis calculé avec succès:\n${JSON.stringify(data.data, null, 2)}`
                    );
                } else {
                    throw new Error('Format de réponse incorrect pour le devis');
                }
            } catch (error) {
                console.error('Error calculating quote:', error);
                addResult(
                    '❌ Erreur devis',
                    `Erreur: ${error.message}`,
                    'error'
                );
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Chargement automatique des services au démarrage
        window.onload = function() {
            addResult('🚀 Page chargée', `API_BASE_URL: ${API_BASE_URL}`);
            loadServices();
        };
    </script>
</body>
</html>
