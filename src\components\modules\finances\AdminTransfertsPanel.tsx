import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { TransfertAdminConfig } from "./TransfertAdminConfig";
import { TransfertValidation } from "./TransfertValidation";
import { TransfertStats } from "./TransfertStats";
import {
  Settings, Check, BarChart3, TrendingUp, Users, CreditCard, AlertCircle, CheckCircle, Clock, DollarSign,
  ArrowUpRight, ArrowDownRight, Activity, Globe, MapPin, Zap, Shield, RefreshCw, Calendar,
  PieChart, LineChart, Target, Award, AlertTriangle, Info, Eye, Download, Filter
} from "lucide-react";

export function AdminTransfertsPanel() {
  // État pour gérer l'onglet actif
  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Données simulées pour les statistiques avancées
  const dashboardStats = {
    // Métriques principales
    totalTransfers: 1247,
    pendingTransfers: 12,
    validatedTransfers: 245,
    rejectedTransfers: 8,

    // Volumes financiers
    todayVolume: 125450,
    weekVolume: 892340,
    monthVolume: 3456780,
    totalVolume: 15678900,

    // Commissions
    todayCommissions: 3890,
    monthCommissions: 89450,

    // Utilisateurs
    activeUsers: 89,
    newUsers: 12,
    totalUsers: 456,

    // Performance
    avgProcessingTime: 2.3, // heures
    successRate: 97.8, // pourcentage

    // Alertes
    criticalAlerts: 2,
    warningAlerts: 5,
    infoAlerts: 8
  };

  // Données pour les graphiques
  const weeklyData = [
    { day: 'Lun', transfers: 45, volume: 23450 },
    { day: 'Mar', transfers: 52, volume: 28900 },
    { day: 'Mer', transfers: 38, volume: 19800 },
    { day: 'Jeu', transfers: 61, volume: 34200 },
    { day: 'Ven', transfers: 49, volume: 26100 },
    { day: 'Sam', transfers: 33, volume: 18700 },
    { day: 'Dim', transfers: 28, volume: 15200 }
  ];

  const serviceStats = [
    { name: 'National Standard', count: 456, percentage: 45, color: 'bg-blue-500' },
    { name: 'International Express', count: 234, percentage: 23, color: 'bg-green-500' },
    { name: 'National Premium', count: 189, percentage: 19, color: 'bg-purple-500' },
    { name: 'International Standard', count: 134, percentage: 13, color: 'bg-orange-500' }
  ];

  // Fonction pour rafraîchir les données
  const refreshData = () => {
    setIsLoading(true);
    setTimeout(() => {
      setLastUpdate(new Date());
      setIsLoading(false);
    }, 1000);
  };

  // Formatage des montants
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Formatage des pourcentages
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const menuItems = [
    {
      id: "overview",
      title: "Vue d'ensemble",
      icon: BarChart3,
      color: "bg-gradient-to-br from-cyan-400 to-cyan-600",
      description: "Tableau de bord général"
    },
    {
      id: "validation",
      title: "Validation des transferts",
      icon: Check,
      color: "bg-gradient-to-br from-emerald-400 to-emerald-600",
      description: "Gérer les demandes en attente"
    },
    {
      id: "configuration",
      title: "Configuration des services",
      icon: Settings,
      color: "bg-gradient-to-br from-indigo-400 to-indigo-600",
      description: "Paramètres et commissions"
    },
    {
      id: "statistiques",
      title: "Statistiques avancées",
      icon: TrendingUp,
      color: "bg-gradient-to-br from-rose-400 to-rose-600",
      description: "Rapports détaillés"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* En-tête moderne */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Administration des Transferts</h1>
              <p className="text-gray-600 text-lg">Gérez efficacement tous vos transferts de fonds</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">Système opérationnel</span>
            </div>
          </div>
        </div>

        {/* Navigation moderne avec cartes colorées */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {menuItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = activeTab === item.id;
            return (
              <Card 
                key={item.id}
                className={`
                  w-full cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg
                  ${isActive ? 'shadow-lg scale-105 ring-2 ring-blue-500 ring-offset-2' : 'hover:shadow-md'}
                `}
                onClick={() => setActiveTab(item.id)}
              >
                <CardContent className="p-6">
                  <div className={`${item.color} rounded-lg p-4 mb-4 text-white`}>
                    <IconComponent className="h-8 w-8" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{item.title}</h3>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          {/* Onglets traditionnels cachés mais fonctionnels */}
          <TabsList className="hidden">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="validation">Validation</TabsTrigger>
            <TabsTrigger value="configuration">Configuration</TabsTrigger>
            <TabsTrigger value="statistiques">Statistiques</TabsTrigger>
          </TabsList>

          {/* Vue d'ensemble */}
          <TabsContent value="overview" className="space-y-8">
            {/* Header avec actions */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Tableau de Bord Administrateur</h2>
                <p className="text-gray-600 mt-1">Vue d'ensemble complète des transferts de fonds</p>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  Dernière MAJ: {lastUpdate.toLocaleTimeString()}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshData}
                  disabled={isLoading}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  Actualiser
                </Button>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Exporter
                </Button>
              </div>
            </div>

            {/* Métriques principales */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Transferts en attente */}
              <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Transferts en attente</p>
                      <p className="text-3xl font-bold text-orange-600">{dashboardStats.pendingTransfers}</p>
                      <p className="text-xs text-gray-500 mt-1">Nécessitent une validation</p>
                    </div>
                    <div className="bg-orange-100 p-3 rounded-full">
                      <Clock className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center">
                    <Badge variant="secondary" className="text-xs bg-orange-50 text-orange-700">
                      Urgent: 3
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Volume aujourd'hui */}
              <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Volume aujourd'hui</p>
                      <p className="text-3xl font-bold text-blue-600">{formatCurrency(dashboardStats.todayVolume)}</p>
                      <div className="flex items-center mt-1">
                        <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                        <p className="text-xs text-green-600">+12.5% vs hier</p>
                      </div>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-full">
                      <DollarSign className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <Progress value={75} className="h-2" />
                    <p className="text-xs text-gray-500 mt-1">75% de l'objectif quotidien</p>
                  </div>
                </CardContent>
              </Card>

              {/* Taux de réussite */}
              <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Taux de réussite</p>
                      <p className="text-3xl font-bold text-green-600">{formatPercentage(dashboardStats.successRate)}</p>
                      <p className="text-xs text-gray-500 mt-1">Transferts validés</p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-full">
                      <Target className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center">
                    <Badge variant="secondary" className="text-xs bg-green-50 text-green-700">
                      Excellent
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Utilisateurs actifs */}
              <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Utilisateurs actifs</p>
                      <p className="text-3xl font-bold text-purple-600">{dashboardStats.activeUsers}</p>
                      <div className="flex items-center mt-1">
                        <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                        <p className="text-xs text-green-600">+{dashboardStats.newUsers} nouveaux</p>
                      </div>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <Progress value={65} className="h-2" />
                    <p className="text-xs text-gray-500 mt-1">65% d'activité</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Métriques secondaires */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Activity className="h-5 w-5 text-blue-600" />
                    Performance système
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Temps de traitement moyen</span>
                    <span className="font-semibold">{dashboardStats.avgProcessingTime}h</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Transferts traités</span>
                    <span className="font-semibold">{dashboardStats.validatedTransfers}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Rejets</span>
                    <span className="font-semibold text-red-600">{dashboardStats.rejectedTransfers}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    Revenus & Commissions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Commissions aujourd'hui</span>
                    <span className="font-semibold text-green-600">{formatCurrency(dashboardStats.todayCommissions)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Commissions du mois</span>
                    <span className="font-semibold">{formatCurrency(dashboardStats.monthCommissions)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Marge moyenne</span>
                    <span className="font-semibold">2.8%</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    Centre d'alertes
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm text-red-800">Critiques</span>
                    </div>
                    <Badge variant="destructive" className="text-xs">{dashboardStats.criticalAlerts}</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-orange-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-orange-800">Avertissements</span>
                    </div>
                    <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">{dashboardStats.warningAlerts}</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-blue-800">Informations</span>
                    </div>
                    <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">{dashboardStats.infoAlerts}</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Graphiques et analyses */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Activité hebdomadaire */}
              <Card className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <LineChart className="h-5 w-5 text-blue-600" />
                    Activité de la semaine
                  </CardTitle>
                  <CardDescription>Volume et nombre de transferts par jour</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {weeklyData.map((day, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-12 text-sm font-medium text-gray-600">{day.day}</div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm font-semibold">{day.transfers} transferts</span>
                              <span className="text-xs text-gray-500">•</span>
                              <span className="text-sm text-gray-600">{formatCurrency(day.volume)}</span>
                            </div>
                            <Progress value={(day.transfers / 70) * 100} className="h-2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Répartition par service */}
              <Card className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5 text-purple-600" />
                    Répartition par service
                  </CardTitle>
                  <CardDescription>Distribution des transferts par type de service</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {serviceStats.map((service, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${service.color}`}></div>
                            <span className="text-sm font-medium">{service.name}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-semibold">{service.count}</div>
                            <div className="text-xs text-gray-500">{service.percentage}%</div>
                          </div>
                        </div>
                        <Progress value={service.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Actions rapides et alertes détaillées */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Actions rapides */}
              <Card className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-600" />
                    Actions rapides
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    Voir les transferts en attente
                  </Button>
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filtrer par montant élevé
                  </Button>
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Calendar className="h-4 w-4 mr-2" />
                    Rapport quotidien
                  </Button>
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Paramètres système
                  </Button>
                </CardContent>
              </Card>

              {/* Alertes détaillées */}
              <Card className="lg:col-span-2 hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-red-600" />
                    Alertes et notifications
                  </CardTitle>
                  <CardDescription>Surveillance en temps réel du système</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                      <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-red-800">Transfert suspect détecté</p>
                        <p className="text-xs text-red-600 mt-1">Montant: 500,000 XOF - Utilisateur: user_12345</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="destructive" className="text-xs">Critique</Badge>
                          <span className="text-xs text-red-600">Il y a 5 min</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                      <Info className="h-5 w-5 text-orange-600 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-orange-800">Limite quotidienne atteinte</p>
                        <p className="text-xs text-orange-600 mt-1">3 utilisateurs ont atteint leur limite de transfert</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">Avertissement</Badge>
                          <span className="text-xs text-orange-600">Il y a 15 min</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                      <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-800">Maintenance programmée</p>
                        <p className="text-xs text-blue-600 mt-1">Système de sauvegarde à 02:00 cette nuit</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">Information</Badge>
                          <span className="text-xs text-blue-600">Il y a 1h</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Résumé des volumes */}
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-indigo-600" />
                  Résumé des volumes financiers
                </CardTitle>
                <CardDescription>Vue d'ensemble des montants traités</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                    <p className="text-sm text-blue-600 font-medium mb-1">Aujourd'hui</p>
                    <p className="text-2xl font-bold text-blue-800">{formatCurrency(dashboardStats.todayVolume)}</p>
                    <div className="flex items-center justify-center mt-2">
                      <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600">+12.5%</span>
                    </div>
                  </div>

                  <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                    <p className="text-sm text-green-600 font-medium mb-1">Cette semaine</p>
                    <p className="text-2xl font-bold text-green-800">{formatCurrency(dashboardStats.weekVolume)}</p>
                    <div className="flex items-center justify-center mt-2">
                      <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600">+8.3%</span>
                    </div>
                  </div>

                  <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                    <p className="text-sm text-purple-600 font-medium mb-1">Ce mois</p>
                    <p className="text-2xl font-bold text-purple-800">{formatCurrency(dashboardStats.monthVolume)}</p>
                    <div className="flex items-center justify-center mt-2">
                      <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600">+15.7%</span>
                    </div>
                  </div>

                  <div className="text-center p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg">
                    <p className="text-sm text-indigo-600 font-medium mb-1">Total</p>
                    <p className="text-2xl font-bold text-indigo-800">{formatCurrency(dashboardStats.totalVolume)}</p>
                    <div className="flex items-center justify-center mt-2">
                      <Award className="h-3 w-3 text-yellow-500 mr-1" />
                      <span className="text-xs text-yellow-600">Record</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="validation" className="space-y-6">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-green-50">
                <CardTitle className="flex items-center gap-2 text-emerald-800">
                  <Check className="h-5 w-5" />
                  Validation des transferts
                </CardTitle>
                <CardDescription className="text-emerald-700">
                  Gérez les demandes de transfert en attente de validation.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <TransfertValidation />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="configuration" className="space-y-6">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50">
                <CardTitle className="flex items-center gap-2 text-indigo-800">
                  <Settings className="h-5 w-5" />
                  Configuration des services
                </CardTitle>
                <CardDescription className="text-indigo-700">
                  Configurez les services de transfert, les commissions et les taux de change.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <TransfertAdminConfig />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="statistiques" className="space-y-6">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-rose-50 to-pink-50">
                <CardTitle className="flex items-center gap-2 text-rose-800">
                  <TrendingUp className="h-5 w-5" />
                  Statistiques avancées
                </CardTitle>
                <CardDescription className="text-rose-700">
                  Consultez les statistiques et les rapports détaillés sur les opérations de transfert.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <TransfertStats />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
