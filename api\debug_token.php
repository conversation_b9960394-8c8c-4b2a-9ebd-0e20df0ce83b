<?php
/**
 * Script de débogage pour vérifier l'authentification
 */

require_once 'config.php';
require_once 'middleware/jwt_auth_middleware.php';

echo "=== DEBUG AUTHENTIFICATION ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Simuler différents scénarios d'authentification
echo "1. Test des en-têtes HTTP:\n";

$headers = getallheaders();
echo "   En-têtes reçus:\n";
foreach ($headers as $name => $value) {
    if (strtolower($name) === 'authorization') {
        echo "   - $name: " . substr($value, 0, 30) . "...\n";
    } else {
        echo "   - $name: $value\n";
    }
}

echo "\n2. Test du middleware JWT:\n";

$authMiddleware = new JWTAuthMiddleware();

try {
    $is_authenticated = $authMiddleware->isAuthenticated();
    $is_admin = $authMiddleware->isAdmin();
    
    echo "   Authentifié: " . ($is_authenticated ? 'OUI' : 'NON') . "\n";
    echo "   Admin: " . ($is_admin ? 'OUI' : 'NON') . "\n";
    
    if ($is_authenticated) {
        $user_data = $authMiddleware->getUserData();
        echo "   Données utilisateur: " . json_encode($user_data) . "\n";
    }
    
} catch (Exception $e) {
    echo "   Erreur: " . $e->getMessage() . "\n";
}

echo "\n3. Test avec token simulé:\n";

// Simuler un token valide (vous devrez adapter selon votre système)
$test_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test"; // Token de test

$_SERVER['HTTP_AUTHORIZATION'] = "Bearer $test_token";

try {
    $authMiddleware2 = new JWTAuthMiddleware();
    $is_authenticated2 = $authMiddleware2->isAuthenticated();
    $is_admin2 = $authMiddleware2->isAdmin();
    
    echo "   Avec token simulé:\n";
    echo "   Authentifié: " . ($is_authenticated2 ? 'OUI' : 'NON') . "\n";
    echo "   Admin: " . ($is_admin2 ? 'OUI' : 'NON') . "\n";
    
} catch (Exception $e) {
    echo "   Erreur avec token simulé: " . $e->getMessage() . "\n";
}

echo "\n4. Vérification de la base de données:\n";

try {
    // Vérifier s'il y a des utilisateurs admin
    $stmt = $pdo->query("SELECT id, nom, email, role FROM utilisateurs WHERE role = 'admin' LIMIT 3");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($admins) {
        echo "   Administrateurs trouvés:\n";
        foreach ($admins as $admin) {
            echo "   - ID: {$admin['id']}, Nom: {$admin['nom']}, Email: {$admin['email']}\n";
        }
    } else {
        echo "   ❌ Aucun administrateur trouvé\n";
    }
    
} catch (Exception $e) {
    echo "   Erreur DB: " . $e->getMessage() . "\n";
}

echo "\n5. Test de génération de token:\n";

try {
    // Essayer de générer un token pour test
    require_once 'middleware/jwt_helper.php';
    
    $test_user = [
        'id' => 1,
        'nom' => 'Admin Test',
        'email' => '<EMAIL>',
        'role' => 'admin'
    ];
    
    if (class_exists('JWTHelper')) {
        $new_token = JWTHelper::generateToken($test_user);
        echo "   Token généré: " . substr($new_token, 0, 50) . "...\n";
        
        // Tester la validation du token
        $decoded = JWTHelper::validateToken($new_token);
        echo "   Token valide: " . ($decoded ? 'OUI' : 'NON') . "\n";
    } else {
        echo "   JWTHelper non disponible\n";
    }
    
} catch (Exception $e) {
    echo "   Erreur génération token: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DEBUG ===\n";
?>
