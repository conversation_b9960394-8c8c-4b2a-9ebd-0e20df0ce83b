<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interface Admin - Commission Transferts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        .service-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        
        .commission-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .commission-item {
            background: white;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Interface Admin - Gestion des Commissions</h1>
    
    <div class="container">
        <h2>1. Authentification</h2>
        <div id="auth-status" class="status info">Non connecté</div>
        <button onclick="testLogin()">Se connecter (admin/admin)</button>
        <button onclick="checkAuthStatus()">Vérifier le statut</button>
    </div>
    
    <div class="container">
        <h2>2. Services de Transfert</h2>
        <div id="services-status" class="status info">Services non chargés</div>
        <button onclick="loadServices()">Charger les services</button>
        <div id="services-list"></div>
    </div>
    
    <div class="container">
        <h2>3. Test de Modification</h2>
        <div id="modify-status" class="status info">Aucune modification testée</div>
        
        <div class="form-group">
            <label>Service à modifier:</label>
            <select id="service-select">
                <option value="">Sélectionner un service</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Nouveau nom:</label>
            <input type="text" id="new-name" placeholder="Nouveau nom du service">
        </div>
        
        <div class="form-group">
            <label>Commission Envoi (fixe):</label>
            <input type="number" id="commission-envoi" placeholder="Ex: 500" step="0.01">
        </div>
        
        <div class="form-group">
            <label>Commission Retrait (fixe):</label>
            <input type="number" id="commission-retrait" placeholder="Ex: 300" step="0.01">
        </div>
        
        <button onclick="testModification()">Tester la modification</button>
    </div>
    
    <div class="container">
        <h2>4. Logs de Debug</h2>
        <div id="debug-logs" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
        <button onclick="clearLogs()">Effacer les logs</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = null;
        let services = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-logs');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        async function testLogin() {
            log('Tentative de connexion admin...');
            
            try {
                const response = await fetch(`${API_BASE}/login.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.token) {
                    authToken = data.token;
                    localStorage.setItem('authToken', authToken);
                    updateStatus('auth-status', `✅ Connecté en tant que ${data.user.username} (${data.user.role})`, 'success');
                    log(`Connexion réussie! Token: ${authToken.substring(0, 30)}...`, 'success');
                } else {
                    updateStatus('auth-status', `❌ Échec: ${data.message}`, 'error');
                    log(`Échec de connexion: ${data.message}`, 'error');
                }
            } catch (error) {
                updateStatus('auth-status', `❌ Erreur: ${error.message}`, 'error');
                log(`Erreur de connexion: ${error.message}`, 'error');
            }
        }
        
        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            if (token) {
                authToken = token;
                updateStatus('auth-status', `✅ Token trouvé: ${token.substring(0, 30)}...`, 'success');
                log('Token trouvé dans localStorage', 'success');
            } else {
                updateStatus('auth-status', '❌ Aucun token trouvé', 'error');
                log('Aucun token dans localStorage', 'error');
            }
        }
        
        async function loadServices() {
            if (!authToken) {
                updateStatus('services-status', '❌ Authentification requise', 'error');
                return;
            }
            
            log('Chargement des services...');
            
            try {
                const response = await fetch(`${API_BASE}/admin_transferts.php?action=services`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success && data.data) {
                    services = data.data;
                    updateStatus('services-status', `✅ ${services.length} services chargés`, 'success');
                    log(`${services.length} services chargés avec succès`, 'success');
                    displayServices();
                    populateServiceSelect();
                } else {
                    updateStatus('services-status', `❌ Échec: ${data.message}`, 'error');
                    log(`Échec du chargement: ${data.message}`, 'error');
                }
            } catch (error) {
                updateStatus('services-status', `❌ Erreur: ${error.message}`, 'error');
                log(`Erreur de chargement: ${error.message}`, 'error');
            }
        }
        
        function displayServices() {
            const container = document.getElementById('services-list');
            container.innerHTML = '';
            
            services.forEach(service => {
                const card = document.createElement('div');
                card.className = 'service-card';
                card.innerHTML = `
                    <h4>${service.nom} (ID: ${service.id})</h4>
                    <div class="commission-info">
                        <div class="commission-item">
                            <strong>Envoi Fixe:</strong> ${service.commission_envoi_fixe || 0} FCFA
                        </div>
                        <div class="commission-item">
                            <strong>Retrait Fixe:</strong> ${service.commission_retrait_fixe || 0} FCFA
                        </div>
                        <div class="commission-item">
                            <strong>Envoi %:</strong> ${service.commission_envoi_pourcentage || 0}%
                        </div>
                        <div class="commission-item">
                            <strong>Retrait %:</strong> ${service.commission_retrait_pourcentage || 0}%
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }
        
        function populateServiceSelect() {
            const select = document.getElementById('service-select');
            select.innerHTML = '<option value="">Sélectionner un service</option>';
            
            services.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.nom} (ID: ${service.id})`;
                select.appendChild(option);
            });
        }
        
        async function testModification() {
            const serviceId = document.getElementById('service-select').value;
            const newName = document.getElementById('new-name').value;
            const commissionEnvoi = document.getElementById('commission-envoi').value;
            const commissionRetrait = document.getElementById('commission-retrait').value;
            
            if (!serviceId) {
                updateStatus('modify-status', '❌ Veuillez sélectionner un service', 'error');
                return;
            }
            
            if (!authToken) {
                updateStatus('modify-status', '❌ Authentification requise', 'error');
                return;
            }
            
            const updateData = {};
            if (newName) updateData.nom = newName;
            if (commissionEnvoi) updateData.commission_envoi_fixe = parseFloat(commissionEnvoi);
            if (commissionRetrait) updateData.commission_retrait_fixe = parseFloat(commissionRetrait);
            
            if (Object.keys(updateData).length === 0) {
                updateStatus('modify-status', '❌ Aucune modification spécifiée', 'warning');
                return;
            }
            
            log(`Modification du service ${serviceId}: ${JSON.stringify(updateData)}`);
            
            try {
                const response = await fetch(`${API_BASE}/admin_transferts.php?action=services&id=${serviceId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('modify-status', '✅ Modification réussie!', 'success');
                    log('Modification réussie!', 'success');
                    
                    // Recharger les services pour voir les changements
                    setTimeout(loadServices, 1000);
                } else {
                    updateStatus('modify-status', `❌ Échec: ${data.message}`, 'error');
                    log(`Échec de modification: ${data.message}`, 'error');
                }
            } catch (error) {
                updateStatus('modify-status', `❌ Erreur: ${error.message}`, 'error');
                log(`Erreur de modification: ${error.message}`, 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('debug-logs').textContent = '';
        }
        
        // Vérifier l'authentification au chargement
        window.onload = function() {
            checkAuthStatus();
            log('Interface de test chargée');
        };
    </script>
</body>
</html>
