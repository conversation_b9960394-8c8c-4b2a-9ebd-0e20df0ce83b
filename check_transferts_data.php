<?php
/**
 * Vérifier les données des transferts pour identifier les problèmes de dates
 */

echo "VÉRIFICATION DES DONNÉES TRANSFERTS\n";
echo "===================================\n\n";

try {
    require_once 'api/config.php';
    
    // Récupérer tous les transferts avec leurs dates
    $stmt = $pdo->query("
        SELECT id, reference, montant, type, statut, 
               date_transfert, date_operation, created_at,
               service, destination
        FROM transferts 
        ORDER BY id DESC 
        LIMIT 10
    ");
    
    $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Derniers transferts dans la base de données:\n";
    echo "============================================\n";
    
    foreach ($transferts as $transfert) {
        echo "ID: {$transfert['id']}\n";
        echo "  Référence: {$transfert['reference']}\n";
        echo "  Montant: {$transfert['montant']}\n";
        echo "  Type: " . ($transfert['type'] ?? 'NULL') . "\n";
        echo "  Statut: {$transfert['statut']}\n";
        echo "  Date transfert: " . ($transfert['date_transfert'] ?? 'NULL') . "\n";
        echo "  Date opération: " . ($transfert['date_operation'] ?? 'NULL') . "\n";
        echo "  Created at: " . ($transfert['created_at'] ?? 'NULL') . "\n";
        echo "  Service: " . ($transfert['service'] ?? 'NULL') . "\n";
        echo "  Destination: " . ($transfert['destination'] ?? 'NULL') . "\n";
        
        // Vérifier si les dates sont valides
        if ($transfert['date_transfert']) {
            $date_valid = strtotime($transfert['date_transfert']);
            echo "  Date transfert valide: " . ($date_valid ? "OUI" : "NON") . "\n";
        }
        
        if ($transfert['date_operation']) {
            $date_op_valid = strtotime($transfert['date_operation']);
            echo "  Date opération valide: " . ($date_op_valid ? "OUI" : "NON") . "\n";
        }
        
        echo "  ---\n";
    }
    
    // Vérifier s'il y a des dates nulles ou invalides
    echo "\nVérification des dates problématiques:\n";
    echo "=====================================\n";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM transferts 
        WHERE date_transfert IS NULL OR date_transfert = '' OR date_transfert = '0000-00-00'
    ");
    $null_dates = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Transferts avec date_transfert nulle/invalide: {$null_dates}\n";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM transferts 
        WHERE date_operation IS NULL OR date_operation = '' OR date_operation = '0000-00-00 00:00:00'
    ");
    $null_op_dates = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Transferts avec date_operation nulle/invalide: {$null_op_dates}\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Vérification terminée\n";
?>
