<?php
/**
 * Test complet du flux admin avec le bon mot de passe
 */

echo "=== TEST COMPLET FLUX ADMIN ===\n";

// 1. Connexion admin
echo "1. Connexion admin avec mot de passe correct:\n";

$login_url = 'http://localhost:8080/api/login.php';
$credentials = [
    'username' => 'admin',
    'password' => 'admin'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($credentials));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   Code HTTP: $http_code\n";

if ($http_code !== 200) {
    echo "   ❌ Échec de connexion: $response\n";
    exit;
}

$login_data = json_decode($response, true);
if (!isset($login_data['token'])) {
    echo "   ❌ Pas de token dans la réponse\n";
    exit;
}

$token = $login_data['token'];
echo "   ✅ Token obtenu: " . substr($token, 0, 30) . "...\n";

// 2. Test GET services
echo "\n2. Test GET services:\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/api/admin_transferts.php?action=services');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   Code HTTP: $http_code\n";

if ($http_code === 200) {
    echo "   ✅ GET services réussi\n";
    $services_data = json_decode($response, true);
    if (isset($services_data['data']) && is_array($services_data['data'])) {
        echo "   Nombre de services: " . count($services_data['data']) . "\n";
        foreach ($services_data['data'] as $service) {
            echo "   - Service ID {$service['id']}: {$service['nom']}\n";
        }
    }
} else {
    echo "   ❌ GET services échoué: $response\n";
    exit;
}

// 3. Test PUT modification service
echo "\n3. Test PUT modification service ID 2:\n";

$put_data = [
    'nom' => 'FLOOZ Updated via API',
    'type' => 'Premium',
    'commission_envoi_fixe' => 400,
    'commission_retrait_fixe' => 320,
    'commission_envoi_pourcentage' => 2.5,
    'commission_retrait_pourcentage' => 2.0,
    'actif' => 1
];

echo "   Données à envoyer: " . json_encode($put_data, JSON_PRETTY_PRINT) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/api/admin_transferts.php?action=services&id=2');
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($put_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   Code HTTP: $http_code\n";
echo "   Réponse: $response\n";

if ($http_code === 200) {
    echo "   ✅ PUT modification réussie!\n";
    
    // Vérifier la modification
    echo "\n4. Vérification de la modification:\n";
    
    require_once 'config.php';
    $stmt = $pdo->prepare("SELECT nom, commission_envoi_fixe, commission_retrait_fixe FROM services_transfert WHERE id = 2");
    $stmt->execute();
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($service) {
        echo "   Service après modification:\n";
        echo "   - Nom: {$service['nom']}\n";
        echo "   - Commission envoi: {$service['commission_envoi_fixe']}\n";
        echo "   - Commission retrait: {$service['commission_retrait_fixe']}\n";
        
        if ($service['nom'] === $put_data['nom']) {
            echo "   ✅ Modification confirmée en base de données!\n";
        } else {
            echo "   ❌ Modification non reflétée en base\n";
        }
    }
    
} else {
    echo "   ❌ PUT modification échouée\n";
    
    // Analyser l'erreur
    $error_data = json_decode($response, true);
    if (isset($error_data['message'])) {
        echo "   Message d'erreur: {$error_data['message']}\n";
    }
}

echo "\n=== RÉSUMÉ ===\n";
echo "✅ Connexion admin: OK\n";
echo "✅ Token obtenu: OK\n";
echo "✅ GET services: OK\n";
echo ($http_code === 200 ? "✅" : "❌") . " PUT modification: " . ($http_code === 200 ? "OK" : "ÉCHEC") . "\n";

echo "\n=== FIN TEST ===\n";
?>
