-- Module de Transfert de Fonds
-- Date de création: 2025-07-09
USE gestion_moulin_db;

-- Table des services de transfert
CREATE TABLE IF NOT EXISTS `services_transfert` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `nom` varchar(100) NOT NULL,
  `type` enum('national','international') NOT NULL DEFAULT 'national',
  `description` text DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `commission_fixe` decimal(10,2) DEFAULT NULL COMMENT 'Commission fixe générale (pour compatibilité)',
  `commission_pourcentage` decimal(5,2) DEFAULT NULL COMMENT 'Commission pourcentage générale (pour compatibilité)',
  `commission_envoi_fixe` decimal(10,2) DEFAULT NULL COMMENT 'Commission fixe pour les envois',
  `commission_envoi_pourcentage` decimal(5,2) DEFAULT NULL COMMENT 'Commission pourcentage pour les envois',
  `commission_retrait_fixe` decimal(10,2) DEFAULT NULL COMMENT 'Commission fixe pour les retraits',
  `commission_retrait_pourcentage` decimal(5,2) DEFAULT NULL COMMENT 'Commission pourcentage pour les retraits',
  `devise_principale` varchar(3) NOT NULL DEFAULT 'XOF',
  `devises_supportees` json DEFAULT NULL,
  `actif` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des taux de change
CREATE TABLE IF NOT EXISTS `taux_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `devise_source` varchar(3) NOT NULL,
  `devise_cible` varchar(3) NOT NULL,
  `taux` decimal(15,6) NOT NULL,
  `date_effet` date NOT NULL,
  `source` varchar(100) DEFAULT NULL COMMENT 'Source du taux de change',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `taux_unique` (`devise_source`, `devise_cible`, `date_effet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des transferts
CREATE TABLE IF NOT EXISTS `transferts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(20) NOT NULL,
  `service_id` int(11) NOT NULL,
  `moulin_id` int(11) NOT NULL,
  `agent_id` int(11) NOT NULL,
  `type` enum('envoi','reception') NOT NULL,
  `montant` decimal(15,2) NOT NULL,
  `devise` varchar(3) NOT NULL DEFAULT 'XOF',
  `commission` decimal(10,2) NOT NULL DEFAULT 0,
  `commission_agent` decimal(10,2) DEFAULT 0,
  `statut` enum('en_attente','valide','complete','annule','echoue') NOT NULL DEFAULT 'en_attente',
  `date_operation` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_validation` datetime DEFAULT NULL,
  `valide_par` int(11) DEFAULT NULL,
  `motif_annulation` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `reference` (`reference`),
  FOREIGN KEY (`service_id`) REFERENCES `services_transfert` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`moulin_id`) REFERENCES `moulins` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`agent_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`valide_par`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table des détails des expéditeurs et bénéficiaires
CREATE TABLE IF NOT EXISTS `details_transfert` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfert_id` int(11) NOT NULL,
  `expediteur_nom` varchar(100) NOT NULL,
  `expediteur_prenom` varchar(100) NOT NULL,
  `expediteur_telephone` varchar(20) NOT NULL,
  `expediteur_piece_identite` varchar(50) DEFAULT NULL,
  `expediteur_adresse` varchar(255) DEFAULT NULL,
  `beneficiaire_nom` varchar(100) NOT NULL,
  `beneficiaire_prenom` varchar(100) NOT NULL,
  `beneficiaire_telephone` varchar(20) NOT NULL,
  `beneficiaire_adresse` varchar(255) DEFAULT NULL,
  `question_secrete` varchar(255) DEFAULT NULL,
  `reponse_secrete` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`transfert_id`) REFERENCES `transferts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table de l'historique des statuts des transferts
CREATE TABLE IF NOT EXISTS `historique_statut_transfert` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfert_id` int(11) NOT NULL,
  `statut_precedent` varchar(20) DEFAULT NULL,
  `statut_nouveau` varchar(20) NOT NULL,
  `user_id` int(11) NOT NULL,
  `commentaire` text DEFAULT NULL,
  `date_modification` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`transfert_id`) REFERENCES `transferts` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertion des services de transfert par défaut avec commissions différenciées
INSERT IGNORE INTO `services_transfert` (
  `code`, `nom`, `type`, `description`,
  `commission_fixe`, `commission_pourcentage`,
  `commission_envoi_fixe`, `commission_envoi_pourcentage`,
  `commission_retrait_fixe`, `commission_retrait_pourcentage`,
  `devise_principale`, `devises_supportees`, `actif`
) VALUES
('MIXX', 'Mixx by Yass', 'national', 'Service de transfert mobile national',
 500.00, 1.50, 500.00, 1.50, 300.00, 1.00, 'XOF', '["XOF"]', 1),
('FLOOZ', 'Flooz', 'national', 'Service de transfert mobile par Moov Africa',
 300.00, 1.00, 300.00, 1.00, 200.00, 0.75, 'XOF', '["XOF"]', 1),
('TMONEY', 'T-Money', 'national', 'Service de transfert mobile par Togocom',
 300.00, 1.00, 300.00, 1.00, 200.00, 0.75, 'XOF', '["XOF"]', 1),
('WARI', 'Wari', 'international', 'Service de transfert d''argent international',
 1000.00, 2.50, 1000.00, 2.50, 800.00, 2.00, 'XOF', '["XOF", "EUR", "USD", "GHS", "NGN"]', 0),
('WESTERN', 'Western Union', 'international', 'Service de transfert d''argent international',
 1500.00, 3.50, 1500.00, 3.50, 1200.00, 3.00, 'XOF', '["XOF", "EUR", "USD", "GBP", "CAD"]', 0);

-- Insertion de quelques taux de change par défaut
INSERT IGNORE INTO `taux_change` (`devise_source`, `devise_cible`, `taux`, `date_effet`, `source`) VALUES
('EUR', 'XOF', 655.957, CURDATE(), 'Banque Centrale'),
('USD', 'XOF', 600.000, CURDATE(), 'Taux moyen'),
('GBP', 'XOF', 770.000, CURDATE(), 'Taux moyen'),
('GHS', 'XOF', 50.000, CURDATE(), 'Taux moyen'),
('NGN', 'XOF', 0.750, CURDATE(), 'Taux moyen');
