<?php
/**
 * Test simple de l'API quote
 */

// Simuler une requête GET
$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET = [
    'action' => 'quote',
    'service' => 'MIXX',
    'montant' => '25000',
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF',
    'operation_type' => 'retrait'
];

echo "=== TEST DIRECT DE L'API QUOTE ===\n";
echo "Paramètres: " . json_encode($_GET) . "\n\n";

// Capturer la sortie de l'API
ob_start();
include 'transferts.php';
$output = ob_get_clean();

echo "Sortie de l'API:\n";
echo $output . "\n";

// Essayer de décoder le JSON
$data = json_decode($output, true);
if ($data) {
    echo "\nDonnées décodées:\n";
    print_r($data);
} else {
    echo "\nErreur de décodage JSON\n";
    echo "Erreur JSON: " . json_last_error_msg() . "\n";
}
?>
