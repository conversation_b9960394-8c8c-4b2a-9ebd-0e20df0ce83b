<?php
/**
 * Test simple d'authentification
 */

echo "=== TEST AUTHENTIFICATION SIMPLE ===\n";

// Test 1: Vérifier si on peut se connecter et obtenir un token
echo "1. Test de connexion admin:\n";

$login_data = [
    'email' => '<EMAIL>', // Ajustez selon vos données
    'password' => 'admin123' // Ajustez selon vos données
];

// Essayer différentes combinaisons d'admin
$admin_combinations = [
    ['email' => '<EMAIL>', 'password' => 'admin123'],
    ['email' => '<EMAIL>', 'password' => 'admin'],
    ['email' => 'admin', 'password' => 'admin'],
    ['username' => 'admin', 'password' => 'admin123']
];

foreach ($admin_combinations as $index => $creds) {
    echo "   Test " . ($index + 1) . ": ";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/api/auth.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($creds));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP $http_code - ";
    
    if ($http_code === 200) {
        $data = json_decode($response, true);
        if (isset($data['token'])) {
            echo "✅ TOKEN OBTENU: " . substr($data['token'], 0, 20) . "...\n";
            
            // Tester ce token
            echo "   Test du token obtenu:\n";
            
            $ch2 = curl_init();
            curl_setopt($ch2, CURLOPT_URL, 'http://localhost:8080/api/admin_transferts.php?action=services');
            curl_setopt($ch2, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $data['token'],
                'Content-Type: application/json'
            ]);
            curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
            
            $test_response = curl_exec($ch2);
            $test_http_code = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
            curl_close($ch2);
            
            echo "   API Test: HTTP $test_http_code - ";
            if ($test_http_code === 200) {
                echo "✅ ACCÈS AUTORISÉ\n";
                break; // Token valide trouvé
            } else {
                echo "❌ ACCÈS REFUSÉ\n";
            }
            
        } else {
            echo "❌ Pas de token dans la réponse\n";
        }
    } else {
        echo "❌ Échec de connexion\n";
    }
}

echo "\n2. Vérification des utilisateurs admin en base:\n";

require_once 'config.php';

try {
    // Vérifier la structure de la table utilisateurs
    $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
    if ($stmt->rowCount() > 0) {
        echo "   ✅ Table 'utilisateurs' trouvée\n";
        
        $stmt = $pdo->query("SELECT id, nom, email, role FROM utilisateurs WHERE role = 'admin' OR role = 'administrateur' LIMIT 5");
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($admins) {
            echo "   Administrateurs trouvés:\n";
            foreach ($admins as $admin) {
                echo "   - ID: {$admin['id']}, Nom: {$admin['nom']}, Email: {$admin['email']}, Role: {$admin['role']}\n";
            }
        } else {
            echo "   ❌ Aucun administrateur trouvé\n";
            
            // Vérifier tous les utilisateurs
            $stmt = $pdo->query("SELECT id, nom, email, role FROM utilisateurs LIMIT 5");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($users) {
                echo "   Utilisateurs existants:\n";
                foreach ($users as $user) {
                    echo "   - ID: {$user['id']}, Nom: {$user['nom']}, Email: {$user['email']}, Role: {$user['role']}\n";
                }
            } else {
                echo "   ❌ Aucun utilisateur trouvé\n";
            }
        }
        
    } else {
        echo "   ❌ Table 'utilisateurs' non trouvée\n";
        
        // Chercher d'autres tables possibles
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "   Tables disponibles: " . implode(', ', $tables) . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n=== FIN TEST ===\n";
?>
