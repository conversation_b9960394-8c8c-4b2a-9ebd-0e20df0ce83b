<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Quote</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Transferts - Calcul de Devis</h1>
        
        <div class="test-section">
            <h3>📋 Paramètres de test</h3>
            <div>
                <label>Montant: <input type="number" id="montant" value="50000" /></label>
                <label>Service: <input type="text" id="service" value="1" /></label>
                <label>Devise source: <input type="text" id="devise_source" value="XOF" /></label>
                <label>Devise destination: <input type="text" id="devise_destination" value="XOF" /></label>
                <label>Type opération: 
                    <select id="operation_type">
                        <option value="envoi">Envoi</option>
                        <option value="retrait">Retrait</option>
                    </select>
                </label>
            </div>
            <button onclick="testQuoteGET()">🔍 Test GET Quote</button>
            <button onclick="testQuotePOST()">📤 Test POST Quote</button>
            <button onclick="testServices()">📋 Test Services</button>
            <button onclick="clearResults()">🗑️ Effacer</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // Fonction pour trouver le token (comme dans le frontend)
        function findToken() {
            const keys = ['token', 'authToken', 'jwt', 'access_token'];
            for (const key of keys) {
                const value = localStorage.getItem(key);
                if (value && value.startsWith('eyJ')) {
                    return value;
                }
            }
            return null;
        }

        function addResult(title, content, isError = false) {
            const resultsDiv = document.getElementById('results');
            const section = document.createElement('div');
            section.className = `test-section ${isError ? 'error' : 'success'}`;
            section.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>Timestamp: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(section);
        }

        async function testQuoteGET() {
            try {
                const montant = document.getElementById('montant').value;
                const service = document.getElementById('service').value;
                const devise_source = document.getElementById('devise_source').value;
                const devise_destination = document.getElementById('devise_destination').value;
                const operation_type = document.getElementById('operation_type').value;
                
                const params = new URLSearchParams({
                    action: 'quote',
                    montant,
                    service,
                    devise_source,
                    devise_destination,
                    operation_type
                });

                const token = findToken();
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const url = `${API_BASE}/transferts.php?${params.toString()}`;
                console.log('URL GET:', url);
                console.log('Headers:', headers);

                const response = await fetch(url, {
                    method: 'GET',
                    headers
                });

                const data = await response.json();
                
                addResult(
                    `✅ GET Quote - Status: ${response.status}`,
                    `URL: ${url}\n\nResponse:\n${JSON.stringify(data, null, 2)}`,
                    !response.ok
                );
            } catch (error) {
                addResult('❌ GET Quote Error', error.message, true);
            }
        }

        async function testQuotePOST() {
            try {
                const montant = document.getElementById('montant').value;
                const service = document.getElementById('service').value;
                const devise_source = document.getElementById('devise_source').value;
                const devise_destination = document.getElementById('devise_destination').value;
                const operation_type = document.getElementById('operation_type').value;
                
                const requestData = {
                    montant: parseFloat(montant),
                    service,
                    devise_source,
                    devise_destination,
                    operation_type
                };

                const token = findToken();
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const url = `${API_BASE}/transferts.php?action=quote`;
                console.log('URL POST:', url);
                console.log('Data:', requestData);
                console.log('Headers:', headers);

                const response = await fetch(url, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                
                addResult(
                    `✅ POST Quote - Status: ${response.status}`,
                    `URL: ${url}\nData: ${JSON.stringify(requestData, null, 2)}\n\nResponse:\n${JSON.stringify(data, null, 2)}`,
                    !response.ok
                );
            } catch (error) {
                addResult('❌ POST Quote Error', error.message, true);
            }
        }

        async function testServices() {
            try {
                const token = findToken();
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const url = `${API_BASE}/transferts.php?action=services`;
                console.log('URL Services:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers
                });

                const data = await response.json();
                
                addResult(
                    `✅ Services - Status: ${response.status}`,
                    `URL: ${url}\n\nResponse:\n${JSON.stringify(data, null, 2)}`,
                    !response.ok
                );
            } catch (error) {
                addResult('❌ Services Error', error.message, true);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Test automatique au chargement
        window.onload = function() {
            addResult('🚀 Page chargée', 'Prêt pour les tests API');
        };
    </script>
</body>
</html>
