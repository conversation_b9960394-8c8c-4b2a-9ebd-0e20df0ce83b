<?php
/**
 * Test de l'API quote pour le calcul de devis
 */

echo "TEST API QUOTE (CALCUL DE DEVIS)\n";
echo "================================\n\n";

// Test 1: Test avec données valides
echo "1. Test avec données valides...\n";
$url = "http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?action=quote";
$data = [
    'montant' => 30000,
    'service' => '1', // ID du service MIXX
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF'
];

echo "URL: $url\n";
echo "Données: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data),
        'timeout' => 10
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response !== false) {
    $result = json_decode($response, true);
    
    if ($result && $result['success']) {
        echo "✅ Calcul de devis OK\n";
        echo "   Montant envoyé: " . $result['data']['montant_envoye'] . " " . $result['data']['devise_source'] . "\n";
        echo "   Frais: " . $result['data']['frais'] . " " . $result['data']['devise_source'] . "\n";
        echo "   Taux de change: " . $result['data']['taux_change'] . "\n";
        echo "   Montant à recevoir: " . $result['data']['montant_a_recevoir'] . " " . $result['data']['devise_destination'] . "\n";
    } else {
        echo "❌ Erreur calcul: " . ($result['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response, 0, 500) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API\n";
    $error = error_get_last();
    echo "   Erreur: " . ($error['message'] ?? 'Erreur inconnue') . "\n";
}

echo "\n";

// Test 2: Test avec service international
echo "2. Test avec service international (Western Union)...\n";
$data2 = [
    'montant' => 50000,
    'service' => '5', // ID du service Western Union
    'devise_source' => 'XOF',
    'devise_destination' => 'EUR'
];

echo "Données: " . json_encode($data2, JSON_PRETTY_PRINT) . "\n";

$context2 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data2),
        'timeout' => 10
    ]
]);

$response2 = file_get_contents($url, false, $context2);

if ($response2 !== false) {
    $result2 = json_decode($response2, true);
    
    if ($result2 && $result2['success']) {
        echo "✅ Calcul international OK\n";
        echo "   Montant envoyé: " . $result2['data']['montant_envoye'] . " " . $result2['data']['devise_source'] . "\n";
        echo "   Frais: " . $result2['data']['frais'] . " " . $result2['data']['devise_source'] . "\n";
        echo "   Taux de change: " . $result2['data']['taux_change'] . "\n";
        echo "   Montant à recevoir: " . $result2['data']['montant_a_recevoir'] . " " . $result2['data']['devise_destination'] . "\n";
    } else {
        echo "❌ Erreur calcul international: " . ($result2['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response2, 0, 500) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API pour le test international\n";
}

echo "\n";

// Test 3: Test avec données manquantes
echo "3. Test avec données manquantes...\n";
$data3 = [
    'montant' => 10000,
    // service manquant
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF'
];

echo "Données (service manquant): " . json_encode($data3, JSON_PRETTY_PRINT) . "\n";

$context3 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'content' => json_encode($data3),
        'timeout' => 10
    ]
]);

$response3 = file_get_contents($url, false, $context3);

if ($response3 !== false) {
    $result3 = json_decode($response3, true);
    
    if (!$result3['success']) {
        echo "✅ Validation OK - Erreur attendue: " . $result3['message'] . "\n";
    } else {
        echo "❌ Validation échouée - Devrait retourner une erreur\n";
    }
} else {
    echo "❌ Impossible de contacter l'API pour le test de validation\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
