<?php
/**
 * Test direct de l'API transferts.php
 */

echo "TEST DIRECT DE L'API TRANSFERTS\n";
echo "===============================\n\n";

// Simuler les variables globales nécessaires
$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET['page'] = '1';
$_GET['limit'] = '10';

// Capturer la sortie
ob_start();

try {
    // Inclure les dépendances nécessaires
    require_once 'api/config.php';
    
    echo "1. Test de connexion à la base de données...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM transferts");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Connexion DB OK - $count transferts trouvés\n\n";
    
    echo "2. Test de la requête directe...\n";
    
    // Reproduire la logique de l'API
    $page = 1;
    $limit = 10;
    $offset = 0;
    
    $query = "SELECT t.*, s.nom as service 
              FROM transferts t 
              LEFT JOIN services_transfert s ON t.service_id = s.id 
              ORDER BY t.created_at DESC LIMIT ? OFFSET ?";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$limit, $offset]);
    $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Requête exécutée - " . count($transferts) . " transferts récupérés\n\n";
    
    if (!empty($transferts)) {
        echo "3. Formatage des données...\n";
        
        foreach ($transferts as &$transfert) {
            $transfert['id'] = (int)$transfert['id'];
            $transfert['montant'] = (float)$transfert['montant'];
            
            // S'assurer que le champ 'type' n'est jamais null
            if (empty($transfert['type'])) {
                $transfert['type'] = 'national';
            }
            
            // S'assurer que le champ 'service' existe
            if (empty($transfert['service'])) {
                $transfert['service'] = 'Service non défini';
            }
        }
        
        echo "✅ Données formatées\n\n";
        
        echo "4. Exemple de réponse JSON:\n";
        $response = [
            'data' => $transferts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $count,
                'pages' => ceil($count / $limit)
            ]
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
        
        echo "✅ Test réussi - L'API devrait fonctionner\n";
    } else {
        echo "❌ Aucun transfert trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n5. Test de l'API avec authentification contournée...\n";

// Créer un fichier temporaire pour tester l'API sans auth
$testApiContent = '<?php
// Test API sans authentification
header("Access-Control-Allow-Origin: http://localhost:8080");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

require_once __DIR__ . "/config.php";

try {
    $page = max(1, intval($_GET["page"] ?? 1));
    $limit = min(100, max(1, intval($_GET["limit"] ?? 10)));
    $offset = ($page - 1) * $limit;
    
    $query = "SELECT t.*, s.nom as service 
              FROM transferts t 
              LEFT JOIN services_transfert s ON t.service_id = s.id 
              ORDER BY t.created_at DESC LIMIT ? OFFSET ?";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$limit, $offset]);
    $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $countQuery = "SELECT COUNT(*) as total FROM transferts";
    $countStmt = $pdo->prepare($countQuery);
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)["total"];
    
    foreach ($transferts as &$transfert) {
        $transfert["id"] = (int)$transfert["id"];
        $transfert["montant"] = (float)$transfert["montant"];
        
        if (empty($transfert["type"])) {
            $transfert["type"] = "national";
        }
        
        if (empty($transfert["service"])) {
            $transfert["service"] = "Service non défini";
        }
    }
    
    echo json_encode([
        "data" => $transferts,
        "pagination" => [
            "page" => $page,
            "limit" => $limit,
            "total" => (int)$total,
            "pages" => ceil($total / $limit)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur serveur: " . $e->getMessage()]);
}
?>';

file_put_contents('api/transferts_test.php', $testApiContent);
echo "✅ Fichier de test créé: api/transferts_test.php\n";
echo "🎯 Testez avec: http://localhost/Gestion_moulin_wifiZone_ok/api/transferts_test.php?page=1&limit=10\n";

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
