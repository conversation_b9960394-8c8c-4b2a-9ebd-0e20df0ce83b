<?php
/**
 * Script de débogage pour les transferts
 */

// Headers CORS
header("Access-Control-Allow-Origin: http://localhost:8080");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/services/transfer_service.php';

echo "=== DEBUG TRANSFERTS ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Test 1: Connexion à la base de données
    echo "1. Test de connexion à la base de données:\n";
    if ($pdo) {
        echo "   ✅ Connexion PDO OK\n";
        
        // Vérifier la table services_transfert
        $stmt = $pdo->query("SHOW TABLES LIKE 'services_transfert'");
        if ($stmt->rowCount() > 0) {
            echo "   ✅ Table services_transfert existe\n";
            
            // Compter les services
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM services_transfert WHERE actif = 1");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "   ✅ Services actifs: " . $count['count'] . "\n";
            
            // Lister les services
            $stmt = $pdo->query("SELECT id, nom, type FROM services_transfert WHERE actif = 1 LIMIT 5");
            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "   Services disponibles:\n";
            foreach ($services as $service) {
                echo "     - ID: {$service['id']}, Nom: {$service['nom']}, Type: {$service['type']}\n";
            }
        } else {
            echo "   ❌ Table services_transfert n'existe pas\n";
        }
    } else {
        echo "   ❌ Connexion PDO échouée\n";
    }
    
    echo "\n2. Test de la classe TransferService:\n";
    
    // Test 2: Instanciation de TransferService
    $transferService = new TransferService($pdo);
    echo "   ✅ TransferService instancié\n";
    
    // Test 3: Calcul de devis simple
    echo "\n3. Test de calcul de devis:\n";
    
    $test_data = [
        'montant' => 50000,
        'service' => '1', // ID du premier service
        'devise_source' => 'XOF',
        'devise_destination' => 'XOF',
        'operation_type' => 'envoi'
    ];
    
    echo "   Données de test: " . json_encode($test_data) . "\n";
    
    // Récupérer le service
    $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE id = ? AND actif = 1");
    $stmt->execute([$test_data['service']]);
    $service_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($service_info) {
        echo "   ✅ Service trouvé: " . $service_info['nom'] . "\n";
        
        // Calculer les frais
        $frais = $transferService->calculateFees($service_info['nom'], $test_data['montant'], $test_data['operation_type']);
        echo "   ✅ Frais calculés: " . number_format($frais, 0, ',', ' ') . " FCFA\n";
        
        // Calculer le taux de change
        $taux_change = $transferService->getExchangeRate($test_data['devise_source'], $test_data['devise_destination']);
        echo "   ✅ Taux de change: $taux_change\n";
        
        // Calculer le montant à recevoir
        $montant_a_recevoir = ($test_data['montant'] - $frais) * $taux_change;
        echo "   ✅ Montant à recevoir: " . number_format($montant_a_recevoir, 0, ',', ' ') . " FCFA\n";
        
        echo "\n4. Simulation de réponse API:\n";
        $response = [
            'success' => true,
            'data' => [
                'montant_source' => $test_data['montant'],
                'montant_envoye' => $test_data['montant'],
                'frais' => $frais,
                'taux_change' => $taux_change,
                'devise_source' => $test_data['devise_source'],
                'devise_destination' => $test_data['devise_destination'],
                'montant_a_recevoir' => round($montant_a_recevoir, 2),
                'montant_destination' => round($montant_a_recevoir, 2)
            ]
        ];
        
        echo "   Réponse JSON: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "   ❌ Service non trouvé avec ID: " . $test_data['service'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "   Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== FIN DEBUG ===\n";
?>
