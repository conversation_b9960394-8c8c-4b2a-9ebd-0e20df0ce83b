<?php
require_once 'api/config.php';

echo "CRÉATION DE TRANSFERTS DE TEST SUPPLÉMENTAIRES\n";
echo "==============================================\n\n";

try {
    // Récupérer les services disponibles
    $stmt = $pdo->query("SELECT id, nom, type FROM services_transfert WHERE actif = 1");
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Services disponibles:\n";
    foreach ($services as $service) {
        echo "- ID: {$service['id']}, Nom: {$service['nom']}, Type: {$service['type']}\n";
    }
    
    if (empty($services)) {
        echo "❌ Aucun service disponible!\n";
        exit;
    }
    
    // Créer plusieurs transferts de test
    $testTransferts = [
        [
            'montant' => 25000.00,
            'type' => 'national',
            'destination' => 'Bouaké',
            'motif' => 'Paiement facture électricité',
            'statut' => 'valide'
        ],
        [
            'montant' => 150000.00,
            'type' => 'international',
            'destination' => 'Dakar, Sénégal',
            'motif' => 'Envoi famille',
            'statut' => 'en_attente'
        ],
        [
            'montant' => 80000.00,
            'type' => 'national',
            'destination' => 'Yamoussoukro',
            'motif' => 'Achat marchandises',
            'statut' => 'termine'
        ],
        [
            'montant' => 300000.00,
            'type' => 'international',
            'destination' => 'Paris, France',
            'motif' => 'Frais de scolarité',
            'statut' => 'valide'
        ],
        [
            'montant' => 45000.00,
            'type' => 'national',
            'destination' => 'San Pedro',
            'motif' => 'Transfert commercial',
            'statut' => 'rejete'
        ]
    ];
    
    echo "\nCréation des transferts...\n";
    
    $insertSQL = "INSERT INTO transferts (reference, montant, type, destination, service_id, date_transfert, motif, statut, agent_id, moulin_id) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)";
    $stmt = $pdo->prepare($insertSQL);
    
    foreach ($testTransferts as $index => $transfert) {
        // Choisir un service aléatoire du bon type
        $servicesFiltered = array_filter($services, function($s) use ($transfert) {
            return $s['type'] === $transfert['type'];
        });
        
        if (empty($servicesFiltered)) {
            // Si pas de service du bon type, prendre le premier disponible
            $serviceChoisi = $services[0];
        } else {
            $serviceChoisi = $servicesFiltered[array_rand($servicesFiltered)];
        }
        
        $reference = 'TRF_' . date('Ymd') . '_' . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
        $dateTransfert = date('Y-m-d', strtotime('-' . rand(0, 7) . ' days'));
        
        try {
            $stmt->execute([
                $reference,
                $transfert['montant'],
                $transfert['type'],
                $transfert['destination'],
                $serviceChoisi['id'],
                $dateTransfert,
                $transfert['motif'],
                $transfert['statut']
            ]);
            
            echo "✅ Transfert créé: $reference ({$transfert['type']}) - {$transfert['montant']} FCFA via {$serviceChoisi['nom']}\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur pour $reference: " . $e->getMessage() . "\n";
        }
    }
    
    // Vérification finale
    echo "\n📊 STATISTIQUES FINALES:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM transferts");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "Total des transferts: $total\n";
    
    $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM transferts GROUP BY type");
    $typeCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Répartition par type:\n";
    foreach ($typeCounts as $typeCount) {
        echo "- {$typeCount['type']}: {$typeCount['count']}\n";
    }
    
    $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM transferts GROUP BY statut");
    $statutCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Répartition par statut:\n";
    foreach ($statutCounts as $statutCount) {
        echo "- {$statutCount['statut']}: {$statutCount['count']}\n";
    }
    
    echo "\n✅ CRÉATION TERMINÉE!\n";
    echo "\n🎯 PROCHAINES ÉTAPES:\n";
    echo "1. Tester l'interface manager des transferts\n";
    echo "2. Vérifier que l'erreur JavaScript est résolue\n";
    echo "3. Tester les filtres et la pagination\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
}
?>
