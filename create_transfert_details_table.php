<?php
/**
 * <PERSON><PERSON>er la table pour les détails des transferts externes
 */

echo "CRÉATION TABLE DÉTAILS TRANSFERTS\n";
echo "=================================\n\n";

try {
    require_once 'api/config.php';
    
    // Créer la table des détails de transfert
    $sql = "CREATE TABLE IF NOT EXISTS transfert_details (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transfert_id INT NOT NULL,
        expediteur_nom VARCHAR(100) NOT NULL,
        expediteur_prenom VARCHAR(100),
        expediteur_telephone VARCHAR(20) NOT NULL,
        expediteur_adresse TEXT,
        destinataire_nom VARCHAR(100) NOT NULL,
        destinataire_prenom VARCHAR(100),
        destinataire_telephone VARCHAR(20) NOT NULL,
        destinataire_adresse TEXT,
        devise_source VARCHAR(3) DEFAULT 'XOF',
        devise_destination VARCHAR(3) DEFAULT 'XOF',
        taux_change DECIMAL(10,6) DEFAULT 1.000000,
        montant_a_recevoir DECIMAL(15,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transfert_id) REFERENCES transferts(id) ON DELETE CASCADE
    )";
    
    if ($pdo->exec($sql)) {
        echo "✅ Table 'transfert_details' créée avec succès\n";
    } else {
        echo "❌ Erreur lors de la création de la table\n";
    }
    
    // Vérifier la structure
    echo "\nStructure de la table transfert_details:\n";
    $stmt = $pdo->query('DESCRIBE transfert_details');
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $nullable = $row['Null'] == 'YES' ? 'NULL' : 'NOT NULL';
        $default = $row['Default'] ? "DEFAULT '{$row['Default']}'" : '';
        echo "- {$row['Field']}: {$row['Type']} {$nullable} {$default}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Création terminée\n";
?>
