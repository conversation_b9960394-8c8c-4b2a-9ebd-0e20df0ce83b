<?php
require_once 'api/config.php';

echo "STRUCTURE TABLE TRANSFERTS\n";
echo "==========================\n";

$stmt = $pdo->query('DESCRIBE transferts');
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $nullable = $row['Null'] == 'YES' ? 'NULL' : 'NOT NULL';
    $default = $row['Default'] ? "DEFAULT '{$row['Default']}'" : '';
    echo "{$row['Field']}: {$row['Type']} {$nullable} {$default}\n";
}

echo "\nNombre de transferts: ";
$stmt = $pdo->query('SELECT COUNT(*) FROM transferts');
echo $stmt->fetchColumn() . "\n";
?>
