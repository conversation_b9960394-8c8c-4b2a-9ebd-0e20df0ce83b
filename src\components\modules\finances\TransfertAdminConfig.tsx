import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Settings, Building2, TrendingUp, CheckCircle, Globe, MapPin, ArrowUpDown, Plus, Edit, Trash2 } from "lucide-react";

export function TransfertAdminConfig() {
  const [isLoading, setIsLoading] = useState(false);
  const [services, setServices] = useState([]);
  const [taux, setTaux] = useState([]);
  const [activeTab, setActiveTab] = useState("services");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [formData, setFormData] = useState({
    code: '',
    nom: '',
    type: 'national',
    description: '',
    commission_fixe: 0,
    commission_pourcentage: 0,
    commission_envoi_fixe: 0,
    commission_envoi_pourcentage: 0,
    commission_retrait_fixe: 0,
    commission_retrait_pourcentage: 0,
    devise_principale: 'XOF',
    actif: 1
  });
  const { toast } = useToast();

  // Fonction pour trouver le token dans localStorage
  const findToken = () => {
    const keys = ['token', 'authToken', 'jwt', 'access_token'];
    for (const key of keys) {
      const value = localStorage.getItem(key);
      if (value && value.startsWith('eyJ')) {
        return value;
      }
    }
    return null;
  };

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      nom: '',
      type: 'Standard',
      description: '',
      commission_fixe: 0,
      commission_pourcentage: 0,
      commission_envoi_fixe: 0,
      commission_envoi_pourcentage: 0,
      commission_retrait_fixe: 0,
      commission_retrait_pourcentage: 0,
      actif: 1
    });
    setEditingService(null);
  };

  // Ouvrir le dialog pour créer un nouveau service
  const handleCreateService = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  // Ouvrir le dialog pour éditer un service
  const handleEditService = (service) => {
    setFormData({
      nom: service.nom || '',
      type: service.type || 'Standard',
      description: service.description || '',
      commission_fixe: service.commission_fixe || 0,
      commission_pourcentage: service.commission_pourcentage || 0,
      commission_envoi_fixe: service.commission_envoi_fixe || service.commission_fixe || 0,
      commission_envoi_pourcentage: service.commission_envoi_pourcentage || service.commission_pourcentage || 0,
      commission_retrait_fixe: service.commission_retrait_fixe || (service.commission_fixe * 0.8) || 0,
      commission_retrait_pourcentage: service.commission_retrait_pourcentage || (service.commission_pourcentage * 0.8) || 0,
      actif: service.actif !== undefined ? service.actif : 1
    });
    setEditingService(service);
    setIsDialogOpen(true);
  };

  // Sauvegarder un service (créer ou modifier)
  const handleSaveService = async () => {
    const token = findToken();
    console.log('Token trouvé:', token ? 'OUI' : 'NON', token ? token.substring(0, 20) + '...' : 'Aucun');

    if (!token) {
      toast({
        title: "Erreur",
        description: "Token d'authentification non trouvé",
        variant: "destructive",
      });
      return;
    }

    if (!formData.nom) {
      toast({
        title: "Erreur",
        description: "Le nom du service est obligatoire",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const url = editingService
        ? `http://localhost:8080/api/admin_transferts.php?action=services&id=${editingService.id}`
        : 'http://localhost:8080/api/admin_transferts.php?action=services';

      const method = editingService ? 'PUT' : 'POST';

      // Debug: Log des données envoyées
      console.log('Sending to API:', {
        url,
        method,
        formData,
        editingService: editingService?.id
      });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Succès",
          description: editingService ? "Service modifié avec succès" : "Service créé avec succès",
        });
        setIsDialogOpen(false);
        resetForm();
        loadServices(); // Recharger la liste
      } else {
        toast({
          title: "Erreur",
          description: data.message || "Erreur lors de la sauvegarde",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erreur:', error);
      toast({
        title: "Erreur",
        description: "Erreur de connexion au serveur",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Chargement initial des données
  useEffect(() => {
    const token = findToken();
    if (!token) {
      toast({
        title: "Authentification requise",
        description: "Veuillez vous connecter pour accéder à cette page",
        variant: "destructive"
      });
      window.location.href = '/login';
      return;
    }
    
    // Charger les données au démarrage
    loadServices();
    loadTaux();
  }, [toast]);

  // Charger les services
  const loadServices = async () => {
    setIsLoading(true);
    try {
      const token = findToken();
      if (!token) {
        throw new Error("Token non trouvé");
      }

      console.log("Chargement des services...");
      
      const response = await fetch('/api/admin_transferts.php?action=services', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log("Réponse HTTP:", response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Données reçues:", data);
      
      if (data.success) {
        setServices(data.data || []);
        // Services chargés avec succès
      } else {
        throw new Error(data.message || "Erreur inconnue");
      }
    } catch (error: any) {
      console.error("Erreur API services:", error);
      
      if (error.message.includes('401')) {
        toast({
          title: "Session expirée",
          description: "Veuillez vous reconnecter",
          variant: "destructive"
        });
        window.location.href = '/login';
        return;
      }
      
      toast({
        title: "Erreur",
        description: error.message || "Impossible de récupérer les services",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les taux
  const loadTaux = async () => {
    setIsLoading(true);
    try {
      const token = findToken();
      if (!token) {
        throw new Error("Token non trouvé");
      }

      console.log("Chargement des taux...");
      
      const response = await fetch('/api/admin_transferts.php?action=taux', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log("Réponse HTTP:", response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Données reçues:", data);
      
      if (data.success) {
        setTaux(data.data || []);
        // Taux chargés avec succès
      } else {
        throw new Error(data.message || "Erreur inconnue");
      }
    } catch (error: any) {
      console.error("Erreur API taux:", error);
      
      if (error.message.includes('401')) {
        toast({
          title: "Session expirée",
          description: "Veuillez vous reconnecter",
          variant: "destructive"
        });
        window.location.href = '/login';
        return;
      }
      
      toast({
        title: "Erreur",
        description: error.message || "Impossible de récupérer les taux",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête avec dégradé coloré */}
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white p-6 rounded-lg shadow-lg">
        <div className="flex items-center gap-3">
          <Settings className="h-8 w-8" />
          <h1 className="text-2xl font-bold">Configuration des Services de Transfert</h1>
        </div>
        <p className="text-indigo-100 text-lg">
          Configurez les services de transfert, les commissions et les taux de change.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-gray-100 to-gray-200">
          <TabsTrigger 
            value="services" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white"
          >
            <Building2 className="h-4 w-4 mr-2" />
            Services
          </TabsTrigger>
          <TabsTrigger 
            value="taux" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-600 data-[state=active]:text-white"
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Taux de Change
          </TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-blue-900">Services de Transfert</h3>
              <div className="flex gap-2">
                <Button
                  onClick={handleCreateService}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un service
                </Button>
                <Button
                  onClick={loadServices}
                  disabled={isLoading}
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                >
                  {isLoading ? "Chargement..." : "Actualiser"}
                </Button>
              </div>
            </div>
          </div>

          {services.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun service configuré</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {services.map((service, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {service.type === 'national' ? (
                          <MapPin className="h-5 w-5 text-blue-500" />
                        ) : (
                          <Globe className="h-5 w-5 text-green-500" />
                        )}
                        <CardTitle className="text-lg">{service.nom}</CardTitle>
                      </div>
                      <Badge variant={service.actif ? "default" : "secondary"}>
                        {service.actif ? "Actif" : "Inactif"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Type:</span>
                        <Badge variant="outline">{service.type}</Badge>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-gray-600 font-medium">Envoi:</span>
                          <span className="font-medium text-blue-600">
                            {service.commission_envoi_fixe || service.commission_fixe || 0} FCFA + {service.commission_envoi_pourcentage || service.commission_pourcentage || 0}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 font-medium">Retrait:</span>
                          <span className="font-medium text-green-600">
                            {service.commission_retrait_fixe || (service.commission_fixe * 0.8) || 0} FCFA + {service.commission_retrait_pourcentage || (service.commission_pourcentage * 0.8) || 0}%
                          </span>
                        </div>
                      </div>
                      {(service.montant_min || service.montant_max) && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Limites:</span>
                          <span className="font-medium">{service.montant_min || 0} FCFA - {service.montant_max || '∞'} FCFA</span>
                        </div>
                      )}
                    </div>
                    <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditService(service)}
                        className="hover:bg-blue-50"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="taux" className="space-y-4">
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-emerald-900">Taux de Change</h3>
              <Button 
                onClick={loadTaux} 
                disabled={isLoading}
                className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700"
              >
                {isLoading ? "Chargement..." : "Actualiser"}
              </Button>
            </div>
          </div>

          {taux.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun taux configuré</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {taux.map((tauxItem, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <ArrowUpDown className="h-5 w-5 text-emerald-500" />
                        <CardTitle className="text-lg">{tauxItem.devise_source} → {tauxItem.devise_cible}</CardTitle>
                      </div>
                      <Badge variant={tauxItem.actif ? "default" : "secondary"}>
                        {tauxItem.actif ? "Actif" : "Inactif"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Taux:</span>
                        <span className="font-bold text-lg text-emerald-600">{tauxItem.taux}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Source:</span>
                        <span className="font-medium">{tauxItem.source}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Mis à jour:</span>
                        <span className="font-medium">{new Date(tauxItem.updated_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Dialog de création/modification de service */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingService ? 'Modifier le service' : 'Créer un nouveau service'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="nom">Nom du service *</Label>
                <Input
                  id="nom"
                  value={formData.nom}
                  onChange={(e) => setFormData({...formData, nom: e.target.value})}
                  placeholder="Ex: MIXX, FLOOZ, Orange Money"
                />
              </div>
              <div>
                <Label htmlFor="type">Type de service</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({...formData, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Standard">Standard</SelectItem>
                    <SelectItem value="Premium">Premium</SelectItem>
                    <SelectItem value="Express">Express</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Type de service</Label>
                <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="national">National</SelectItem>
                    <SelectItem value="international">International</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="devise">Devise principale</Label>
                <Select value={formData.devise_principale} onValueChange={(value) => setFormData({...formData, devise_principale: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="XOF">XOF (FCFA)</SelectItem>
                    <SelectItem value="EUR">EUR (Euro)</SelectItem>
                    <SelectItem value="USD">USD (Dollar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Description du service de transfert"
                rows={3}
              />
            </div>

            {/* Commissions d'envoi */}
            <div className="border rounded-lg p-4 bg-blue-50">
              <h4 className="font-semibold text-blue-900 mb-3">Commissions d'envoi</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="commission_envoi_fixe">Commission fixe (FCFA)</Label>
                  <Input
                    id="commission_envoi_fixe"
                    type="number"
                    value={formData.commission_envoi_fixe}
                    onChange={(e) => setFormData({...formData, commission_envoi_fixe: parseFloat(e.target.value) || 0})}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="commission_envoi_pourcentage">Commission pourcentage (%)</Label>
                  <Input
                    id="commission_envoi_pourcentage"
                    type="number"
                    step="0.01"
                    value={formData.commission_envoi_pourcentage}
                    onChange={(e) => setFormData({...formData, commission_envoi_pourcentage: parseFloat(e.target.value) || 0})}
                    placeholder="0.00"
                  />
                </div>
              </div>
            </div>

            {/* Commissions de retrait */}
            <div className="border rounded-lg p-4 bg-green-50">
              <h4 className="font-semibold text-green-900 mb-3">Commissions de retrait</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="commission_retrait_fixe">Commission fixe (FCFA)</Label>
                  <Input
                    id="commission_retrait_fixe"
                    type="number"
                    value={formData.commission_retrait_fixe}
                    onChange={(e) => setFormData({...formData, commission_retrait_fixe: parseFloat(e.target.value) || 0})}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="commission_retrait_pourcentage">Commission pourcentage (%)</Label>
                  <Input
                    id="commission_retrait_pourcentage"
                    type="number"
                    step="0.01"
                    value={formData.commission_retrait_pourcentage}
                    onChange={(e) => setFormData({...formData, commission_retrait_pourcentage: parseFloat(e.target.value) || 0})}
                    placeholder="0.00"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Annuler
              </Button>
              <Button
                onClick={handleSaveService}
                disabled={isLoading}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
              >
                {isLoading ? "Sauvegarde..." : (editingService ? "Modifier" : "Créer")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
