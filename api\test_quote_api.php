<?php
/**
 * Script de test pour l'API quote avec le nouveau paramètre operation_type
 */

echo "=== TEST DE L'API QUOTE AVEC OPERATION_TYPE ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Test 1: Envoi avec MIXX
echo "1. Test envoi avec MIXX (25000 FCFA):\n";
$url = "http://localhost:8080/api/transferts.php?" . http_build_query([
    'action' => 'quote',
    'service' => 'MIXX',
    'montant' => '25000',
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF',
    'operation_type' => 'envoi'
]);

echo "   URL: $url\n";

$response = file_get_contents($url);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "   ✅ Succès\n";
    echo "   Frais: " . $data['data']['frais'] . " FCFA\n";
    echo "   Montant à recevoir: " . $data['data']['montant_a_recevoir'] . " FCFA\n";
    echo "   Taux de change: " . $data['data']['taux_change'] . "\n";
} else {
    echo "   ❌ Erreur: " . ($data['message'] ?? 'Réponse invalide') . "\n";
    echo "   Réponse brute: $response\n";
}

echo "\n";

// Test 2: Retrait avec MIXX
echo "2. Test retrait avec MIXX (25000 FCFA):\n";
$url = "http://localhost:8080/api/transferts.php?" . http_build_query([
    'action' => 'quote',
    'service' => 'MIXX',
    'montant' => '25000',
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF',
    'operation_type' => 'retrait'
]);

echo "   URL: $url\n";

$response = file_get_contents($url);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "   ✅ Succès\n";
    echo "   Frais: " . $data['data']['frais'] . " FCFA\n";
    echo "   Montant à recevoir: " . $data['data']['montant_a_recevoir'] . " FCFA\n";
    echo "   Taux de change: " . $data['data']['taux_change'] . "\n";
} else {
    echo "   ❌ Erreur: " . ($data['message'] ?? 'Réponse invalide') . "\n";
    echo "   Réponse brute: $response\n";
}

echo "\n";

// Test 3: Comparaison des frais
echo "3. Comparaison des frais pour différents services:\n";
$services = ['MIXX', 'FLOOZ', 'Orange Money'];
$montant = 50000;

foreach ($services as $service) {
    echo "   Service: $service\n";
    
    // Test envoi
    $url_envoi = "http://localhost:8080/api/transferts.php?" . http_build_query([
        'action' => 'quote',
        'service' => $service,
        'montant' => $montant,
        'devise_source' => 'XOF',
        'devise_destination' => 'XOF',
        'operation_type' => 'envoi'
    ]);
    
    $response_envoi = file_get_contents($url_envoi);
    $data_envoi = json_decode($response_envoi, true);
    
    // Test retrait
    $url_retrait = "http://localhost:8080/api/transferts.php?" . http_build_query([
        'action' => 'quote',
        'service' => $service,
        'montant' => $montant,
        'devise_source' => 'XOF',
        'devise_destination' => 'XOF',
        'operation_type' => 'retrait'
    ]);
    
    $response_retrait = file_get_contents($url_retrait);
    $data_retrait = json_decode($response_retrait, true);
    
    if ($data_envoi && $data_envoi['success'] && $data_retrait && $data_retrait['success']) {
        $frais_envoi = $data_envoi['data']['frais'];
        $frais_retrait = $data_retrait['data']['frais'];
        $difference = $frais_envoi - $frais_retrait;
        $pourcentage_diff = ($difference / $frais_envoi) * 100;
        
        echo "     Envoi: " . number_format($frais_envoi, 0, ',', ' ') . " FCFA\n";
        echo "     Retrait: " . number_format($frais_retrait, 0, ',', ' ') . " FCFA\n";
        echo "     Différence: " . number_format($difference, 0, ',', ' ') . " FCFA (" . 
             number_format($pourcentage_diff, 1) . "%)\n";
        
        if ($frais_retrait < $frais_envoi) {
            echo "     ✅ Le retrait est moins cher que l'envoi\n";
        } else {
            echo "     ⚠️  Le retrait n'est pas moins cher que l'envoi\n";
        }
    } else {
        echo "     ❌ Erreur lors du test pour $service\n";
    }
    echo "\n";
}

// Test 4: Test sans operation_type (fallback)
echo "4. Test sans operation_type (doit utiliser 'envoi' par défaut):\n";
$url = "http://localhost:8080/api/transferts.php?" . http_build_query([
    'action' => 'quote',
    'service' => 'MIXX',
    'montant' => '25000',
    'devise_source' => 'XOF',
    'devise_destination' => 'XOF'
    // Pas d'operation_type
]);

echo "   URL: $url\n";

$response = file_get_contents($url);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "   ✅ Succès (fallback vers 'envoi')\n";
    echo "   Frais: " . $data['data']['frais'] . " FCFA\n";
} else {
    echo "   ❌ Erreur: " . ($data['message'] ?? 'Réponse invalide') . "\n";
}

echo "\n=== TESTS TERMINÉS ===\n";
?>
