import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose, DialogDescription } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import api from "@/services/api";
import { PlusCircle, Search, Filter, ChevronLeft, ChevronRight, Trash2, RefreshCcw, CheckCircle, AlertCircle, Loader2, ArrowLeftRight, BarChart4 } from "lucide-react";
import { CreateExternalTransferForm } from "./CreateExternalTransferForm";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Toast, ToastDescription, ToastTitle } from "@/components/ui/toast";
import { Toaster } from "@/components/ui/toaster";

// Importer les styles personnalisés
import "@/styles/custom.css";

// Types
interface Transfert {
  id: number;
  montant: number;
  type: 'national' | 'international' | null | undefined;
  destination?: string;
  service: string;
  date_transfert: string;
  motif: string;
  statut: string;
  reference: string;
  created_at: string;
}

export function TransfertsManager() {
  // États
  const [transferts, setTransferts] = useState<Transfert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTransfertId, setSelectedTransfertId] = useState<number | null>(null);
  const [isCreateExternalDialogOpen, setIsCreateExternalDialogOpen] = useState(false);
  const [actionInProgress, setActionInProgress] = useState<string | null>(null);

  // Filtres
  const [filters, setFilters] = useState({
    type: "all",
    destination: "",
    statut: "all",
    dateDebut: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    dateFin: new Date(),
    search: ""
  });

  const [filtersApplied, setFiltersApplied] = useState(false);

  // Hook pour les notifications
  const { toast } = useToast();

  // Fonction pour charger les transferts
  const fetchTransferts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(filters.type && filters.type !== "all" && { type: filters.type }),
        ...(filters.destination && { destination: filters.destination }),
        ...(filters.statut && filters.statut !== "all" && { statut: filters.statut }),
        ...(filters.search && { search: filters.search }),
        ...(filtersApplied && { date_debut: format(filters.dateDebut, "yyyy-MM-dd") }),
        ...(filtersApplied && { date_fin: format(filters.dateFin, "yyyy-MM-dd") })
      });

      const response = await api.get(`api/transferts.php?${params.toString()}`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      const data = response.data;

      if (data.success) {
        setTransferts(data.data || []);
        setTotalPages(data.pagination?.total_pages || 1);
      } else {
        setError(data.message || "Erreur lors du chargement des transferts");
        toast({
          title: "Erreur",
          description: data.message || "Impossible de charger les transferts",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      const errorMessage = error.name === 'AbortError' 
        ? "La requête a pris trop de temps. Vérifiez votre connexion." 
        : "Erreur de connexion à l'API transferts";
      console.error(errorMessage, error);
      setError(errorMessage);
      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, filters, toast]);

  // Fonction pour supprimer un transfert
  const deleteTransfert = async () => {
    if (!selectedTransfertId) return;

    setIsLoading(true);
    setError(null);
    setActionInProgress('delete');

    try {
      const response = await api.delete(`api/transferts.php?id=${selectedTransfertId}`);
      const data = response.data;

      if (data.success) {
        toast({
          title: "Succès",
          description: "Transfert supprimé avec succès",
          variant: "default"
        });
        fetchTransferts();
        setIsDeleteDialogOpen(false);
      } else {
        setError(data.message || "Erreur lors de la suppression");
        toast({
          title: "Erreur",
          description: data.message || "Impossible de supprimer le transfert",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      const errorMessage = "Erreur lors de la suppression du transfert";
      console.error(errorMessage, error);
      setError(errorMessage);
      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      setActionInProgress(null);
    }
  };

  // Fonction pour confirmer un transfert
  const confirmTransfert = async (id: number) => {
    setIsLoading(true);
    setActionInProgress('confirm');

    try {
      const response = await api.patch(`api/transferts.php?id=${id}`, {
        statut: 'validé'
      });
      const data = response.data;

      if (data.success) {
        toast({
          title: "Succès",
          description: "Transfert confirmé avec succès",
          variant: "default"
        });
        fetchTransferts();
      } else {
        setError(data.message || "Erreur lors de la confirmation");
        toast({
          title: "Erreur",
          description: data.message || "Impossible de confirmer le transfert",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      const errorMessage = "Erreur lors de la confirmation du transfert";
      console.error(errorMessage, error);
      setError(errorMessage);
      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      setActionInProgress(null);
    }
  };

  // Fonction pour appliquer les filtres
  const applyFilters = () => {
    setCurrentPage(1);
    setFiltersApplied(true);
  };

  // Fonction pour réinitialiser les filtres
  const resetFilters = () => {
    setFilters({
      type: "all",
      destination: "",
      statut: "all",
      dateDebut: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      dateFin: new Date(),
      search: ""
    });
    setCurrentPage(1);
    setFiltersApplied(false);
  };

  // Fonction pour changer de page
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Charger les données au montage et quand les filtres changent
  useEffect(() => {
    fetchTransferts();
  }, [fetchTransferts]);

  // Fonction pour obtenir le badge de statut
  const getStatusBadge = (statut: string) => {
    const statusConfig = {
      'en_attente': { label: 'En attente', className: 'bg-yellow-100 text-yellow-800' },
      'validé': { label: 'Validé', className: 'bg-green-100 text-green-800' },
      'rejeté': { label: 'Rejeté', className: 'bg-red-100 text-red-800' },
      'en_cours': { label: 'En cours', className: 'bg-blue-100 text-blue-800' },
      'terminé': { label: 'Terminé', className: 'bg-gray-100 text-gray-800' }
    };

    const config = statusConfig[statut as keyof typeof statusConfig] || 
                   { label: statut, className: 'bg-gray-100 text-gray-800' };

    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Fonction pour obtenir le badge de type
  const getTypeBadge = (type: string | null | undefined) => {
    const typeConfig = {
      'national': { label: 'National 🇫🇷', className: 'bg-blue-100 text-blue-800' },
      'international': { label: 'International 🌍', className: 'bg-purple-100 text-purple-800' }
    };

    // Gérer les valeurs null/undefined
    if (!type) {
      return (
        <Badge className="bg-gray-100 text-gray-800">
          Non défini
        </Badge>
      );
    }

    const config = typeConfig[type as keyof typeof typeConfig] ||
                   { label: type, className: 'bg-gray-100 text-gray-800' };

    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Toaster />
      
      {/* En-tête */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-blue-800 flex items-center">
            <ArrowLeftRight className="mr-3 h-6 w-6 text-blue-600" />
            Gestion des Transferts Nationaux et Internationaux
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <BarChart4 className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">
                  Total: {transferts.length} transfert(s)
                </span>
              </div>
            </div>
            
            <div className="flex gap-3">
              <Dialog open={isCreateExternalDialogOpen} onOpenChange={setIsCreateExternalDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700 transition-all hover:scale-105">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Nouveau Transfert
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[700px]">
                  <DialogHeader>
                    <DialogTitle>Nouveau Transfert</DialogTitle>
                    <DialogDescription>
                      Créez un nouveau transfert de fonds national ou international.
                    </DialogDescription>
                  </DialogHeader>
                  <CreateExternalTransferForm 
                    onClose={() => setIsCreateExternalDialogOpen(false)}
                    onSuccess={() => {
                      setIsCreateExternalDialogOpen(false);
                      fetchTransferts();
                    }}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filtres */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filtres de recherche
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Type de transfert</Label>
              <Select value={filters.type} onValueChange={(value) => setFilters({...filters, type: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner le type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les types</SelectItem>
                  <SelectItem value="national">National 🇫🇷</SelectItem>
                  <SelectItem value="international">International 🌍</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="destination">Destination</Label>
              <Input
                id="destination"
                placeholder="Destination..."
                value={filters.destination}
                onChange={(e) => setFilters({...filters, destination: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="statut">Statut</Label>
              <Select value={filters.statut} onValueChange={(value) => setFilters({...filters, statut: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner le statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="en_attente">En attente</SelectItem>
                  <SelectItem value="validé">Validé</SelectItem>
                  <SelectItem value="rejeté">Rejeté</SelectItem>
                  <SelectItem value="en_cours">En cours</SelectItem>
                  <SelectItem value="terminé">Terminé</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateDebut">Date de début</Label>
              <DatePicker
                date={filters.dateDebut}
                onDateChange={(date) => setFilters({...filters, dateDebut: date || new Date()})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateFin">Date de fin</Label>
              <DatePicker
                date={filters.dateFin}
                onDateChange={(date) => setFilters({...filters, dateFin: date || new Date()})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">Recherche</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Référence, motif..."
                  value={filters.search}
                  onChange={(e) => setFilters({...filters, search: e.target.value})}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          <div className="flex gap-3 mt-4">
            <Button onClick={applyFilters} className="bg-blue-600 hover:bg-blue-700">
              <Search className="mr-2 h-4 w-4" />
              Appliquer les filtres
            </Button>
            <Button onClick={resetFilters} variant="outline">
              <RefreshCcw className="mr-2 h-4 w-4" />
              Réinitialiser
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Messages d'erreur */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Tableau des transferts */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Liste des transferts
            {filtersApplied && (
              <Badge variant="secondary" className="ml-2">
                Filtres appliqués
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Chargement des transferts...</span>
            </div>
          ) : transferts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ArrowLeftRight className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun transfert trouvé</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Référence</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Destination</TableHead>
                      <TableHead>Service</TableHead>
                      <TableHead>Montant</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transferts.map((transfert) => (
                      <TableRow key={transfert.id}>
                        <TableCell className="font-medium">
                          {transfert.reference}
                        </TableCell>
                        <TableCell>
                          {getTypeBadge(transfert.type)}
                        </TableCell>
                        <TableCell>
                          {transfert.destination || '-'}
                        </TableCell>
                        <TableCell>
                          {transfert.service}
                        </TableCell>
                        <TableCell className="font-semibold">
                          {transfert.montant.toLocaleString()} FCFA
                        </TableCell>
                        <TableCell>
                          {transfert.date_transfert && transfert.date_transfert !== '0000-00-00'
                            ? format(new Date(transfert.date_transfert), "dd/MM/yyyy", { locale: fr })
                            : 'Non définie'
                          }
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(transfert.statut)}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            {transfert.statut === 'en_attente' && (
                              <Button
                                size="sm"
                                onClick={() => confirmTransfert(transfert.id)}
                                disabled={actionInProgress === 'confirm'}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                setSelectedTransfertId(transfert.id);
                                setIsDeleteDialogOpen(true);
                              }}
                              disabled={actionInProgress === 'delete'}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-600">
                  Page {currentPage} sur {totalPages}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog de confirmation de suppression */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer ce transfert ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Annuler</Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={deleteTransfert}
              disabled={actionInProgress === 'delete'}
            >
              {actionInProgress === 'delete' ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Suppression...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Supprimer
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}