<?php
/**
 * Test de l'API des services de transfert
 */

echo "TEST API SERVICES DE TRANSFERT\n";
echo "==============================\n\n";

// Test 1: Test direct de la base de données
echo "1. Test direct de la base de données...\n";
try {
    require_once 'api/config.php';
    
    $stmt = $pdo->prepare("SELECT id, nom, code, description, type, commission_fixe, commission_pourcentage, devise_principale, devises_supportees, actif FROM services_transfert WHERE actif = 1 ORDER BY nom ASC");
    $stmt->execute();
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Requête DB OK - " . count($services) . " services trouvés\n";
    
    if (!empty($services)) {
        echo "   Services disponibles:\n";
        foreach ($services as $service) {
            echo "   - ID: {$service['id']}, Nom: {$service['nom']}, Code: {$service['code']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur DB: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Test de l'API via simulation
echo "2. Test de l'API via simulation...\n";

$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET['action'] = 'services';

ob_start();
try {
    include 'api/transferts.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ API OK - " . count($data['data']) . " services retournés\n";
        
        if (!empty($data['data'])) {
            echo "   Premier service: " . $data['data'][0]['nom'] . "\n";
            echo "   Structure complète du premier service:\n";
            print_r($data['data'][0]);
        }
    } else {
        echo "❌ Erreur API: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse brute: " . substr($output, 0, 500) . "\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception API: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test via HTTP
echo "3. Test via HTTP...\n";
$url = "http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?action=services";
echo "URL: $url\n";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = file_get_contents($url, false, $context);

if ($response !== false) {
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "✅ HTTP OK - " . count($data['data']) . " services\n";
    } else {
        echo "❌ Erreur HTTP: " . ($data['message'] ?? 'Erreur inconnue') . "\n";
        echo "   Réponse: " . substr($response, 0, 300) . "\n";
    }
} else {
    echo "❌ Impossible de contacter l'API via HTTP\n";
    $headers = get_headers($url, 1);
    echo "   Headers: " . print_r($headers, true) . "\n";
}

echo "\n";

// Test 4: Vérification de la structure de la table
echo "4. Vérification de la structure de la table services_transfert...\n";
try {
    $stmt = $pdo->query("DESCRIBE services_transfert");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Structure de la table:\n";
    foreach ($columns as $column) {
        echo "   - {$column['Field']}: {$column['Type']} " . 
             ($column['Null'] === 'YES' ? '(NULL)' : '(NOT NULL)') . 
             ($column['Default'] ? " DEFAULT {$column['Default']}" : '') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur structure: " . $e->getMessage() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Test terminé\n";
?>
