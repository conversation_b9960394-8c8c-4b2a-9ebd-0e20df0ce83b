<?php
/**
 * Test direct de la base de données transferts
 */

require_once 'api/config.php';

echo "TEST DIRECT DE LA BASE DE DONNÉES TRANSFERTS\n";
echo "============================================\n\n";

try {
    // Test de la requête utilisée par l'API
    $query = "SELECT t.*, s.nom as service 
              FROM transferts t 
              LEFT JOIN services_transfert s ON t.service_id = s.id 
              ORDER BY t.created_at DESC LIMIT 10";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "NOMBRE DE TRANSFERTS TROUVÉS: " . count($transferts) . "\n\n";
    
    if (!empty($transferts)) {
        echo "DÉTAILS DES TRANSFERTS:\n";
        echo "=======================\n";
        
        foreach ($transferts as $index => $transfert) {
            echo "\nTRANSFERT " . ($index + 1) . ":\n";
            echo "- ID: " . $transfert['id'] . "\n";
            echo "- Référence: " . $transfert['reference'] . "\n";
            echo "- Montant: " . $transfert['montant'] . "\n";
            echo "- Type: " . ($transfert['type'] ?? 'NULL') . "\n";
            echo "- Destination: " . ($transfert['destination'] ?? 'NULL') . "\n";
            echo "- Service: " . ($transfert['service'] ?? 'NULL') . "\n";
            echo "- Statut: " . ($transfert['statut'] ?? 'NULL') . "\n";
            echo "- Date: " . ($transfert['created_at'] ?? 'NULL') . "\n";
            
            // Test spécifique pour le champ 'type'
            if (is_null($transfert['type'])) {
                echo "⚠️  ATTENTION: Le champ 'type' est NULL!\n";
            } else {
                echo "✅ Champ 'type' OK: " . $transfert['type'] . "\n";
            }
        }
        
        // Formater comme le fait l'API
        echo "\n\nFORMATAGE COMME L'API:\n";
        echo "======================\n";
        
        foreach ($transferts as &$transfert) {
            $transfert['id'] = (int)$transfert['id'];
            $transfert['montant'] = (float)$transfert['montant'];
            
            // S'assurer que le champ 'type' n'est jamais null
            if (empty($transfert['type'])) {
                $transfert['type'] = 'national';
                echo "⚠️  Type corrigé pour transfert ID {$transfert['id']}: 'national'\n";
            }
            
            // S'assurer que le champ 'service' existe
            if (empty($transfert['service'])) {
                $transfert['service'] = 'Service non défini';
                echo "⚠️  Service corrigé pour transfert ID {$transfert['id']}: 'Service non défini'\n";
            }
        }
        
        echo "\nPREMIER TRANSFERT APRÈS FORMATAGE:\n";
        $premier = $transferts[0];
        foreach ($premier as $key => $value) {
            echo "- $key: " . (is_null($value) ? 'NULL' : $value) . " (" . gettype($value) . ")\n";
        }
        
        // Simuler la réponse JSON
        $response = [
            'data' => $transferts,
            'pagination' => [
                'page' => 1,
                'limit' => 10,
                'total' => count($transferts),
                'pages' => 1
            ]
        ];
        
        echo "\nRÉPONSE JSON SIMULÉE:\n";
        echo json_encode($response, JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "❌ Aucun transfert trouvé!\n";
        
        // Créer un transfert de test
        echo "\nCréation d'un transfert de test...\n";
        
        $stmt = $pdo->query("SELECT id FROM services_transfert LIMIT 1");
        $service = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($service) {
            $testRef = 'API_TEST_' . date('YmdHis');
            $insertSQL = "INSERT INTO transferts (reference, montant, type, destination, service_id, date_transfert, motif, statut, agent_id, moulin_id) 
                          VALUES (?, 75000.00, 'international', 'Paris, France', ?, CURDATE(), 'Test API', 'en_attente', 1, 1)";
            $stmt = $pdo->prepare($insertSQL);
            $stmt->execute([$testRef, $service['id']]);
            
            echo "✅ Transfert de test créé: $testRef\n";
            echo "Relancez le script pour voir les résultats.\n";
        }
    }
    
    echo "\n✅ TEST TERMINÉ\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
