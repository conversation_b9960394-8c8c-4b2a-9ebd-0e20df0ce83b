<?php
require_once 'config.php';

echo "=== STRUCTURE DE LA TABLE services_transfert ===\n";

try {
    $stmt = $pdo->query('DESCRIBE services_transfert');
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo sprintf("%-30s %-20s %s\n", 
            $row['Field'], 
            $row['Type'], 
            ($row['Null'] == 'YES' ? 'NULL' : 'NOT NULL')
        );
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}

echo "\n=== DONNÉES EXISTANTES ===\n";
try {
    $stmt = $pdo->query('SELECT id, nom, commission_envoi_fixe, commission_retrait_fixe FROM services_transfert LIMIT 3');
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: {$row['id']}, Nom: {$row['nom']}, Envoi: {$row['commission_envoi_fixe']}, Retrait: {$row['commission_retrait_fixe']}\n";
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
?>
