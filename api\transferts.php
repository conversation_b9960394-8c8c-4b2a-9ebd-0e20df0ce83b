<?php
// EN-TÊTES CORS IMMÉDIATEMENT - AVANT TOUT AUTRE CODE
header("Access-Control-Allow-Origin: http://localhost:8080");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Max-Age: 86400");

// Gérer OPTIONS immédiatement
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

// Inclure CORS APRÈS les en-têtes (pour les fonctions utilitaires)
require_once __DIR__ . '/cors.php';

// Gérer la requête de test de l'API (healthcheck) sans authentification
if (isset($_GET['healthcheck']) && $_GET['healthcheck'] === 'true') {
    cors_json_response(['success' => true, 'message' => 'API transferts connection successful.'], 200);
    exit;
}

// Configuration et services après CORS
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/services/transfer_service.php';
require_once __DIR__ . '/auth.php';

// Bypass authentication for GET requests and special actions (temporary for debugging)
$user_data = null;
$input_data = null;

// Lire les données POST une seule fois si nécessaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $raw_input = file_get_contents('php://input');
    $input_data = json_decode($raw_input, true);
}

// Vérifier si c'est un transfert externe (POST sans action ou avec action=create)
$is_external_transfer = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (empty($_GET['action']) || $_GET['action'] === 'create')) {
    if ($input_data && isset($input_data['type']) && $input_data['type'] === 'externe') {
        $is_external_transfer = true;
    }
}

if (!in_array($_GET['action'] ?? '', ['services', 'quote']) &&
    $_SERVER['REQUEST_METHOD'] !== 'GET' &&
    !$is_external_transfer) {
    // Sécurisation du point d'accès : valider le token pour toutes les opérations sauf exceptions et GET
    $user_data = validateToken($jwt_key);
    if ($user_data === null) {
        cors_json_response(['error' => 'Token invalide ou manquant'], 401);
        exit;
    }
}

// Récupérer la méthode HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Récupérer l'action demandée
$action = $_GET['action'] ?? '';

// Récupérer les services de transfert disponibles depuis la base de données
if ($action === 'services' && $method === 'GET') {
    try {
        $stmt = $pdo->prepare("SELECT id, nom, description, type, commission_fixe, commission_pourcentage, montant_min, montant_max, actif FROM services_transfert WHERE actif = 1 ORDER BY nom ASC");
        $stmt->execute();
        $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

        cors_json_response([
            'success' => true,
            'data' => $services
        ], 200);
    } catch (Exception $e) {
        cors_json_response([
            'success' => false,
            'message' => 'Erreur lors de la récupération des services: ' . $e->getMessage()
        ], 500);
    }
    exit;
}

// Calcul de devis pour un transfert
if ($action === 'quote' && ($method === 'POST' || $method === 'GET')) {
    try {
        // Gérer les données selon la méthode HTTP
        if ($method === 'GET') {
            $data = $_GET;
        } else {
            // Utiliser les données déjà lues ou les lire si pas encore fait
            $data = $input_data ?? json_decode(file_get_contents('php://input'), true);
        }

        $requiredFields = ['montant', 'service', 'devise_source', 'devise_destination'];
        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            cors_json_response(['success' => false, 'message' => 'Données manquantes pour le devis: ' . implode(', ', $missingFields)], 400);
            exit;
        }

        $transferService = new TransferService($pdo);

        $montant = (float)$data['montant'];
        $service_id = $data['service'];
        $devise_source = $data['devise_source'];
        $devise_destination = $data['devise_destination'];

        // Récupérer les informations du service depuis la base de données
        // Chercher par nom ou par ID
        if (is_numeric($service_id)) {
            $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE id = ? AND actif = 1");
            $stmt->execute([$service_id]);
        } else {
            $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE nom = ? AND actif = 1");
            $stmt->execute([$service_id]);
        }
        $service_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service_info) {
            cors_json_response(['success' => false, 'message' => 'Service de transfert non trouvé'], 404);
            exit;
        }

        $service_nom = $service_info['nom'];
        $operation_type = $data['operation_type'] ?? 'envoi'; // Par défaut 'envoi'
        $frais = $transferService->calculateFees($service_nom, $montant, $operation_type);
        $taux_change = $transferService->getExchangeRate($devise_source, $devise_destination);

        $montant_a_recevoir = ($montant - $frais) * $taux_change;

        cors_json_response([
            'success' => true,
            'data' => [
                'montant_source' => $montant,
                'montant_envoye' => $montant,
                'frais' => $frais,
                'taux_change' => $taux_change,
                'devise_source' => $devise_source,
                'devise_destination' => $devise_destination,
                'montant_a_recevoir' => round($montant_a_recevoir, 2),
                'montant_destination' => round($montant_a_recevoir, 2) // Pour compatibilité frontend
            ]
        ], 200);
    } catch (Exception $e) {
        cors_json_response([
            'success' => false,
            'message' => 'Erreur lors du calcul du devis: ' . $e->getMessage()
        ], 500);
    }
    exit;
}

// Gestion des routes selon la méthode HTTP
switch ($method) {
    case 'GET':
        handleGetRequest($user_data ?? null);
        break;
    case 'POST':
        handlePostRequest($user_data ?? null, $input_data);
        break;
    case 'PUT':
        handlePutRequest($user_data ?? null);
        break;
    case 'DELETE':
        handleDeleteRequest($user_data ?? null);
        break;
    default:
        cors_json_response(['error' => 'Méthode non supportée'], 405);
        break;
}

/**
 * Gérer les requêtes GET
 */
function handleGetRequest($user_data) {
    global $pdo;
    
    try {
        // Paramètres de pagination
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(100, max(1, intval($_GET['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;
        
        // Paramètres de filtrage
        $filters = [];
        $params = [];
        
        if (!empty($_GET['type']) && $_GET['type'] !== 'all') {
            $filters[] = "t.type = ?";
            $params[] = $_GET['type'];
        }

        if (!empty($_GET['statut']) && $_GET['statut'] !== 'all') {
            $filters[] = "t.statut = ?";
            $params[] = $_GET['statut'];
        }

        if (!empty($_GET['destination'])) {
            $filters[] = "t.destination LIKE ?";
            $params[] = "%" . $_GET['destination'] . "%";
        }

        if (!empty($_GET['date_debut'])) {
            $filters[] = "t.date_transfert >= ?";
            $params[] = $_GET['date_debut'];
        }

        if (!empty($_GET['date_fin'])) {
            $filters[] = "t.date_transfert <= ?";
            $params[] = $_GET['date_fin'];
        }

        if (!empty($_GET['search']) || !empty($_GET['recherche'])) {
            $searchTerm = $_GET['search'] ?? $_GET['recherche'];
            $filters[] = "(t.reference LIKE ? OR t.motif LIKE ?)";
            $params[] = "%" . $searchTerm . "%";
            $params[] = "%" . $searchTerm . "%";
        }
        
        // Construire la requête
        $whereClause = !empty($filters) ? "WHERE " . implode(" AND ", $filters) : "";

        // Requête pour compter le total (utilise une copie des paramètres)
        $countQuery = "SELECT COUNT(*) as total FROM transferts t $whereClause";
        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute($params);
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Requête pour récupérer les données avec JOIN sur services_transfert
        $query = "SELECT t.*, s.nom as service
                  FROM transferts t
                  LEFT JOIN services_transfert s ON t.service_id = s.id
                  $whereClause
                  ORDER BY t.created_at DESC LIMIT ? OFFSET ?";

        // Ajouter les paramètres LIMIT et OFFSET à une copie des paramètres de filtrage
        $queryParams = array_merge($params, [$limit, $offset]);

        $stmt = $pdo->prepare($query);
        $stmt->execute($queryParams);
        $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Formater les données
        foreach ($transferts as &$transfert) {
            $transfert['id'] = (int)$transfert['id'];
            $transfert['montant'] = (float)$transfert['montant'];

            // S'assurer que le champ 'type' n'est jamais null
            if (empty($transfert['type'])) {
                $transfert['type'] = 'national';
            }

            // S'assurer que le champ 'service' existe
            if (empty($transfert['service'])) {
                $transfert['service'] = 'Service non défini';
            }
        }
        
        cors_json_response([
            'success' => true,
            'data' => $transferts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'total_pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Erreur GET transferts: " . $e->getMessage());
        cors_json_response([
            'success' => false,
            'message' => 'Erreur lors de la récupération des transferts',
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Gérer les requêtes POST (création)
 */
function handlePostRequest($user_data, $data = null) {
    global $pdo;

    try {
        // Utiliser les données déjà lues ou les lire si pas encore fait
        if ($data === null) {
            $data = json_decode(file_get_contents('php://input'), true);
        }

        if (!$data) {
            cors_json_response(['success' => false, 'message' => 'Données JSON invalides'], 400);
            return;
        }

        // Vérifier les données requises pour les transferts externes
        if (isset($data['type']) && $data['type'] === 'externe') {
            $requiredFields = ['montant', 'devise_source', 'type', 'service'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    $missingFields[] = $field;
                }
            }

            // Vérifier les données de l'expéditeur
            if (!isset($data['expediteur']) || !is_array($data['expediteur'])) {
                $missingFields[] = 'expediteur';
            } elseif (!isset($data['expediteur']['nom']) || !isset($data['expediteur']['telephone'])) {
                $missingFields[] = 'expediteur (nom et téléphone requis)';
            }

            // Vérifier les données du destinataire
            if (!isset($data['destinataire']) || !is_array($data['destinataire'])) {
                $missingFields[] = 'destinataire';
            } elseif (!isset($data['destinataire']['nom']) || !isset($data['destinataire']['telephone'])) {
                $missingFields[] = 'destinataire (nom et téléphone requis)';
            }

            if (!empty($missingFields)) {
                cors_json_response(['success' => false, 'message' => 'Données manquantes: ' . implode(', ', $missingFields)], 400);
                return;
            }

            // Générer une référence unique pour le transfert
            $reference = 'TR' . date('YmdHis') . rand(1000, 9999);

            // Calculer les frais et le taux de change
            $transferService = new TransferService();

            // Récupérer les informations du service depuis la base de données
            $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE id = ? AND actif = 1");
            $stmt->execute([$data['service']]);
            $service_info = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$service_info) {
                cors_json_response(['success' => false, 'message' => 'Service de transfert non trouvé'], 404);
                return;
            }

            $service_nom = $service_info['nom'];
            $operation_type = $data['operation_type'] ?? 'envoi'; // Par défaut 'envoi'
            $frais = $transferService->calculateFees($service_nom, $data['montant'], $operation_type);
            $devise_source = $data['devise_source'];
            $devise_destination = $data['devise_destination'] ?? $devise_source;
            $taux_change = $transferService->getExchangeRate($devise_source, $devise_destination);
            $montant_reception = ($data['montant'] - $frais) * $taux_change;

            // Log des informations calculées pour le debugging
            error_log('Transfert à créer - Montant: ' . $data['montant'] . ', Frais: ' . $frais . ', Taux: ' . $taux_change . ', Montant à recevoir: ' . $montant_reception);

            // Insérer le transfert principal avec la structure réelle de la table
            $stmt = $pdo->prepare("
                INSERT INTO transferts (
                    reference, montant, devise, commission, type, statut,
                    service_id, moulin_id, agent_id, date_operation, date_transfert, motif
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), CURDATE(), ?)
            ");

            $stmt->execute([
                $reference,
                $data['montant'],
                $devise_source,
                $frais,
                $data['type'] === 'externe' ? 'international' : 'national', // Adapter le type
                'en_attente',
                $data['service'], // service_id
                1, // moulin_id par défaut (à adapter selon vos besoins)
                1, // agent_id par défaut (à adapter selon vos besoins)
                $data['motif'] ?? 'Transfert externe'
            ]);

            $transfert_id = $pdo->lastInsertId();

            // Insérer les détails du transfert (expéditeur et destinataire)
            $stmt_details = $pdo->prepare("
                INSERT INTO transfert_details (
                    transfert_id, expediteur_nom, expediteur_prenom, expediteur_telephone,
                    destinataire_nom, destinataire_prenom, destinataire_telephone,
                    devise_source, devise_destination, taux_change, montant_a_recevoir
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt_details->execute([
                $transfert_id,
                $data['expediteur']['nom'],
                $data['expediteur']['prenom'] ?? '',
                $data['expediteur']['telephone'],
                $data['destinataire']['nom'],
                $data['destinataire']['prenom'] ?? '',
                $data['destinataire']['telephone'],
                $devise_source,
                $devise_destination,
                $taux_change,
                round($montant_reception, 2)
            ]);

            cors_json_response([
                'success' => true,
                'message' => 'Transfert externe créé avec succès',
                'data' => [
                    'id' => (int)$transfert_id,
                    'reference' => $reference,
                    'montant' => $data['montant'],
                    'frais' => $frais,
                    'taux_change' => $taux_change,
                    'montant_a_recevoir' => round($montant_reception, 2)
                ]
            ], 201);

        } else {
            // Gestion des autres types de transferts (existant)
            $required = ['montant', 'type', 'service', 'motif'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    cors_json_response(['error' => "Le champ $field est requis"], 400);
                    return;
                }
            }

            // Générer une référence unique
            $reference = 'TR' . date('YmdHis') . rand(1000, 9999);

            // Insérer le transfert
            $query = "INSERT INTO transferts (montant, type, destination, service, date_transfert, motif, statut, reference, created_at)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $stmt = $pdo->prepare($query);
            $stmt->execute([
                $data['montant'],
                $data['type'],
                $data['destination'] ?? null,
                $data['service'],
                $data['date_transfert'] ?? date('Y-m-d'),
                $data['motif'],
                'en_attente',
                $reference
            ]);

            $transfert_id = $pdo->lastInsertId();

            cors_json_response([
                'success' => true,
                'message' => 'Transfert créé avec succès',
                'transfert_id' => (int)$transfert_id,
                'reference' => $reference
            ], 201);
        }

    } catch (Exception $e) {
        error_log("Erreur POST transferts: " . $e->getMessage());
        cors_json_response(['success' => false, 'message' => 'Erreur lors de la création du transfert: ' . $e->getMessage()], 500);
    }
}

/**
 * Gérer les requêtes PUT (mise à jour)
 */
function handlePutRequest($user_data) {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['id'])) {
            cors_json_response(['error' => 'ID du transfert requis'], 400);
            return;
        }
        
        $transfert_id = (int)$input['id'];
        
        // Construire la requête de mise à jour
        $updates = [];
        $params = [];
        
        $allowed_fields = ['montant', 'type', 'destination', 'service', 'date_transfert', 'motif', 'statut'];
        
        foreach ($allowed_fields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (empty($updates)) {
            cors_json_response(['error' => 'Aucune donnée à mettre à jour'], 400);
            return;
        }
        
        $params[] = $transfert_id;
        
        $query = "UPDATE transferts SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            cors_json_response(['success' => true, 'message' => 'Transfert mis à jour avec succès']);
        } else {
            cors_json_response(['error' => 'Transfert non trouvé'], 404);
        }
        
    } catch (Exception $e) {
        error_log("Erreur PUT transferts: " . $e->getMessage());
        cors_json_response(['error' => 'Erreur lors de la mise à jour du transfert'], 500);
    }
}

/**
 * Gérer les requêtes DELETE
 */
function handleDeleteRequest($user_data) {
    global $pdo;
    
    try {
        $transfert_id = (int)($_GET['id'] ?? 0);
        
        if ($transfert_id <= 0) {
            cors_json_response(['error' => 'ID du transfert invalide'], 400);
            return;
        }
        
        $query = "DELETE FROM transferts WHERE id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$transfert_id]);
        
        if ($stmt->rowCount() > 0) {
            cors_json_response(['success' => true, 'message' => 'Transfert supprimé avec succès']);
        } else {
            cors_json_response(['error' => 'Transfert non trouvé'], 404);
        }
        
    } catch (Exception $e) {
        error_log("Erreur DELETE transferts: " . $e->getMessage());
        cors_json_response(['error' => 'Erreur lors de la suppression du transfert'], 500);
    }
}

?>