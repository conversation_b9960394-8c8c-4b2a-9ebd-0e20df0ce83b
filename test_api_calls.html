<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Calls</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Test des appels API Manager</h1>
    
    <div>
        <button onclick="testApiConfig()">Test Configuration API</button>
        <button onclick="testMoulinsAPI()">Test API Moulins</button>
        <button onclick="testTicketsAPI()">Test API Tickets</button>
        <button onclick="testVersementsAPI()">Test API Versements</button>
        <button onclick="clearResults()">Effacer</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testApiConfig() {
            addResult('<h3>Configuration API</h3>');
            
            // Simuler la configuration de l'app
            const getApiBaseUrl = () => {
                if (window.location.port === '8080') {
                    return '/api';
                }
                return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            };
            
            const API_BASE_URL = getApiBaseUrl();
            
            addResult(`
                <strong>Port actuel:</strong> ${window.location.port}<br>
                <strong>API Base URL:</strong> ${API_BASE_URL}<br>
                <strong>URL complète moulins:</strong> ${API_BASE_URL}/moulins.php?manager_id=2
            `, 'info');
        }

        async function testMoulinsAPI() {
            addResult('<h3>Test API Moulins</h3>');
            
            const API_BASE_URL = window.location.port === '8080' ? '/api' : 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            const url = `${API_BASE_URL}/moulins.php?manager_id=2`;
            
            try {
                addResult(`Appel vers: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                addResult(`Status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`<strong>Réponse:</strong><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                    
                    if (data.success && data.data && data.data.length > 0) {
                        addResult(`✅ ${data.data.length} moulins trouvés`, 'success');
                    } else {
                        addResult(`⚠️ Aucun moulin trouvé ou format de réponse incorrect`, 'error');
                    }
                } else {
                    const errorText = await response.text();
                    addResult(`<strong>Erreur:</strong><pre>${errorText}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur de connexion: ${error.message}`, 'error');
            }
        }

        async function testTicketsAPI() {
            addResult('<h3>Test API Tickets</h3>');
            
            const API_BASE_URL = window.location.port === '8080' ? '/api' : 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            const url = `${API_BASE_URL}/tickets.php?manager_id=2`;
            
            try {
                addResult(`Appel vers: ${url}`, 'info');
                
                const response = await fetch(url);
                addResult(`Status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`<strong>Réponse:</strong><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`<strong>Erreur:</strong><pre>${errorText}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur de connexion: ${error.message}`, 'error');
            }
        }

        async function testVersementsAPI() {
            addResult('<h3>Test API Versements</h3>');
            
            const API_BASE_URL = window.location.port === '8080' ? '/api' : 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            const url = `${API_BASE_URL}/versements.php?user_role=manager&user_id=2`;
            
            try {
                addResult(`Appel vers: ${url}`, 'info');
                
                const response = await fetch(url);
                addResult(`Status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`<strong>Réponse:</strong><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`<strong>Erreur:</strong><pre>${errorText}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur de connexion: ${error.message}`, 'error');
            }
        }

        // Test automatique au chargement
        window.onload = function() {
            testApiConfig();
        };
    </script>
</body>
</html>
