<?php
require_once 'api/config.php';

echo "CONFIGURATION DES SERVICES DE TRANSFERT\n";
echo "=======================================\n\n";

try {
    // 1. Vérifier si la table services_transfert existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'services_transfert'");
    $serviceTableExists = $stmt->rowCount() > 0;
    
    if (!$serviceTableExists) {
        echo "Création de la table services_transfert...\n";
        $createServiceSQL = "CREATE TABLE services_transfert (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(20) UNIQUE NOT NULL,
            nom VARCHAR(100) NOT NULL,
            type ENUM('national', 'international') DEFAULT 'national',
            description TEXT,
            commission_fixe DECIMAL(10,2) DEFAULT 0.00,
            commission_pourcentage DECIMAL(5,2) DEFAULT 0.00,
            actif BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($createServiceSQL);
        echo "✅ Table services_transfert créée\n";
    } else {
        echo "✅ Table services_transfert existe\n";
    }
    
    // 2. Vérifier les services existants
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM services_transfert");
    $serviceCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($serviceCount == 0) {
        echo "\nInsertion des services de transfert...\n";
        
        $services = [
            ['ORANGE', 'Orange Money', 'national', 'Service de transfert Orange Money'],
            ['MTN', 'MTN Mobile Money', 'national', 'Service de transfert MTN'],
            ['WAVE', 'Wave', 'national', 'Service de transfert Wave'],
            ['MOOV', 'Moov Money', 'national', 'Service de transfert Moov'],
            ['WESTERN', 'Western Union', 'international', 'Service de transfert international Western Union'],
            ['WARI', 'Wari', 'international', 'Service de transfert international Wari'],
            ['MONEYGRAM', 'MoneyGram', 'international', 'Service de transfert international MoneyGram']
        ];
        
        $insertServiceSQL = "INSERT INTO services_transfert (code, nom, type, description) VALUES (?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertServiceSQL);
        
        foreach ($services as $service) {
            $stmt->execute($service);
            echo "✅ Service ajouté: {$service[1]}\n";
        }
    } else {
        echo "✅ $serviceCount services existent déjà\n";
    }
    
    // 3. Vérifier la structure de la table transferts
    echo "\nVérification de la table transferts...\n";
    $stmt = $pdo->query('DESCRIBE transferts');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasServiceId = false;
    $hasService = false;
    
    foreach($columns as $col) {
        if ($col['Field'] === 'service_id') $hasServiceId = true;
        if ($col['Field'] === 'service') $hasService = true;
    }
    
    echo "- service_id: " . ($hasServiceId ? "✅" : "❌") . "\n";
    echo "- service: " . ($hasService ? "✅" : "❌") . "\n";
    
    // 4. Insérer un transfert de test avec service_id
    echo "\nInsertion d'un transfert de test...\n";
    
    // Récupérer un service existant
    $stmt = $pdo->query("SELECT id FROM services_transfert LIMIT 1");
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($service) {
        $testRef = 'TEST_' . date('YmdHis');
        
        if ($hasServiceId) {
            $insertSQL = "INSERT INTO transferts (reference, montant, type, destination, service_id, date_transfert, motif, statut, agent_id, moulin_id) 
                          VALUES (?, 50000.00, 'national', 'Abidjan', ?, CURDATE(), 'Test transfert', 'en_attente', 1, 1)";
            $stmt = $pdo->prepare($insertSQL);
            $stmt->execute([$testRef, $service['id']]);
        } else {
            $insertSQL = "INSERT INTO transferts (reference, montant, type, destination, service, date_transfert, motif, statut) 
                          VALUES (?, 50000.00, 'national', 'Abidjan', 'Orange Money', CURDATE(), 'Test transfert', 'en_attente')";
            $stmt = $pdo->prepare($insertSQL);
            $stmt->execute([$testRef]);
        }
        
        echo "✅ Transfert de test inséré: $testRef\n";
    }
    
    // 5. Vérifier le contenu final
    echo "\nCONTENU FINAL:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM services_transfert");
    $serviceCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Services: $serviceCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM transferts");
    $transfertCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Transferts: $transfertCount\n";
    
    if ($transfertCount > 0) {
        echo "\nExemples de transferts:\n";
        if ($hasServiceId) {
            $stmt = $pdo->query("SELECT t.id, t.reference, t.montant, t.type, t.destination, s.nom as service_nom, t.statut 
                                FROM transferts t 
                                LEFT JOIN services_transfert s ON t.service_id = s.id 
                                ORDER BY t.id DESC LIMIT 3");
        } else {
            $stmt = $pdo->query("SELECT id, reference, montant, type, destination, service, statut FROM transferts ORDER BY id DESC LIMIT 3");
        }
        
        $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($transferts as $t) {
            $serviceName = $hasServiceId ? $t['service_nom'] : $t['service'];
            echo "- ID: {$t['id']}, Ref: {$t['reference']}, Type: {$t['type']}, Service: $serviceName, Statut: {$t['statut']}\n";
        }
    }
    
    echo "\n✅ CONFIGURATION TERMINÉE!\n";
    echo "\n🎯 RÉSULTAT:\n";
    echo "- Table services_transfert configurée avec des services\n";
    echo "- Table transferts prête avec données de test\n";
    echo "- Plus d'erreur de contrainte de clé étrangère\n";
    echo "- L'erreur JavaScript 'Cannot read properties of null (reading 'type')' devrait être résolue\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
