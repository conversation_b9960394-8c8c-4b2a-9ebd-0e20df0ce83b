<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Proxy Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; white-space: pre-wrap; }
        .url-test { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Proxy et API Transferts</h1>
        
        <div class="test-section">
            <h3>📋 Configuration détectée</h3>
            <div id="config-info"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Tests d'URL</h3>
            <button onclick="testAllUrls()">🔍 Tester toutes les URLs</button>
            <button onclick="testQuoteAPI()">📊 Test Quote API</button>
            <button onclick="testServicesAPI()">📋 Test Services API</button>
            <button onclick="clearResults()">🗑️ Effacer</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Configuration comme dans l'app
        const getApiBaseUrl = () => {
            if (typeof window === 'undefined') {
                return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            }
            
            if (window.location.port === '8080') {
                return '/api';
            }
            
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return 'http://localhost/Gestion_moulin_wifiZone_ok/api';
            }
            
            const { protocol, hostname } = window.location;
            return `${protocol}//${hostname}/Gestion_moulin_wifiZone_ok/api`;
        };

        const API_BASE_URL = getApiBaseUrl();

        // Afficher la configuration
        document.getElementById('config-info').innerHTML = `
            <div class="url-test">
                <strong>Port actuel:</strong> ${window.location.port}<br>
                <strong>Hostname:</strong> ${window.location.hostname}<br>
                <strong>API_BASE_URL:</strong> ${API_BASE_URL}<br>
                <strong>URL complète:</strong> ${window.location.href}
            </div>
        `;

        function addResult(title, content, type = 'success') {
            const resultsDiv = document.getElementById('results');
            const section = document.createElement('div');
            section.className = `test-section ${type}`;
            section.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>Timestamp: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(section);
        }

        async function testUrl(url, description) {
            try {
                console.log(`Testing: ${url}`);
                const startTime = Date.now();
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                let data;
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    data = await response.text();
                }
                
                const result = {
                    url,
                    status: response.status,
                    statusText: response.statusText,
                    responseTime: `${responseTime}ms`,
                    contentType,
                    data: typeof data === 'string' ? data.substring(0, 500) + (data.length > 500 ? '...' : '') : data
                };
                
                addResult(
                    `${response.ok ? '✅' : '❌'} ${description}`,
                    JSON.stringify(result, null, 2),
                    response.ok ? 'success' : 'error'
                );
                
                return { success: response.ok, result };
            } catch (error) {
                addResult(
                    `❌ ${description} - ERREUR`,
                    `URL: ${url}\nErreur: ${error.message}\nStack: ${error.stack}`,
                    'error'
                );
                return { success: false, error: error.message };
            }
        }

        async function testAllUrls() {
            addResult('🚀 Début des tests d\'URL', 'Test de toutes les URLs importantes...');
            
            const urls = [
                // Tests de base
                { url: 'http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?healthcheck=true', desc: 'Healthcheck Direct' },
                { url: '/api/transferts.php?healthcheck=true', desc: 'Healthcheck via Proxy' },
                
                // Tests des services
                { url: 'http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?action=services', desc: 'Services Direct' },
                { url: '/api/transferts.php?action=services', desc: 'Services via Proxy' },
                
                // Tests de quote
                { url: 'http://localhost/Gestion_moulin_wifiZone_ok/api/transferts.php?action=quote&montant=50000&service=1&devise_source=XOF&devise_destination=XOF', desc: 'Quote Direct' },
                { url: '/api/transferts.php?action=quote&montant=50000&service=1&devise_source=XOF&devise_destination=XOF', desc: 'Quote via Proxy' },
            ];
            
            for (const { url, desc } of urls) {
                await testUrl(url, desc);
                // Petite pause entre les tests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            addResult('✅ Tests terminés', 'Tous les tests d\'URL ont été exécutés.');
        }

        async function testQuoteAPI() {
            const params = new URLSearchParams({
                action: 'quote',
                montant: '50000',
                service: '1',
                devise_source: 'XOF',
                devise_destination: 'XOF',
                operation_type: 'envoi'
            });

            const url = `${API_BASE_URL}/transferts.php?${params.toString()}`;
            await testUrl(url, 'Quote API avec configuration actuelle');
        }

        async function testServicesAPI() {
            const url = `${API_BASE_URL}/transferts.php?action=services`;
            await testUrl(url, 'Services API avec configuration actuelle');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Test automatique au chargement
        window.onload = function() {
            addResult('🚀 Page chargée', `Configuration API initialisée avec: ${API_BASE_URL}`);
        };
    </script>
</body>
</html>
