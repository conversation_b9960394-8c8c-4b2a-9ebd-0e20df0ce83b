<?php
require_once 'api/config.php';

echo "DEBUG TABLE TRANSFERTS\n";
echo "======================\n\n";

try {
    // 1. Vérifier si la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'transferts'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "❌ La table 'transferts' n'existe pas!\n";
        echo "Création de la table...\n";
        
        $createSQL = "CREATE TABLE transferts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            reference VARCHAR(50) UNIQUE NOT NULL,
            montant DECIMAL(10,2) NOT NULL,
            type ENUM('national', 'international') DEFAULT 'national',
            destination VARCHAR(255),
            service VARCHAR(100) NOT NULL,
            date_transfert DATE,
            motif TEXT,
            statut ENUM('en_attente', 'valide', 'rejete', 'annule', 'termine') DEFAULT 'en_attente',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($createSQL);
        echo "✅ Table 'transferts' créée\n";
    } else {
        echo "✅ La table 'transferts' existe\n";
    }
    
    // 2. Vérifier la structure
    echo "\nSTRUCTURE DE LA TABLE:\n";
    $stmt = $pdo->query('DESCRIBE transferts');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach($columns as $col) {
        echo "- {$col['Field']}: {$col['Type']} (Null: {$col['Null']}, Default: {$col['Default']})\n";
    }
    
    // 3. Insérer un transfert de test simple
    echo "\nINSERTION D'UN TRANSFERT DE TEST:\n";
    
    $testRef = 'TEST_' . date('YmdHis');
    $insertSQL = "INSERT INTO transferts (reference, montant, type, destination, service, date_transfert, motif, statut) 
                  VALUES (?, 50000.00, 'national', 'Abidjan', 'Orange Money', CURDATE(), 'Test transfert', 'en_attente')";
    
    $stmt = $pdo->prepare($insertSQL);
    $stmt->execute([$testRef]);
    
    echo "✅ Transfert de test inséré: $testRef\n";
    
    // 4. Vérifier le contenu
    echo "\nCONTENU DE LA TABLE:\n";
    $stmt = $pdo->query('SELECT id, reference, montant, type, destination, service, statut FROM transferts ORDER BY id DESC LIMIT 5');
    $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($transferts as $t) {
        echo "- ID: {$t['id']}, Ref: {$t['reference']}, Type: {$t['type']}, Montant: {$t['montant']}, Statut: {$t['statut']}\n";
    }
    
    echo "\n✅ DEBUG TERMINÉ - La table est prête!\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
