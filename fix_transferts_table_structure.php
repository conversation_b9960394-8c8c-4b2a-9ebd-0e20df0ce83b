<?php
/**
 * Script pour corriger la structure de la table transferts
 * et s'assurer qu'elle correspond aux attentes du frontend
 */

require_once 'api/config.php';

echo "🔧 CORRECTION DE LA STRUCTURE DE LA TABLE TRANSFERTS\n";
echo "==================================================\n\n";

try {
    // 1. Vérifier la structure actuelle de la table
    echo "1. VÉRIFICATION DE LA STRUCTURE ACTUELLE:\n";
    $stmt = $pdo->query("DESCRIBE transferts");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Colonnes actuelles:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} ({$column['Null']}, {$column['Default']})\n";
    }
    
    // 2. Vérifier si la colonne type existe et ses valeurs
    $hasTypeColumn = false;
    $typeColumnType = '';
    foreach ($columns as $column) {
        if ($column['Field'] === 'type') {
            $hasTypeColumn = true;
            $typeColumnType = $column['Type'];
            break;
        }
    }
    
    echo "\n2. ANALYSE DE LA COLONNE TYPE:\n";
    if ($hasTypeColumn) {
        echo "✅ Colonne 'type' trouvée: $typeColumnType\n";
        
        // Vérifier les valeurs actuelles
        $stmt = $pdo->query("SELECT DISTINCT type FROM transferts WHERE type IS NOT NULL");
        $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "Valeurs actuelles: " . implode(', ', $types) . "\n";
        
        // Vérifier si on a des valeurs null
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM transferts WHERE type IS NULL");
        $nullCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Enregistrements avec type NULL: $nullCount\n";
        
    } else {
        echo "❌ Colonne 'type' non trouvée\n";
    }
    
    // 3. Corriger la structure si nécessaire
    echo "\n3. CORRECTION DE LA STRUCTURE:\n";
    
    if (!$hasTypeColumn) {
        echo "Ajout de la colonne 'type'...\n";
        $pdo->exec("ALTER TABLE transferts ADD COLUMN type ENUM('national', 'international') DEFAULT 'national'");
        echo "✅ Colonne 'type' ajoutée\n";
    } else if (!strpos($typeColumnType, 'national') || !strpos($typeColumnType, 'international')) {
        echo "Modification de la colonne 'type' pour supporter 'national' et 'international'...\n";
        
        // Sauvegarder les anciennes valeurs
        $pdo->exec("ALTER TABLE transferts ADD COLUMN type_backup VARCHAR(50)");
        $pdo->exec("UPDATE transferts SET type_backup = type");
        
        // Modifier la colonne
        $pdo->exec("ALTER TABLE transferts MODIFY COLUMN type ENUM('national', 'international') DEFAULT 'national'");
        
        // Mapper les anciennes valeurs vers les nouvelles
        $pdo->exec("UPDATE transferts SET type = 'national' WHERE type_backup IN ('envoi', 'depot', 'transfert')");
        $pdo->exec("UPDATE transferts SET type = 'international' WHERE type_backup = 'reception'");
        $pdo->exec("UPDATE transferts SET type = 'national' WHERE type IS NULL OR type = ''");
        
        // Supprimer la colonne de sauvegarde
        $pdo->exec("ALTER TABLE transferts DROP COLUMN type_backup");
        
        echo "✅ Colonne 'type' modifiée\n";
    }
    
    // 4. S'assurer que toutes les colonnes nécessaires existent
    echo "\n4. VÉRIFICATION DES COLONNES REQUISES:\n";
    $requiredColumns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'montant' => 'DECIMAL(10,2) NOT NULL',
        'type' => "ENUM('national', 'international') DEFAULT 'national'",
        'destination' => 'VARCHAR(255)',
        'service' => 'VARCHAR(100) NOT NULL',
        'date_transfert' => 'DATE',
        'motif' => 'TEXT',
        'statut' => "ENUM('en_attente', 'valide', 'rejete', 'annule', 'termine') DEFAULT 'en_attente'",
        'reference' => 'VARCHAR(50) UNIQUE NOT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    ];
    
    $currentColumns = array_column($columns, 'Field');
    
    foreach ($requiredColumns as $columnName => $columnDef) {
        if (!in_array($columnName, $currentColumns)) {
            echo "Ajout de la colonne '$columnName'...\n";
            $pdo->exec("ALTER TABLE transferts ADD COLUMN $columnName $columnDef");
            echo "✅ Colonne '$columnName' ajoutée\n";
        } else {
            echo "✅ Colonne '$columnName' existe\n";
        }
    }
    
    // 5. Mettre à jour les enregistrements avec type NULL
    echo "\n5. MISE À JOUR DES ENREGISTREMENTS:\n";
    $stmt = $pdo->prepare("UPDATE transferts SET type = 'national' WHERE type IS NULL OR type = ''");
    $stmt->execute();
    $updated = $stmt->rowCount();
    echo "✅ $updated enregistrements mis à jour avec type = 'national'\n";
    
    // 6. Vérification finale
    echo "\n6. VÉRIFICATION FINALE:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM transferts");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "Total des transferts: $total\n";
    
    $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM transferts GROUP BY type");
    $typeCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Répartition par type:\n";
    foreach ($typeCounts as $typeCount) {
        echo "- {$typeCount['type']}: {$typeCount['count']}\n";
    }
    
    echo "\n✅ CORRECTION TERMINÉE AVEC SUCCÈS!\n";
    echo "\n🎯 RÉSULTAT:\n";
    echo "- Structure de la table corrigée\n";
    echo "- Colonne 'type' configurée avec ENUM('national', 'international')\n";
    echo "- Tous les enregistrements ont une valeur 'type' valide\n";
    echo "- Plus d'erreur 'Cannot read properties of null (reading 'type')'\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n" . date('Y-m-d H:i:s') . " - Script terminé\n";
?>
