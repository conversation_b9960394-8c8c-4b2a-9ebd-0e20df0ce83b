<?php
// Test API sans authentification
header("Access-Control-Allow-Origin: http://localhost:8080");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

require_once __DIR__ . "/config.php";

try {
    $page = max(1, intval($_GET["page"] ?? 1));
    $limit = min(100, max(1, intval($_GET["limit"] ?? 10)));
    $offset = ($page - 1) * $limit;
    
    $query = "SELECT t.*, s.nom as service 
              FROM transferts t 
              LEFT JOIN services_transfert s ON t.service_id = s.id 
              ORDER BY t.created_at DESC LIMIT ? OFFSET ?";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$limit, $offset]);
    $transferts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $countQuery = "SELECT COUNT(*) as total FROM transferts";
    $countStmt = $pdo->prepare($countQuery);
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)["total"];
    
    foreach ($transferts as &$transfert) {
        $transfert["id"] = (int)$transfert["id"];
        $transfert["montant"] = (float)$transfert["montant"];
        
        if (empty($transfert["type"])) {
            $transfert["type"] = "national";
        }
        
        if (empty($transfert["service"])) {
            $transfert["service"] = "Service non défini";
        }
    }
    
    echo json_encode([
        "data" => $transferts,
        "pagination" => [
            "page" => $page,
            "limit" => $limit,
            "total" => (int)$total,
            "pages" => ceil($total / $limit)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => "Erreur serveur: " . $e->getMessage()]);
}
?>