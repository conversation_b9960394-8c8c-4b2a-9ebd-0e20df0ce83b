<?php
// Headers CORS
header("Access-Control-Allow-Origin: http://localhost:8080");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

// Simuler exactement ce que fait l'API transferts.php pour les devis
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/services/transfer_service.php';
require_once __DIR__ . '/cors.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

echo json_encode([
    'debug' => [
        'method' => $method,
        'action' => $action,
        'get_params' => $_GET,
        'post_data' => $method === 'POST' ? json_decode(file_get_contents('php://input'), true) : null,
        'timestamp' => date('Y-m-d H:i:s')
    ]
]);

if ($action === 'quote' && ($method === 'POST' || $method === 'GET')) {
    try {
        // Gérer les données selon la méthode HTTP
        if ($method === 'GET') {
            $data = $_GET;
        } else {
            $data = json_decode(file_get_contents('php://input'), true);
        }

        $requiredFields = ['montant', 'service', 'devise_source', 'devise_destination'];
        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            cors_json_response(['success' => false, 'message' => 'Données manquantes pour le devis: ' . implode(', ', $missingFields)], 400);
            exit;
        }

        $transferService = new TransferService($pdo);

        $montant = (float)$data['montant'];
        $service_id = $data['service'];
        $devise_source = $data['devise_source'];
        $devise_destination = $data['devise_destination'];

        // Récupérer les informations du service depuis la base de données
        if (is_numeric($service_id)) {
            $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE id = ? AND actif = 1");
            $stmt->execute([$service_id]);
        } else {
            $stmt = $pdo->prepare("SELECT nom FROM services_transfert WHERE nom = ? AND actif = 1");
            $stmt->execute([$service_id]);
        }
        $service_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service_info) {
            cors_json_response(['success' => false, 'message' => 'Service de transfert non trouvé: ' . $service_id], 404);
            exit;
        }

        $service_nom = $service_info['nom'];
        $operation_type = $data['operation_type'] ?? 'envoi';
        $frais = $transferService->calculateFees($service_nom, $montant, $operation_type);
        $taux_change = $transferService->getExchangeRate($devise_source, $devise_destination);

        $montant_a_recevoir = ($montant - $frais) * $taux_change;

        cors_json_response([
            'success' => true,
            'data' => [
                'montant_source' => $montant,
                'montant_envoye' => $montant,
                'frais' => $frais,
                'taux_change' => $taux_change,
                'devise_source' => $devise_source,
                'devise_destination' => $devise_destination,
                'montant_a_recevoir' => round($montant_a_recevoir, 2),
                'montant_destination' => round($montant_a_recevoir, 2)
            ]
        ], 200);
    } catch (Exception $e) {
        cors_json_response([
            'success' => false,
            'message' => 'Erreur lors du calcul du devis: ' . $e->getMessage()
        ], 500);
    }
    exit;
}

cors_json_response(['success' => false, 'message' => 'Action non supportée'], 400);
?>
