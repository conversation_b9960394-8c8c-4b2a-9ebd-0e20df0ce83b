<?php
/**
 * Test de connexion admin pour obtenir un token
 */

echo "=== TEST CONNEXION ADMIN ===\n";

// Test de connexion avec les identifiants admin
$login_url = 'http://localhost:8080/api/login.php';

$credentials = [
    'username' => 'admin',
    'password' => 'admin123' // Vous devrez peut-être ajuster le mot de passe
];

echo "1. Tentative de connexion avec username: admin\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($credentials));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "   Code HTTP: $http_code\n";
if ($error) {
    echo "   Erreur cURL: $error\n";
}

echo "   Réponse: $response\n";

if ($http_code === 200) {
    $data = json_decode($response, true);
    if (isset($data['token'])) {
        echo "   ✅ TOKEN OBTENU!\n";
        $token = $data['token'];
        echo "   Token: " . substr($token, 0, 30) . "...\n";
        
        // Tester le token avec l'API admin
        echo "\n2. Test du token avec l'API admin:\n";
        
        $admin_url = 'http://localhost:8080/api/admin_transferts.php?action=services';
        
        $ch2 = curl_init();
        curl_setopt($ch2, CURLOPT_URL, $admin_url);
        curl_setopt($ch2, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
        
        $admin_response = curl_exec($ch2);
        $admin_http_code = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
        curl_close($ch2);
        
        echo "   Code HTTP: $admin_http_code\n";
        echo "   Réponse: " . substr($admin_response, 0, 200) . "...\n";
        
        if ($admin_http_code === 200) {
            echo "   ✅ ACCÈS ADMIN AUTORISÉ!\n";
            
            // Tester une requête PUT
            echo "\n3. Test d'une requête PUT:\n";
            
            $put_url = 'http://localhost:8080/api/admin_transferts.php?id=2';
            $put_data = [
                'nom' => 'FLOOZ Test Update',
                'commission_envoi_fixe' => 350,
                'commission_retrait_fixe' => 280
            ];
            
            $ch3 = curl_init();
            curl_setopt($ch3, CURLOPT_URL, $put_url);
            curl_setopt($ch3, CURLOPT_CUSTOMREQUEST, 'PUT');
            curl_setopt($ch3, CURLOPT_POSTFIELDS, json_encode($put_data));
            curl_setopt($ch3, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
            
            $put_response = curl_exec($ch3);
            $put_http_code = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
            curl_close($ch3);
            
            echo "   Code HTTP: $put_http_code\n";
            echo "   Réponse: $put_response\n";
            
            if ($put_http_code === 200) {
                echo "   ✅ MODIFICATION RÉUSSIE!\n";
            } else {
                echo "   ❌ MODIFICATION ÉCHOUÉE\n";
            }
            
        } else {
            echo "   ❌ ACCÈS ADMIN REFUSÉ\n";
        }
        
    } else {
        echo "   ❌ Pas de token dans la réponse\n";
        if (isset($data['message'])) {
            echo "   Message: {$data['message']}\n";
        }
    }
} else {
    echo "   ❌ Échec de connexion\n";
}

// Essayer avec d'autres mots de passe possibles
$other_passwords = ['admin', '123456', 'password', 'admin123'];

foreach ($other_passwords as $pwd) {
    if ($pwd === 'admin123') continue; // Déjà testé
    
    echo "\n4. Test avec mot de passe: $pwd\n";
    
    $creds = ['username' => 'admin', 'password' => $pwd];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($creds));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "   Code HTTP: $http_code\n";
    
    if ($http_code === 200) {
        $data = json_decode($response, true);
        if (isset($data['token'])) {
            echo "   ✅ CONNEXION RÉUSSIE avec mot de passe: $pwd\n";
            break;
        }
    }
}

echo "\n=== FIN TEST ===\n";
?>
